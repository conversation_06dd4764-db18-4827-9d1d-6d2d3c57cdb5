<?php

ini_set('magic_quotes_sybase', 0);

// FIX
define('QUOTES_ENABLED', false);


ini_set('session.bug_compat_42', 1);
ini_set('session.bug_compat_warn', 0);

function get_selected_timezone()
{

    //Default Turkey
    if (isset($_SESSION['selected_timezone']))
        return $_SESSION['selected_timezone'];
    else
        return 'Europe/Istanbul';
}

$SELECTED_TIMEZONE = get_selected_timezone();
date_default_timezone_set($SELECTED_TIMEZONE);
define('PS', PATH_SEPARATOR);

//
// Strip tags from any type variable
//
function fn_strip_tags(&$var)
{
    global $trusted_variables;

    if (!is_array($var)) {
        return (strip_tags($var));
    } else {
        $stripped = array();
        foreach ($var as $k => $v) {
            $sk = strip_tags($k);
            if (!is_array($v)) {
                $sv = strip_tags($v);
            } else {
                $sv = fn_strip_tags($v);
            }
            $stripped[$sk] = $sv;
        }
        return ($stripped);
    }
}

//
// Add slashes to any type variable
//
function fn_add_slashes($var, $escape_nls = false)
{

    if (!is_array($var)) {
        return ($escape_nls == true) ? str_replace("\n", "\\n", addslashes($var)) : addslashes($var);
    } else {
        $slashed = array();
        foreach ($var as $k => $v) {
            $sk = addslashes($k);
            if (!is_array($v)) {
                $sv = ($escape_nls == true) ? str_replace("\n", "\\n", addslashes($v)) : addslashes($v);
            } else {
                $sv = fn_add_slashes($v, $escape_nls);
            }
            $slashed[$sk] = $sv;
        }
        return ($slashed);
    }
}

//
// Extract superglobals to global scope
// Warning! The order of variables should be by importance (get, post, cookie, etc...)
function fn_process_superglobals()
{

    foreach (func_get_args() as $var) {
        if (isset($var)) {
            foreach ($var as $k => $v) {
                $var = fn_strip_tags($v);
                if (!defined('QUOTES_ENABLED')) {
                    $var = fn_add_slashes($var);
                }
                $GLOBALS[$k] = $var;
            }
        }
    }
}


//
// Register SERVER variables to the global scope
//
if (defined('CONSOLE')) { // Script is running from the server
    $_SERVER['SERVER_SOFTWARE'] = 'EVR';
    $_SERVER['REMOTE_ADDR'] = @$_SERVER['HOSTNAME'];
    @set_time_limit(0); // the script, running in console mode has no time limits
}

fn_process_superglobals($_GET, $_POST, $_COOKIE, $_SERVER);


if (!empty($QUERY_STRING)) {
    $QUERY_STRING = (defined('QUOTES_ENABLED')) ? stripslashes($QUERY_STRING) : $QUERY_STRING;
    $QUERY_STRING = str_replace(array('"', "'"), array('', ''), $QUERY_STRING);
    $query_string = $QUERY_STRING;
}

define('DIR_ROOT', realpath(dirname(__FILE__)));


// Require configuration
require_once(DIR_ROOT . '/inc/config.php');

//Require ezSQL
require_once CORE_DIR . "/ez_sql_core.php";
//Require ezSQL MYSQL
require_once CORE_DIR . "/ez_sql_mysqli.php";

// ezSQL start
$db = new ezSQL_mysqli($config['db_user'], $config['db_password'], $config['db_name'], $config['db_host'], 'utf8');

if (!$db) {
    die('database bağlanılamadı.');
}
$db->query("SET NAMES 'utf8'");

// if(empty($_SESSION['lang'])) {$_SESSION['lang']='en';}

$_SESSION['lang'] = 'en';

//General options
$_settings = $db->get_results("SELECT * FROM options WHERE autoload='yes' ", ARRAY_A);

foreach ($_settings as $k => $v) {

    $options[$v['type']][$v['option_name']] = $v['option_value'];
}


putenv('LC_ALL=    en_US.UTF-8');
setlocale(LC_ALL, 'en_US.UTF-8');

bindtextdomain("evocms", DIR_ROOT . "/language");
textdomain("evocms");

// Require core functions
require_once(CORE_DIR . '/fn.database.php');
require_once(CORE_DIR . '/fn.image.php');
require_once(CORE_DIR . '/fn.common.php');
require_once(CORE_DIR . '/fn.mail.php');
require_once(CORE_DIR . '/fn.string.php');
require_once(CORE_DIR . '/fn.category.php');
require_once(CORE_DIR . '/fn.post.php');
require_once(CORE_DIR . '/fn.hooks.php');
require_once(CORE_DIR . '/fn.template.php');



// Mail
require(LIB_DIR . "/phpmailer/Exception.php");
require(LIB_DIR . "/phpmailer/PHPMailer.php");
require(LIB_DIR . "/phpmailer/SMTP.php");

$EKLENTIS = array();
$EKLENTI  = $db->get_results("SELECT * FROM addons GROUP BY anahtar", ARRAY_A, false);

foreach ($EKLENTI as $k => $v) {

    $EKLENTIS[$v['anahtar']] = $v;
}
//Addons init
// $A = directory_list(ADDONS_DIR);
// foreach ($A as $k => $v) {
//     $aaddons[] = strtolower($k);
// }

foreach ($EKLENTIS as $k => $v) {

    if ($v['durum'] == 'aktif') {

        $addons_name = $k;
        $ADDONS[$addons_name]['adi'] = ($v['adi']) ? $v['adi'] : $k;
        $ADDONS[$addons_name]['name'] = ($v['tekil_adi']) ? $v['tekil_adi'] : $k;
        $ADDONS[$addons_name]['names'] = ($v['cogul_adi']) ? $v['cogul_adi'] : $k;

        // image dir
        ${$addons_name . "_images"}  = IMAGES_DIR_OUT . '/' . $addons_name . '/';
        ${$addons_name . "_web_images"} = IMAGES_WEB_DIR_OUT . '/' . $addons_name . '/';

        if (file_exists(ADDONS_DIR . "/" . $k . "/init.php"))
            require(ADDONS_DIR . "/" . $k . "/init.php");
        if (file_exists(ADDONS_DIR . "/" . $k . "/func.php"))
            require(ADDONS_DIR . "/" . $k . "/func.php");

        // if ($k == 'projects') {
        //     echo ADDONS_DIR . "/" . $k . "/func.php";
        //     var_dump(file_exists(ADDONS_DIR . "/" . $k . "/func.php"));
        // }
    }
}


// Permission add & update end
// Ajax init - BUG
if (@$ajax == 1) {

    define('AJAX_REQUEST', 1);

    // 7 Mart 2025 @workinton
    if ($target == 'sidebar_minimize') {
        $_data = array();
        $value = $_POST['value'];
        $_data['sidebar_minimize'] = ($value == '2') ? '1' : '0';
        fn_update_array('users', $_data, "user_id = " . USER_LOGIN);
        send_json($_data);
    }
    // Demet Hanim ve Can Bey'e Ozel
    if ($target == 'system_show_down') {
        if ($mode == 'get') {
            if ($options['General']['bakim_calismasi'] == '2') {
                if (USER_LOGIN == '2' or USER_LOGIN == '3' or USER_LOGIN == '6') {
                    send_json(array('redirect' => 0));
                } else {
                    send_json(array('redirect' => 1));
                }
            } else {
                send_json(array('redirect' => 0));
            }
        }
        if ($mode == 'set') {
            $_data = array();
            $_data['option_value'] = $value;
            fn_update_array('options', $_data, "option_id = 87 limit 1");
            if ($value == '2')
                send_json(array('msg' => 'Sistem tüm kullanıcılar için devre dışı bırakıldı.'));
            else
                send_json(array('msg' => 'Sistem tüm kullanıcılar için aktifleştirildi.'));
        }
    }

    $ajax_include_path = fn_target_inc_set();
    require $ajax_include_path;
    die;
} else {
    define('AJAX_REQUEST', 0);
}
$PERMISSION_TARGET = array();
// if (is_developer('v3.5')) {
if (is_developer('v3.5')) {
    if (USER_LOGIN)
        $PERMISSION_TARGET = get_permisson_user(USER_LOGIN, $target);
} else {

    $PERMISSION_TARGET['add'] = '1';
    $PERMISSION_TARGET['update'] = '1';
    $PERMISSION_TARGET['manage'] = '1';
    $PERMISSION_TARGET['delete'] = '1';
    $PERMISSION_TARGET['view'] = '1';
    $PERMISSION_TARGET['archive_mode'] = '1';
    $PERMISSION_TARGET['archive'] = '1';
    // $PERMISSION_TARGET['log'] = '1';
    $PERMISSION_TARGET['clone'] = '1';
    $PERMISSION_TARGET['addtocari'] = '1';
}

// Tumm mailler icin gerekli
$PERMISSION_TARGET['send'] = '1';
// 5 Adet Cumhuriyet Altini Kulpsuz : 5 x 1995 TL 
// 9 Adet Cumhuriyet Altini Kulptsiz: 9 x 3700 TL
// 1 Adet Ceyrek Altin 485
// Yarim Gram 1 x 550 TL
// 4.220,00
// 1.056,00
// 2.122,3

// 16 Mart 2023 Guncelleme
// 15 cumhuriyer
// Bir tane ceyrek
// Bir tane gram altın
// bir yarım altın
// Sadece evrim
if ($target == 'mailbox' or $target == 'file_manager') {
    if (USER_LOGIN != '2') {
        fn_set_notice('Yetkiniz yok', 'W');
        fn_redirect(WEB_DIR);
    }
}
