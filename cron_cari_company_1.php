<?php
ob_start();
session_start();
$ses_id = session_id();
define('AREA', 'A');
define('APP_INIT', 0);
$user_login = (@$_SESSION['user_login']) ? $_SESSION['user_login'] : 0;
define('USER_LOGIN', $user_login);
require dirname(__FILE__) . '/init.php';



// $customer_id = 3;
if ($self_ajax == '1') {
  $customer = get_row('customers', "is_delete=0 AND id=$customer_id", 'company_id');
  $company_id  = $customer->company_id;
} else {

  $company_id = 1; // TURKIYE
}
// die;
// Sirkete gore mail gonderilecek. TUrkiye - Europe
// 11 Nisan 2023 11:25 @WORKINTON

$company = get_row('my_companies', "id=$company_id");
// Bugunin ID'SI
// echo $today_uniq =  date('mdY') . '-' . 'DENEME';
$today_uniq =  date('mdY') . '-' . $company_id;

// $customer_id = 49;
if ($customer_id) {
  $customers = get_results('customers', "is_delete=0 AND id=$customer_id");
} else {
  $customers = get_results('customers', "is_delete=0 AND cron_action_date_1<>'$today_uniq'");
}


foreach ($customers as $musteri_row => $musteri) {

  // Musteri Resmi
  $musteri_logosu = MAIL_LINK_BEFORE . $customers_web_images . $musteri->logo;
  $musteri_logosu = MAIL_LINK_BEFORE . T . $musteri_logosu . '&w=175';
  if ($musteri->adres2) {
    $adres2 = '<p>' . $musteri->adres2 . '</p>';
  } else {
    $adres2 = '';
  }


  // $invoice_herader_png = WEB_DIR . '/mail_assets/invoice_header.png';
  $Header = '
    <table border="0" align="left" width="1250" cellspacing="0" cellpadding="0" style="border-collapse: collapse; mso-table-lspace:0pt; margin-top: 10px;">
              <tbody>
                <tr>
                  <td align="left" style="vertical-align: middle; padding: 0; text-align: left; border-collapse: collapse;" width="700">
                    <table border="0" align="left" width="750" cellspacing="0" cellpadding="0" style="mso-table-lspace:0pt; border-collapse: collapse; margin: 0; mso-table-lspace:0pt; mso-table-rspace:0pt; padding: 0;">
                      <tr>
                        <td align="center" width="100"><img src="' . $musteri_logosu . '" style="max-width: 100%;height: auto;" alt=""></td>
                        <td width="" style="padding-left: 20px;" width="600">
                          <span class="fw_bold" style="font-size:22px;color: #1d274a;margin: 0; padding-bottom: 0;"><b>' . $musteri->firma_adi . '</b></span>
                          <p style="margin:0; padding:0; margin-bottom: 10px;margin-top: 10px;">' . $musteri->adres . '</p>
                          ' . $adres2 . '
                          <p style="margin:0; padding:0; margin-bottom: 10px;margin-top: 10px;">' . $musteri->sehir . ' ' . $musteri->postakodu . ',' . $musteri->ulke . '</p>
                          <p style="margin:0; padding:0; margin-bottom: 10px;margin-top: 10px;"><span class="fw_bold" >VAT REG. NO:</span> ' . $musteri->vat . '</p>
                        </td>
                      </tr>
                    </table>
                  </td>
                  <td align="center" style="vertical-align: bottom;padding-left: 20px; border-bottom:1px solid #002060;" width="550">
                  <span class="fw_bold">ISTANBUL, ' . date("jS F Y", time()) . '</span>
                  </td>
                </tr>
              </tbody>
            </table>
            <table border="0" align="left" width="100%" cellspacing="0" cellpadding="0" style="border-collapse: collapse; mso-table-lspace:0pt; margin-top: 10px; margin-left: 10px;">
              <tr>
                <td>
                <p>Good day,</p>
                <p>We wanted to reach out because our records indicate that payments for the below invoices are  <span class="fw_bold" >OVERDUE.</span></p>
                </td>
              </tr>
            </table>';

  $table_dolar = liste_olustur('Dolar', $musteri->id, $company_id);
  $table_euro = liste_olustur('Euro', $musteri->id, $company_id);
  // echo 'geldim';
  if (!$table_dolar && !$table_euro) {

    $tmp_user = array();
    $tmp_user['cron_action_date_1'] = $today_uniq;
    fn_update_array('customers', $tmp_user, "id=$musteri->id limit 1");
    if ($self_ajax)
      send_json(array('error' => 'Kriterlere uygun gönderilecek bir veri bulunamadı'));
    continue;
  }

  $Header .= $table_dolar . $table_euro . '</tbody></table>';
  // Yeni mail ekle

  // echo $Header;
  // die;
  $mail_id = taslak_mail_olustur(28, 0, 0, 0);

  $gonderilenler = array();
  if ($musteri->cari_email5) {
    $gonderici = array();
    $gonderici['value'] = $musteri->firma_adi;
    $gonderici['email'] = $musteri->cari_email5;
    $gonderici['rank'] = 'owner';
    $gonderici['pic'] = $customers_web_images . $musteri->logo;
    $gonderici['class'] = 'tagify__tag--info';
    $gonderilenler[] = $gonderici;
  } else {
    $gonderici = array();
    $gonderici['value'] = 'Demet Seçmeer';
    $gonderici['email'] = '<EMAIL>';
    $gonderici['rank'] = 'owner';
    $gonderici['pic'] = $customers_web_images . $musteri->logo;
    $gonderici['class'] = 'tagify__tag--info';
    $gonderilenler[] = $gonderici;
  }

  if ($musteri->cari_email6) {
    $gonderici = array();
    $gonderici['value'] = $musteri->firma_adi;
    $gonderici['email'] = $musteri->cari_email6;
    $gonderici['rank'] = 'owner';
    $gonderici['pic'] = $customers_web_images . $musteri->logo;
    $gonderici['class'] = 'tagify__tag--info';
    $gonderilenler[] = $gonderici;
  }
  if ($musteri->cari_email7) {
    $gonderici = array();
    $gonderici['value'] = $musteri->firma_adi;
    $gonderici['email'] = $musteri->cari_email7;
    $gonderici['rank'] = 'owner';
    $gonderici['pic'] = $customers_web_images . $musteri->logo;
    $gonderici['class'] = 'tagify__tag--info';
    $gonderilenler[] = $gonderici;
  }
  if ($musteri->cari_email8) {
    $gonderici = array();
    $gonderici['value'] = $musteri->firma_adi;
    $gonderici['email'] = $musteri->cari_email8;
    $gonderici['rank'] = 'owner';
    $gonderici['pic'] = $customers_web_images . $musteri->logo;
    $gonderici['class'] = 'tagify__tag--info';
    $gonderilenler[] = $gonderici;
  }
  $cc_val = array();
  $cc_val['value'] = 'Demet SEÇMEER';
  $cc_val['email'] = '<EMAIL>';
  $cc_val['rank'] = 'owner';
  $cc_val['class'] = 'tagify__tag--warning';
  $cc_list[] = $cc_val;
  $cc_val = array();
  $cc_val['value'] = 'Can SEÇMEER';
  $cc_val['email'] = '<EMAIL>';
  $cc_val['rank'] = 'owner';
  $cc_val['class'] = 'tagify__tag--warning';
  $cc_list[] = $cc_val;

  // $gonderilenler = array();
  // $gonderici = array();
  // $gonderici['value'] = 'Evrim BAKIR';
  // $gonderici['email'] = '<EMAIL>';
  // $gonderici['rank'] = 'owner';
  // $gonderici['pic'] = $customers_web_images . $musteri->logo;
  // $gonderici['class'] = 'tagify__tag--info';
  // $gonderilenler[] = $gonderici;

  $_data = array();
  $_data['gonderilenler'] = serialize($gonderilenler);
  $_data['compose_cc'] = serialize($cc_list);
  $_data['mesaj'] = fn_add_slashes(trim($Header));
  $_data['konu'] = 'OVERDUE INVOICES DETATILS' . ' - ' . $company->country;

  fn_update_array('mail_gonderilenler', $_data, "id=$mail_id");

  $sonuc = mail_gonder($mail_id, $kaydedilmeyen_mesaj_govdesi);
  $tmp_user = array();
  $tmp_user['cron_action_date_1'] = $today_uniq;
  fn_update_array('customers', $tmp_user, "id=$musteri->id limit 1");
  if ($self_ajax == '1') {
    send_json(array('msg' => 'Sended Mail'));
  } else {
    echo 'Sended Mail:' . $musteri->id;
  }
  break;
}
