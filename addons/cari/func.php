<?php
function liste_olustur_soa($para_birimi, $customer_id, $company_id = 0, $year)
{
    global $db;
    $balance = 0;
    $currency_sql = " AND currency='$para_birimi'";
    $ex_sql_c = " AND  (company_id=" . $_SESSION['selected_country'] . ')';

    // echo "SELECT * FROM cari WHERE 1=1 AND yil='$year' AND is_delete=0 AND customer_id=$customer_id  $currency_sql $ex_sql_c order by id";
    // die;
    $caris = $db->get_results("SELECT * FROM cari WHERE 1=1 AND yil='$year' AND is_delete=0 AND customer_id=$customer_id  $currency_sql $ex_sql_c order by id");

    $tmp_data = array();
    foreach ($caris as $k => $v) {
        if ($v->type == 'odeme') {
            $tmp_data[$v->pay_date . $v->id] = $v;
            // $tmp_data[$v->pay_date] = $v;
        } else {
            $tmp_data[$v->inv_date . $v->id] = $v;
            // $tmp_data[$v->inv_date] = $v;
        }
    }
    $caris = $tmp_data;
    ksort($caris);

    if (!$caris) {
        return false;
    }
    $musteri = get_row("customers", "id=$customer_id", 'bank_id_euro,bank_id_dolar');

    if ($para_birimi == 'Euro') {
        $banka = get_row('company_account', "id=$musteri->bank_id_euro");
    }
    if ($para_birimi == 'Dolar') {
        $banka = get_row('company_account', "id=$musteri->bank_id_dolar");
    }

    $TABLE  = '<table border="1" align="left" width="100%" cellspacing="0" cellpadding="5" style="border-collapse: collapse; margin-top: 10px; margin-left: 10px; margin-bottom: 20px; border-color:#002060;">
  <tr>
    <th align="left"><span class="fw_bold" style="color:#002060;">VESSEL RAME / PORT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">PAY.DATE</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">INV.DATE.</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">INVOICE REF.</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">DESCRIPTION</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">DUE DATE</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">INV. AMOUNT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">ADV. AMOUNT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">PAYMENT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">BALANCE</span></th>
  </tr>
  <tbody>';
    $table_tr = '';
    $balance = 0;
    foreach ($caris as $k => $v) {
        if ($v->type == 'alacak') {

            $inv_amount = $v->inv_amount - $v->advance_payment;
            $balance = $balance + $inv_amount;


            // $balance = $balance + $v->inv_amount;
            $color = '#2d7ec1';
        } else {
            $balance = $balance - $v->payment;
            $color = '#1BC5BD';
        }

        $vessel_name_port = ($v->project_id) ? get_project_name($v->project_id) : $v->vessel_name_port;
        $inv_date = ($v->inv_date) ?  date("jS M y", $v->inv_date) : '-';
        $pay_date = ($v->pay_date) ?  date("jS M y", $v->pay_date) : '-';
        $dua_date = ($v->dua_date) ?  date("jS M y", $v->dua_date) : ' - ';

        if ($v->type == 'alacak') {
            $odeme_durumu_alacak = ($v->odeme_durumu == '0') ? 'Payment EXP.' : 'Paid';
            $table_tr .= '<tr>
                <td style="color:' . $color . '">' . $vessel_name_port . '</td>
                <td style="color:' . $color . '"></td>
                <td style="color:' . $color . '">' . $inv_date . '</td>
                <td style="color:' . $color . '">' . $v->invoice_ref . '</td>
                <td style="color:' . $color . '" class="text-muted font-weight-bold">' . $v->description . '</td>
                <td style="color:' . $color . '">' . $dua_date . '</td>
                <td style="color:' . $color . '">' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($v->inv_amount, 2, ',', '.') . '</td>
                <td style="color:' . $color . '">' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($v->advance_payment, 2, ',', '.') . '</td>
                <td style="color:' . $color . '">' . $odeme_durumu_alacak . '</td>
                <td style="color:' . $color . '">' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($balance, 2, ',', '.') . '</td>
                </tr>';
        } else {
            $pay_date = ($v->pay_date) ?  date("jS M y", $v->pay_date) : '-';

            $table_tr .= '<tr>
            <td style="color:' . $color . '">' . $vessel_name_port . '</td>
            <td style="color:' . $color . '">' . $pay_date . '</td>
            <td style="color:' . $color . '">' . $inv_date . '</td>
            <td style="color:' . $color . '">' . $v->invoice_ref . '</td>
            <td style="color:' . $color . '" class="text-muted font-weight-bold">' . $v->description . '</td>
            <td style="color:' . $color . '">' . $dua_date . '</td>
            <td style="color:' . $color . '">' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($v->inv_amount, 2, ',', '.') . '</td>
            <td style="color:' . $color . '"></td>
            <td style="color:' . $color . '">' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($v->payment, 2, ',', '.') . '</td>
            <td style="color:' . $color . '">' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($balance, 2, ',', '.') . '</td>
            </tr>';
        }
    }
    $TABLE .= $table_tr . '</tbody></table>';
    $TABLE  .= '
    <table border="0" align="left" width="100%" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
    <tr>
    <td>
    <p style="margin-bottom:20px;"><span class="fw_bold" style="font-size:16px;text-decoration: underline;">Buraya istediginiz not gelecek</p>';

    // Bank Information
    if ($para_birimi == 'Euro') {
        $banka_birim = 'EUR';
    }
    if ($para_birimi == 'Dolar') {
        $banka_birim = 'USD';
    }

    $bank_header = '
<span class="fw_bold" style="text-decoration: underline;">OUR ' . $banka_birim . ' BANK ACCOUNT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<p style="margin:0; padding:0; margin-bottom:1px; margin-top:10px;">Beneficiary: <span class="fw_bold">' . $banka->name . '</span></p>
<p style="margin:0; padding:0; margin-bottom:1px;">Iban no: <span class="fw_bold">' . $banka->iban . '</span></p>
<p style="margin:0; padding:0; margin-bottom:21px;">Swift: <span class="fw_bold">' . $banka->swift . '</span></p>

</td><tr></table>';

    return $TABLE  .= $bank_header;
}

function liste_olustur($para_birimi, $customer_id, $company_id = 0)
{

    $bes_gun_sonra = strtotime("+5 days");
    $suan = time();
    $balance = 0;
    // $yil = date('Y');

    $company_sql = '';
    if ($company_id && $company_id != '-1') {
        $company_sql = " AND company_id=$company_id";
    }

    // echo unix_to_date(**********);
    // echo '<br>';

    // echo "odeme_durumu=0 AND customer_id=$customer_id AND type='alacak' AND currency='$para_birimi' AND dua_date>0 AND dua_date < $suan   $company_sql order by dua_date asc";
    // AND yil='$yil'
    $caris = get_results('cari', "odeme_durumu=0 AND customer_id=$customer_id AND type='alacak' AND currency='$para_birimi' AND dua_date>0 AND dua_date < $suan   $company_sql order by dua_date asc");
    if (!$caris) {
        return false;
    }
    $musteri = get_row("customers", "id=$customer_id", 'bank_id_euro,bank_id_dolar');

    if ($para_birimi == 'Euro') {
        $banka = get_row('company_account', "id=$musteri->bank_id_euro");
    }
    if ($para_birimi == 'Dolar') {
        $banka = get_row('company_account', "id=$musteri->bank_id_dolar");
    }

    $TABLE  = '<table border="1" align="left" width="100%" cellspacing="0" cellpadding="5" style="border-collapse: collapse; margin-top: 10px; margin-left: 10px; margin-bottom: 20px; border-color:#002060;">
  <tr>
    <th align="left"><span class="fw_bold" style="color:#002060;">VESSEL RAME / PORT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">PAY.DATE</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">INV.DATE.</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">INVOICE REF.</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">DESCRIPTION</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">DUE DATE</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">INV. AMOUNT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">ADV. AMOUNT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">PAYMENT</span></th>
    <th align="left"><span class="fw_bold" style="color:#002060;">BALANCE</span></th>
  </tr>
  <tbody>';
    $table_tr = '';
    foreach ($caris as $k => $v) {

        $inv_amount = $v->inv_amount - $v->advance_payment;
        $balance = $balance + $inv_amount;

        $inv_date = ($v->inv_date) ?  date("jS M y", $v->inv_date) : '-';
        $dua_date = ($v->dua_date) ?  date("jS M y", $v->dua_date) : ' - ';
        $vessel_name_port = ($v->project_id) ? get_project_name($v->project_id) : $v->vessel_name_port;
        $table_tr .= '<tr>
      <td>' . $vessel_name_port . '</td>
      <td></td>
      <td>' . $inv_date . '</td>
      <td>' . $v->invoice_ref . '</td>
      <td class="text-muted font-weight-bold">' . $v->description . '</td>
      <td>' . $dua_date . '</td>
      <td>' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($v->inv_amount, 2, ',', '.') . '</td>
      <td>' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($v->advance_payment, 2, ',', '.') . '</td>
      <td></td>
      <td>' . para_birimi($v->currency, 1, 1) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . number_format($balance, 2, ',', '.') . '</td>
    </tr>';
    }
    $TABLE .= $table_tr . '</tbody></table>';
    $TABLE  .= '<p style="margin-bottom:20px;">We kindly ask you to remit the amount of <span class="fw_bold" style="font-size:16px;text-decoration: underline;">' . para_birimi($v->currency, 1, 1) . '&nbsp;' . number_format($balance, 2, ',', '.') . '.</span>- to our following bank account, mentioning our invoice references.</p>';

    // Bank Information
    if ($para_birimi == 'Euro') {
        $banka_birim = 'EUR';
    }
    if ($para_birimi == 'Dolar') {
        $banka_birim = 'USD';
    }

    $bank_header = '
<span class="fw_bold" style="text-decoration: underline;">OUR ' . $banka_birim . ' BANK ACCOUNT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<p style="margin:0; padding:0; margin-bottom:1px; margin-top:10px;">Beneficiary: <span class="fw_bold">' . $banka->name . '</span></p>
<p style="margin:0; padding:0; margin-bottom:1px;">Iban no: <span class="fw_bold">' . $banka->iban . '</span></p>
<p style="margin:0; padding:0; margin-bottom:21px;">Swift: <span class="fw_bold">' . $banka->swift . '</span></p>
';
    // 0 ucret gonderilmez
    if ($balance > 0)
        return $TABLE  .= $bank_header;
    else
        return '';
}


function fn_update_cari_balance($customer_id, $cari_id = 0, $currency = '', $yil = '')
{
    global $db;

    if (empty($currency)) {
        $currency = get_var('cari', 'currency', "id=$cari_id");
    }

    if ($cari_id) {
        $yil = get_var('cari', 'yil', "id=$cari_id");
    }
    if ($yil) {
        // Yil geldi
    } else {
        $yil = date('Y');
    }

    // Dolar
    $toplam_alacak_inv_amount = $db->get_var("SELECT SUM(inv_amount) FROM cari WHERE type='alacak' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency'");

    // Fix Advance Payment
    $toplam_alacak_advance_payment = $db->get_var("SELECT SUM(advance_payment) FROM cari WHERE type='alacak' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency'");
    $toplam_alacak = $toplam_alacak_inv_amount - $toplam_alacak_advance_payment;

    $toplam_odeme = $db->get_var("SELECT SUM(payment) FROM cari WHERE type='odeme' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency'");

    $_data = array();
    $_data['cari_balance_' . strtolower($currency)] = $toplam_alacak - $toplam_odeme;
    fn_update_array('customers', $_data, "id=$customer_id limit 1");
}
function gecmis_yil_balance($customer_id, $currency, $year)
{
    global $db;

    // $secili_yil = date('Y');
    $yil = $year;
    $ex_sql_c = get_country_sql();

    // echo "SELECT SUM(inv_amount) FROM cari WHERE type='alacak' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency' ";
    $toplam_alacak_inv_amount = $db->get_var("SELECT SUM(inv_amount) FROM cari WHERE type='alacak' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency' $ex_sql_c ");
    // echo '-';
    $toplam_alacak_advance_payment = $db->get_var("SELECT SUM(advance_payment) FROM cari WHERE type='alacak' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency' $ex_sql_c ");
    $toplam_alacak = $toplam_alacak_inv_amount - $toplam_alacak_advance_payment;

    //  "SELECT SUM(payment) FROM cari WHERE type='odeme' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency' ";
    $toplam_odeme = $db->get_var("SELECT SUM(payment) FROM cari WHERE type='odeme' AND customer_id=$customer_id AND yil='$yil' AND currency='$currency' $ex_sql_c ");
    $_data = array();
    $tutar = $toplam_alacak - $toplam_odeme;
    return number_format($tutar, 2, ',', '.');
}
function did_add_invoice($invoice_id)
{
    $id = get_var('cari', 'id', "invoice_id=$invoice_id");
    if ($id) return true;
    return false;
}
function the_price_cari($tutar, $para_birimi = '')
{
    if ($para_birimi == 'Dolar') $para_birimi = '$';
    if ($para_birimi == 'Euro') $para_birimi = '€';
    $para_birimi = ($para_birimi) ? ' ' . $para_birimi : '';
    $price = number_format($tutar, 2, ',', '.');
    $class =  ($tutar < 0) ? 'text-kirmizi' : 'text-success';
    echo $str = '<span class="font-weight-bold ' . $class . '">' . $price . ' ' . $para_birimi . '</span>';
}

function add_cari_from_invoice($invoice_id)
{
    global $db;
    $n_data = array();
    $invoice = get_row('invoices', "id=$invoice_id");
    $proje = get_row('projects', "id=$invoice->project_id", 'ship_id,port,terminal,company_id');
    $ship = get_row("ships", "id=$proje->ship_id");

    $n_data['project_id'] = $invoice->project_id;
    if ($invoice->customer_id) {
        $n_data['customer_id'] = $invoice->customer_id;
    } else {
        $n_data['customer_id'] = $ship->customer_id;
    }
    $n_data['vessel_name_port'] = $ship->name . ' @ ' . $proje->terminal;
    $n_data['pay_date'] = 0;
    $n_data['inv_date'] = $invoice->invoice_date;
    $n_data['invoice_ref'] = $invoice->invoice_ref;
    $n_data['description'] = '';
    $n_data['dua_date'] = strtotime("+30 days", $invoice->invoice_date);

    // Account Summary varsa, formul baska. 31 ocak 2023 @CNMAR OFFICE
    // if ($invoice->account_summary == '1') {
    //     $first_data = get_row('account_summary', "invoice_id=$invoice_id AND first_data=1");
    //     $account_summary_toplam_tutar = $db->get_var("SELECT SUM(amount) FROM account_summary WHERE invoice_id=$invoice_id AND first_data=0");
    //     $toplam_tutar = $first_data->amount - $account_summary_toplam_tutar;
    // } else {
    // }

    // Son olarak 3 Nisan 2023 Can Bey ile buna karar verildi. Grand Total baz alinacak.
    // $toplam_tutar = $db->get_var("SELECT SUM(amount) FROM voucher_list WHERE invoice_id=$invoice_id");

    // Son guncelleme: 15 nisan 2024 @Workinton: Gelen mail ile.
    $operation_form_ids = str_replace('#', '', $invoice->operation_form_ids);
    $operation_form_ids = explode(',', $operation_form_ids);
    $toplam_tutar = 0;
    foreach ($operation_form_ids as $form_id) {

        $toplam_tutar += get_sub_total($invoice_id, $form_id);
    }
    $n_data['inv_amount'] = $toplam_tutar;
    $n_data['currency'] = $invoice->currency;
    $n_data['payment'] = 0;
    $n_data['invoice_id'] = $invoice_id;
    $n_data['type'] = 'alacak'; // Alacak ekliyoruz.

    $n_data['company_id'] = $proje->company_id;
    // $n_data['yil'] = date('Y', $n_data['dua_date']);
    $n_data['yil'] = date('Y', $invoice->invoice_date); // Can bey'in talebi
    $cari_id = get_var('cari', 'id', "invoice_id=$invoice_id");
    $is_update = false;
    if ($cari_id) {
        fn_update_array('cari', $n_data, "id=$cari_id limit 1");
        $is_update = true;
    } else {
        $cari_id = fn_insert_array('cari', $n_data);
    }

    fn_update_cari_balance($ship->customer_id, $cari_id);
    return array($cari_id, $is_update);
}
