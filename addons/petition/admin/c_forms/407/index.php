<?php
$URL = W_ADDONS_DIR . DS . $target . DS . 'admin' . DS . 'c_forms' . DS . $view . DS;

if (empty($proje) or empty($murettebat)) {
	die(_('Eksik Bilgi'));
}

$personel = get_row('users', "user_id=" . USER_LOGIN);
$personel_kasesi = T . $_SESSION['admin']['personel_kasesi'] . '&h=90';
$personel_adi = $_SESSION['admin']['name'] . ' ' . $_SESSION['admin']['lastname'];
$proje = get_row("projects", "id=$proje");
// pe($proje);
$data = get_row('murettebatlar', "id=$murettebat");

$ucus_bilgileri = get_row('ucusbilgileri', "murettebat_ids LIKE '%#$murettebat#%' AND is_delete=0");
// pe($ucus_bilgileri);
$ucuslar = get_results('ucuslar', "ucus_id=$ucus_bilgileri->id order by id asc");
// pe($ucuslar);
$ship = get_row("ships", "id=$proje->ship_id");

//Bu satır uçuşlardan alınacak, tarih ve saat 
//Katılacak mürettebatta; İstanbul veya Sabiha Gökçen Havaalanına geliş zamanı ve o uçuşun numarası
//Ayrılacak mürettebatta; ilk uçuşun gidiş zamanı ve o uçuşun numarası

//$main_ucus = 
if ($data->status == 'EBMERKATION') {
	foreach ($ucuslar as $a => $b) {
		if ($b->varis_havalimani == 'SAW' or $b->varis_havalimani == 'IST' or $b->varis_havalimani == 'ESB') {
			$main_ucus = $b;
			break;
		}
	}
	$checked_1 = 'checked';
	$checked_2 = '';
}
if ($data->status == 'DESEMMERKATION') {
	$main_ucus = $ucuslar[0];

	$checked_1 = '';
	$checked_2 = 'checked';
}
// pe($main_ucus);
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
	<meta charset="utf-8" />
</head>

<body style="margin: 0;" onload="">

	<div id="p1" style="overflow: hidden; position: relative; background-color: white; width: 909px; height: 1286px; top:0px">

		<!-- Begin shared CSS values -->
		<style class="shared-css" type="text/css">
			.t {
				transform-origin: bottom left;
				z-index: 2;
				position: absolute;
				white-space: pre;
				overflow: visible;
				line-height: 1.5;
			}

			.ek1_baslik {
				position: absolute;
				top: 0;
				right: 16px;
				font-size: 17px !important;
			}

			.text-container {
				white-space: pre;
			}

			@supports (-webkit-touch-callout: none) {
				.text-container {
					white-space: normal;
				}
			}
		</style>
		<!-- End shared CSS values -->


		<!-- Begin inline CSS -->
		<style type="text/css">
			#t1_1 {
				left: 241px;
				bottom: 1221px;
				letter-spacing: -0.12px;
				word-spacing: 0.02px;
			}

			#t2_1 {
				left: 375px;
				bottom: 1182px;
				letter-spacing: 0.16px;
			}

			#t3_1 {
				left: 44px;
				bottom: 1156px;
				letter-spacing: 0.16px;
			}

			#t4_1 {
				left: 44px;
				bottom: 1135px;
				letter-spacing: 0.15px;
			}

			#t5_1 {
				left: 44px;
				bottom: 1032px;
				letter-spacing: 0.16px;
			}

			#t6_1 {
				left: 441px;
				bottom: 1138px;
				letter-spacing: 0.12px;
				word-spacing: -0.01px;
			}

			#t7_1 {
				left: 441px;
				bottom: 1120px;
				letter-spacing: 0.17px;
			}

			#t8_1 {
				left: 438px;
				bottom: 1103px;
				letter-spacing: 0.14px;
			}

			#t9_1 {
				left: 442px;
				bottom: 1050px;
				letter-spacing: 0.17px;
				word-spacing: -0.01px;
			}

			#ta_1 {
				left: 442px;
				bottom: 1032px;
				letter-spacing: 0.18px;
			}

			#tb_1 {
				left: 363px;
				bottom: 1005px;
				letter-spacing: 0.16px;
			}

			#tc_1 {
				left: 44px;
				bottom: 955px;
				letter-spacing: 0.11px;
			}

			#td_1 {
				left: 212px;
				bottom: 955px;
				letter-spacing: 0.14px;
			}

			#te_1 {
				left: 494px;
				bottom: 955px;
				letter-spacing: 0.17px;
			}

			#tf_1 {
				left: 479px;
				bottom: 937px;
				letter-spacing: 0.1px;
			}

			#tg_1 {
				left: 44px;
				bottom: 919px;
				letter-spacing: 0.15px;
			}

			#th_1 {
				left: 260px;
				bottom: 889px;
				letter-spacing: 0.15px;
			}

			#ti_1 {
				left: 444px;
				bottom: 919px;
				letter-spacing: 0.12px;
				word-spacing: 0.01px;
			}

			#tj_1 {
				left: 710px;
				bottom: 919px;
				letter-spacing: 0.17px;
				word-spacing: 0.02px;
			}

			#tk_1 {
				left: 748px;
				bottom: 901px;
				letter-spacing: 0.18px;
			}

			#tl_1 {
				left: 44px;
				bottom: 875px;
				letter-spacing: 0.14px;
				word-spacing: 0.02px;
			}

			#tm_1 {
				left: 260px;
				bottom: 851px;
				letter-spacing: 0.16px;
			}

			#tn_1 {
				left: 444px;
				bottom: 875px;
				letter-spacing: 0.15px;
			}

			#to_1 {
				left: 719px;
				bottom: 875px;
				letter-spacing: 0.16px;
				word-spacing: 0.01px;
			}

			#tp_1 {
				left: 44px;
				bottom: 844px;
				letter-spacing: 0.16px;
			}

			#tq_1 {
				left: 264px;
				bottom: 844px;
				letter-spacing: 0.16px;
			}

			#tr_1 {
				left: 287px;
				bottom: 826px;
				letter-spacing: 0.2px;
			}

			#ts_1 {
				left: 444px;
				bottom: 844px;
				letter-spacing: 0.14px;
				word-spacing: 0.01px;
			}

			#tt_1 {
				left: 763px;
				bottom: 844px;
				letter-spacing: 0.12px;
			}

			#tu_1 {
				left: 44px;
				bottom: 807px;
				letter-spacing: 0.16px;
				word-spacing: 0.01px;
			}

			#tv_1 {
				left: 304px;
				bottom: 807px;
				letter-spacing: 0.15px;
			}

			#tw_1 {
				left: 444px;
				bottom: 807px;
				letter-spacing: 0.16px;
			}

			#tx_1 {
				left: 765px;
				bottom: 807px;
				letter-spacing: 0.14px;
			}

			#ty_1 {
				left: 44px;
				bottom: 775px;
				letter-spacing: 0.14px;
			}

			#tz_1 {
				left: 306px;
				bottom: 775px;
				letter-spacing: 0.13px;
			}

			#t10_1 {
				left: 444px;
				bottom: 775px;
				letter-spacing: 0.14px;
			}

			#t11_1 {
				left: 763px;
				bottom: 775px;
				letter-spacing: 0.12px;
			}

			#t12_1 {
				left: 44px;
				bottom: 742px;
				letter-spacing: 0.14px;
			}

			#t13_1 {
				left: 306px;
				bottom: 742px;
				letter-spacing: 0.13px;
			}

			#t14_1 {
				left: 444px;
				bottom: 742px;
				letter-spacing: 0.14px;
			}

			#t15_1 {
				left: 763px;
				bottom: 742px;
				letter-spacing: 0.12px;
			}

			#t16_1 {
				left: 281px;
				bottom: 700px;
				letter-spacing: 0.15px;
			}

			#t17_1 {
				left: 44px;
				bottom: 660px;
				letter-spacing: 0.14px;
				word-spacing: 0.01px;
			}

			#t18_1 {
				left: 44px;
				bottom: 642px;
				letter-spacing: 0.13px;
			}

			#t19_1 {
				left: 367px;
				bottom: 651px;
				letter-spacing: 0.16px;
			}

			#t1a_1 {
				left: 44px;
				bottom: 619px;
				letter-spacing: 0.14px;
				word-spacing: 0.01px;
			}

			#t1b_1 {
				left: 265px;
				bottom: 606px;
				letter-spacing: 0.18px;
				word-spacing: -0.01px;
			}

			#t1c_1 {
				left: 444px;
				bottom: 619px;
				letter-spacing: 0.15px;
			}

			#t1d_1 {
				left: 718px;
				bottom: 619px;
				letter-spacing: 0.18px;
			}

			#t1e_1 {
				left: 44px;
				bottom: 586px;
				letter-spacing: 0.13px;
				word-spacing: 0.01px;
			}

			#t1f_1 {
				left: 312px;
				bottom: 586px;
				letter-spacing: 0.13px;
			}

			#t1g_1 {
				left: 444px;
				bottom: 586px;
				letter-spacing: 0.15px;
			}

			#t1h_1 {
				left: 44px;
				bottom: 552px;
				letter-spacing: 0.14px;
			}

			#t1i_1 {
				left: 312px;
				bottom: 552px;
				letter-spacing: 0.13px;
			}

			#t1j_1 {
				left: 444px;
				bottom: 552px;
				letter-spacing: 0.16px;
				word-spacing: -0.01px;
			}

			#t1k_1 {
				left: 750px;
				bottom: 552px;
				letter-spacing: 0.2px;
				word-spacing: -0.01px;
			}

			#t1l_1 {
				left: 315px;
				bottom: 509px;
				letter-spacing: 0.15px;
			}

			#t1m_1 {
				left: 48px;
				bottom: 476px;
				letter-spacing: 0.14px;
			}

			#t1n_1 {
				left: 319px;
				bottom: 476px;
				letter-spacing: 0.17px;
			}

			#t1o_1 {
				left: 48px;
				bottom: 441px;
				letter-spacing: 0.16px;
			}

			#t1p_1 {
				left: 48px;
				bottom: 415px;
				letter-spacing: 0.12px;
				word-spacing: 0.01px;
			}

			#t1q_1 {
				left: 356px;
				bottom: 415px;
				letter-spacing: 0.15px;
			}

			#t1r_1 {
				left: 677px;
				bottom: 415px;
				letter-spacing: 0.17px;
			}

			#t1s_1 {
				left: 48px;
				bottom: 380px;
				letter-spacing: 0.17px;
			}

			#t1t_1 {
				left: 44px;
				bottom: 346px;
				letter-spacing: 0.16px;
			}

			#t1u_1 {
				left: 263px;
				bottom: 346px;
				letter-spacing: 0.2px;
			}

			#t1v_1 {
				left: 432px;
				bottom: 346px;
				letter-spacing: 0.2px;
			}

			#t1w_1 {
				left: 642px;
				bottom: 346px;
				letter-spacing: 0.19px;
			}

			#t1x_1 {
				left: 44px;
				bottom: 297px;
				letter-spacing: 0.11px;
				word-spacing: 0.01px;
			}

			#t1y_1 {
				left: 229px;
				bottom: 297px;
				letter-spacing: 0.12px;
			}

			#t1z_1 {
				left: 399px;
				bottom: 297px;
				letter-spacing: 0.12px;
			}

			#t20_1 {
				left: 558px;
				bottom: 297px;
				letter-spacing: 0.14px;
			}

			#t21_1 {
				left: 216px;
				bottom: 262px;
				letter-spacing: 0.13px;
				word-spacing: 0.01px;
			}

			#t22_1 {
				left: 383px;
				bottom: 262px;
				letter-spacing: 0.12px;
			}

			#t23_1 {
				left: 216px;
				bottom: 227px;
				letter-spacing: 0.14px;
				word-spacing: 0.01px;
			}

			#t24_1 {
				left: 436px;
				bottom: 227px;
				letter-spacing: 0.17px;
				word-spacing: 0.01px;
			}

			#t25_1 {
				left: 44px;
				bottom: 174px;
				letter-spacing: 0.13px;
			}

			#t26_1 {
				left: 229px;
				bottom: 174px;
				letter-spacing: 0.13px;
			}

			#t27_1 {
				left: 280px;
				bottom: 174px;
				letter-spacing: 0.12px;
			}

			#t28_1 {
				left: 422px;
				bottom: 174px;
				letter-spacing: 0.14px;
			}

			#t29_1 {
				left: 465px;
				bottom: 174px;
				letter-spacing: 0.12px;
			}

			#t2a_1 {
				left: 551px;
				bottom: 174px;
				letter-spacing: 0.13px;
				word-spacing: 0.01px;
			}

			#t2b_1 {
				left: 658px;
				bottom: 174px;
				letter-spacing: 0.13px;
				word-spacing: 0.02px;
			}

			#t2c_1 {
				left: 48px;
				bottom: 141px;
				letter-spacing: 0.11px;
			}

			#t2d_1 {
				left: 71px;
				bottom: 123px;
				letter-spacing: 0.11px;
				word-spacing: 0.01px;
			}

			.s1_1 {
				font-size: 17px;
				font-family: LiberationSerif-Bold_h;
				color: #000;
			}

			.s2_1 {
				font-size: 15px;
				font-family: LiberationSerif_m;
				color: #000;
			}

			.s3,
			.s3_1 {
				font-size: 15px;
				font-family: LiberationSerif-Bold_h;
				color: #000;
			}
		</style>
		<!-- End inline CSS -->

		<!-- Begin embedded font definitions -->
		<style id="fonts1" type="text/css">
			@font-face {
				font-family: LiberationSerif-Bold_h;
				src: url("<?php echo $URL; ?>fonts/LiberationSerif-Bold_h.woff") format("woff");
			}

			@font-face {
				font-family: LiberationSerif_m;
				src: url("<?php echo $URL; ?>fonts/LiberationSerif_m.woff") format("woff");
			}

			.twolines {
				width: 160px;
				display: block;
				line-height: 1.1em;
				/* height: 3em; */
				overflow: hidden;
				white-space: normal;
				text-align: center;
			}

			input.largerCheckbox {
				transform: scale(1.8);
				left: 9px;
				position: relative;
				bottom: -5px;
			}

			.custom_data {
				text-transform: uppercase;
			}
		</style>
		<!-- End embedded font definitions -->

		<!-- Begin page background -->
		<div id="pg1Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
		<div id="pg1" style="-webkit-user-select: none;"><object width="909" height="1286" data="<?php echo $URL; ?>/1/1.svg" type="image/svg+xml" id="pdf1" style="width:909px; height:1286px; -moz-transform:scale(1); z-index: 0;"></object></div>
		<!-- End page background -->


		<!-- Begin text definitions (Positioned/styled in CSS) -->
		<div class="text-container">

			<span id="t33_1" class="s3 ek1_baslik">EK 1</span>
			<span id="t1_1" class="t s1_1">VİZEYE TABİ DENİZCİLER İÇİN VİZE TALEP FORMU </span>
			<span id="t2_1" class="t s2_1">RESMİ KULLANIM İÇİN </span>
			<span id="t3_1" class="t s2_1">DÜZENLEYEN : CNMAR DENİZCİLİK</span>
			<span id="t4_1" class="t s2_1">YETKİLİNİN ADI SOYADI / KODU: </span>
			<!-- <span id="t5_1" class="t s2_1"><img width="334" style="object-fit: contain;" height="90" src="<?php echo $personel_kasesi ?>"></span> -->
			<span id="t5_1" class="t s2_1"><?php echo $personel_adi; ?> / <?php echo $personel->acente_yetki_belge; ?></span>
			<span id="t6_1" class="t s2_1" style="bottom: 1155px;">ALICI : <span contenteditable="">PASAPORT KONTROL BÜRO AMİRLİĞİ</span></span>
			<span id="t9_1" class="t s2_1" style="bottom:1030px;">MAKAM : <span contenteditable="">İSTANBUL HAVALİMANI ŞUBE MÜDÜRLÜĞÜ </span></span>
			<span id="tb_1" class="t s2_1">DENİZCİ HAKKINDA BİLGİ </span>
			<span id="tc_1" class="t s2">BİREY <input type="checkbox" class="largerCheckbox" name="checkBox2" checked></span>
			<span id="td_1" class="t s2">GRUP LİDERİ <input style="left:6px; bottom:-3px" type="checkbox" class="largerCheckbox" name="checkBox2"></span>
			<span id="te_1" class="t s2">KAPALI GRUP <input style="left:6px; bottom:2px" type="checkbox" class="largerCheckbox" name="checkBox2"></span>
			<span id="tf_1" class="t s2_1">(kimlik tespiti için ekteki kayda bakınız) </span>

			<span id="tg_1" class="t s2_1">1A. SOYAD (LAR) </span>
			<span id="th_1" class="t s3_1"><?php c_form('lastname', 'twolines'); ?> </span>
			<span id="ti_1" class="t s2_1">1B. İLK İSİM (LER) </span>
			<span id="tj_1" class="t s3_1" style="bottom:885px; width: 180px;"><?php c_form('name', 'twolines'); ?> </span>
			<span id="tl_1" class="t s2_1">1C. UYRUK </span>
			<span id="tm_1" class="t s3_1"><?php c_form('nationolaity', 'twolines'); ?> </span>
			<span id="tn_1" class="t s2_1">1D. RÜTBE / DERECE </span>
			<span id="to_1" class="t s3"><?php c_form('rank', 'twolines') ?></span>
			<span id="tp_1" class="t s2_1">2A. DOĞUM YERİ </span>
			<span id="tq_1" class="t s3" style="bottom: 839px;"><?php c_form('placeofbirth', 'twolines') ?></span>
			<span id="ts_1" class="t s2_1">2B. DOĞUM TARİHİ: </span>
			<span id="tt_1" class="t s3"><?php c_form(unix_to_date($data->dateofbirth, 1)); ?></span>
			<span id="tu_1" class="t s2_1">3A. PASAPORT NUMARASI </span>
			<span id="tv_1" class="t s3"><?php c_form('passport_no') ?> </span>
			<span id="tw_1" class="t s2_1">4A. DENİZCİ CÜZDANI NUMARASI </span>
			<span id="tx_1" class="t s3_1"><?php c_form('seamanbook_no') ?> </span>
			<span id="ty_1" class="t s2_1">3B. VERİLİŞ TARİHİ </span>
			<span id="tz_1" class="t s3_1"><?php c_form(unix_to_date($data->pasaport_issued, 1)); ?></span>
			<span id="t10_1" class="t s2_1">4B. VERİLİŞ TARİHİ </span>
			<span id="t11_1" class="t s3_1"><?php c_form(unix_to_date($data->seamanbook_issued, 1)); ?></span>
			<span id="t12_1" class="t s2_1">3C. GEÇERLİLİK SÜRESİ </span>
			<span id="t13_1" class="t s3_1"><?php c_form(unix_to_date($data->pasaport_expiry, 1)); ?></span>
			<span id="t14_1" class="t s2_1">4C. GEÇERLİLİK SÜRESİ </span>
			<span id="t15_1" class="t s3_1"><?php c_form(unix_to_date($data->seamanbook_expiry, 1)); ?></span>
			<span id="t16_1" class="t s2_1">GEMİ VE DENİZCİLİK ACENTASI İLE İLGİLİ BİLGİ </span>
			<span id="t17_1" class="t s2_1">5. DENİZCİLİK ACENTASININ </span>
			<span id="t18_1" class="t s2_1">ADI </span>
			<span id="t19_1" class="t s3_1">CNMAR DENİZCİLİK ve LOJİSTİK HİZMETLERİ LTD. ŞTİ. </span>
			<span id="t1a_1" class="t s2_1">6A. GEMİNİN ADI </span>
			<span id="t1b_1" class="t s3_1"><?php c_form($ship->name, 'twolines') ?></span>
			<span id="t1c_1" class="t s2_1">6B. BANDIRASI </span>
			<span id="t1d_1" class="t s3_1" style="bottom:590px; width: 170px;"><?php c_form($ship->flag, 'twolines', 'style="width: 170px;"') ?> </span>
			<span id="t1d_1" class="t s3_1" contenteditable="" style="bottom:580px; width: 170px; text-align: center; text-transform: uppercase; font-size: 13px; left:712px;"><span class=""><?php echo $proje->geldigi_liman; ?></span></span>
			<span id="t1e_1" class="t s2_1">7A. GELİŞ TARİHİ </span>
			<span id="t1f_1" class="t s3_1"><?php c_form(unix_to_date($proje->eta, 1)) ?></span>
			<span id="t1g_1" class="t s2_1">7B. GEMİNİN MENŞEİ </span>
			<span id="t1h_1" class="t s2_1">8A. AYRILIŞ TARİHİ </span>
			<span id="t1i_1" class="t s3_1"><?php c_form(unix_to_date($proje->ets, 1)) ?></span>
			<span id="t1j_1" class="t s2_1">8B. GEMİNİN GİDECEĞİ YER </span>
			<span id="t1k_1" class="t s3_1"><?php $str = (trim($proje->gidecegi_liman)) ? $proje->gidecegi_liman : 'EMRE GÖRE';
											c_form($str) ?> </span>
			<span id="t1l_1" class="t s2_1">DENİZCİNİN DOLAŞIMI İLE İLGİLİ BİLGİ </span>
			<span id="t1m_1" class="t s2_1">9. DENİZCİNİN SON VARIŞ NOKTASI : </span>
			<span id="t1n_1" class="t s3_1" contenteditable="">İSTANBUL BOĞAZI </span>
			<span id="t1o_1" class="t s2_1">10. BAŞVURU NEDENLERİ </span>
			<span id="t1p_1" class="t s2_1">İŞE (GEMİYE) GİRİŞ <input style="left: 57px;bottom: -5px;" type="checkbox" <?php echo $checked_1; ?> class="largerCheckbox" name="checkBox2"></span>
			<span id="t1q_1" class="t s2_1">İŞTEN (GEMİDEN) AYRILMA <input style="left: 7px;bottom: -5px;" type="checkbox" <?php echo $checked_2; ?> class="largerCheckbox" name="checkBox2"></span>
			<span id="t1r_1" class="t s2_1">TRANSFER <input style="left: 11px;bottom: -5px;" type="checkbox" class="largerCheckbox" name="checkBox2"></span>
			<span id="t1s_1" class="t s2_1">AÇIKLAMA : </span>
			<span id="t1t_1" class="t s2_1">11.YOLCULUK VASITASI </span>
			<span id="t1u_1" class="t s2_1">ARABA <input style="left: 24px;bottom: -3px;" type="checkbox" class="largerCheckbox" name="checkBox2"> </span>
			<span id="t1v_1" class="t s2_1">TREN <input style="left: 56px;bottom: -4px;" type="checkbox" class="largerCheckbox" name="checkBox2"></span>
			<span id="t1w_1" class="t s2_1">UÇAK <input style="left: 36px;bottom: -4px;" type="checkbox" class="largerCheckbox" name="checkBox2" checked></span>
			<span id="t1x_1" class="t s2_1">12. TARIH: </span>
			<span id="t1y_1" class="t s2_1">GELİŞ: <input style="left: 27px;bottom: -3px;" type="checkbox" <?php echo $checked_1; ?> class="largerCheckbox" name="checkBox2"></span>
			<span id="t1z_1" class="t s2_1">GEÇİŞ : <input style="left: 10px;bottom: -2px;" type="checkbox" class="largerCheckbox" name="checkBox2"></span>
			<span id="t20_1" class="t s2_1">AYRILIŞ: <input style="left: 20px;bottom: -3px;" type="checkbox" <?php echo $checked_2; ?> class="largerCheckbox" name="checkBox2"></span>
			<span id="t21_1" class="t s2_1">ARABA (*) </span>
			<span id="t22_1" class="t s2_1">TREN(*) </span>
			<span id="t23_1" class="t s2_1">SİCİL NUMARASI: </span>
			<span id="t24_1" class="t s2_1">YOLCULUK GÜZERGAHI: </span>
			<span id="t25_1" class="t s2_1">UÇUŞ BİLGİLERİ : </span>
			<span id="t26_1" class="t s2_1">TARİH </span>
			<span id="t27_1" class="t s3_1"><?php c_form(unix_to_date($main_ucus->varis_tarihi, 1)) ?> </span>
			<span id="t28_1" class="t s2_1">SAAT </span>
			<span id="t29_1" class="t s3_1"><?php
											if ($data->status == 'EBMERKATION') {

												c_form($main_ucus->varis_ucus_time);
											} else {
												c_form($main_ucus->kalkis_ucus_time);
											}
											?></span>
			<span id="t2a_1" class="t s2_1">SEFER SAYISI: </span>
			<span id="t2b_1" class="t s3_1"><?php c_form($main_ucus->ucus_no) ?></span>
			<span id="t2c_1" style="bottom: 88px;" class="t s2_1">13. Denizcilik acentesi ya da gemi sahibi tarafından denizcinin ikamet sorumluluğu ve denizcinin eve dönüş masrafları <span>
					<span id="t2d_1" style="bottom:30px;left:21px" class="t s2_1">adına imzalanmış resmi deklarasyon </span>
		</div>
		<!-- End text definitions -->


	</div>
</body>

</html>