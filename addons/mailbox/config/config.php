<?php
// IMAP Ayarları
$mailbox_config = array(
    // Varsayılan mail hesabı
    'default' => array(
        'imap_host' => '{mail.evrimbakir.com:993/imap/ssl/novalidate-cert}INBOX',
        'username' => '<EMAIL>',
        'password' => 'eB14785963',
        'smtp_host' => 'mail.evrimbakir.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'smtp_auth' => true,
        'display_name' => 'Evrim Bakır'
    ),

    // Alternatif mail hesapları
    'accounts' => array(
        array(
            'id' => 1,
            'imap_host' => '{mail.evrimbakir.com:993/imap/ssl/novalidate-cert}INBOX',
            'username' => '<EMAIL>',
            'password' => 'eB14785963',
            'smtp_host' => 'mail.evrimbakir.com',
            'smtp_port' => 587,
            'smtp_secure' => 'tls',
            'smtp_auth' => true,
            'display_name' => 'Bilgi'
        ),
        array(
            'id' => 2,
            'imap_host' => '{mail.evrimbakir.com:993/imap/ssl/novalidate-cert}INBOX',
            'username' => '<EMAIL>',
            'password' => 'eB14785963',
            'smtp_host' => 'mail.evrimbakir.com',
            'smtp_port' => 587,
            'smtp_secure' => 'tls',
            'smtp_auth' => true,
            'display_name' => 'Destek'
        ),
        array(
            'id' => 3,
            'imap_host' => '{mail.cnmarship.com:993/imap/ssl/novalidate-cert}INBOX',
            'username' => '<EMAIL>',
            'password' => 'zcj65JTYsaC2pVMADJ3Z',
            'smtp_host' => 'mail.cnmarship.com',
            'smtp_port' => 587,
            'smtp_secure' => 'tls',
            'smtp_auth' => true,
            'display_name' => 'Cnmar Ship'
        )
    )
);

if (isset($_SESSION['active_mail_account'])) {
    $account_id = $_SESSION['active_mail_account'];
}
if ($account_id === 'default' || $account_id === null) {
    $account = $mailbox_config['default'];
} else {
    // Belirtilen hesabı bul
    foreach ($mailbox_config['accounts'] as $acc) {
        if ($acc['id'] == $account_id) {
            $account = $acc;
            break;
        }
    }

    // Hesap bulunamadıysa varsayılanı kullan
    if ($account === null) {
        $account = $mailbox_config['default'];
    }
}
$mailbox_config['default'] = $account;
// Geriye dönük uyumluluk için varsayılan hesap bilgilerini ana diziye kopyala
foreach ($mailbox_config['default'] as $key => $value) {
    $mailbox_config[$key] = $value;
}
