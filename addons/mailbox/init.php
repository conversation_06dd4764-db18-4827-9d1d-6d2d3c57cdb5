<?php

$FONT_LINK = WEB_DIR . '/theme/admin/demo6/assets/';

$MAILBOX_HEADER = '<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
  <title>CNMAR - Email</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="x-apple-disable-message-reformatting">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <style type="text/css">
    a, p, span, div, td {
      color:#002060;
      font-size:13px;
    }
    ul,ol,p{
      padding-top:8px;
      padding-bottom:8px;
      margin-top:8px;
      margin-bottom:8px;
    }
    
    a{
      color:#002060;
    }
    @font-face {
			font-family: \'Century Gothic\';
			src: url("' . $FONT_LINK . '/font/centurygothic/CenturyGothic.woff2") format("woff2"),
				url("' . $FONT_LINK . '/font/centurygothic/CenturyGothic.woff") format("woff");
			font-weight: normal;
			font-style: normal;
			font-display: swap;
		}

		@font-face {
			font-family: \'Britannic Bold\';
			src: url("' . $FONT_LINK . '/font/britannicbold/BritannicBold.woff2") format("woff2"),
				url("' . $FONT_LINK . '/font/britannicbold/BritannicBold.woff") format("woff");
			font-weight: normal;
			font-style: normal;
			font-display: swap;
		}

    @font-face {
      font-family: "Century Gothic";
      src: url("' . $FONT_LINK . '/font/centurygothicbold/CenturyGothic-Bold.woff2") format("woff2"),
        url("' . $FONT_LINK . '/font/centurygothicbold/CenturyGothic-Bold.woff") format("woff");
      font-style: normal;
      font-display: swap;
      font-weight: bold;
    }
      body, .selected_font, td, span, p, a,div{
        font-family: "Century Gothic",sans-serif !important;
      }
    
      .fw_bold {
        font-weight: normal;
        font-family: "Britannic Bold",sans-serif !important;
      }
  
      b, strong{ 
        font-weight: normal;
        font-family: "Britannic Bold",sans-serif !important;
      }
      figure{
        background-color: #efefef;
        padding: 10px;
      }
    }
  </style>
  <!--[if mso]>
    <style>
      .o_col { float: left; }
    </style>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  <![endif]-->
</head>';

$MAILBOX_BODY_BEFORE = '<body marginwidth="0" align="left" marginheight="0"
  style="margin-top: 0; background-color: #ffffff; margin-bottom: 0; padding-top: 0; padding-bottom: 0; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"
  offset="0" topmargin="0" leftmargin="0"><table border="0" align="left" width="1100" cellspacing="0" cellpadding="0" style="mso-table-lspace:0pt; border-collapse: collapse; margin: 0; mso-table-lspace:0pt; mso-table-rspace:0pt; padding: 0;">
                <tr>
                  <td style="padding: 0; text-align:left">';

$MAILBOX_BODY_AFTER = '</td></tr></table></body></html>';
// Config dosyasını dahil et
require_once dirname(__FILE__) . '/config/config.php';

// Aktif hesabı kontrol et veya varsayılanı ayarla
global $mailbox_config;

if (!isset($_SESSION['active_mail_account'])) {
    $_SESSION['active_mail_account'] = 'default';
}

// Hesap değiştirme işlemi
if (isset($_GET['switch_account'])) {
    $account_id = $_GET['switch_account'];

    // Varsayılan hesap kontrolü
    if ($account_id === 'default') {
        $_SESSION['active_mail_account'] = 'default';
    } else {
        // Alternatif hesaplar için ID kontrolü
        $found = false;
        foreach ($mailbox_config['accounts'] as $account) {
            if ($account['id'] == $account_id) {
                $_SESSION['active_mail_account'] = $account_id;
                $found = true;
                break;
            }
        }
        if (!$found) {
            // Hesap bulunamadı, varsayılan hesap kullanılıyor
        }
    }

    // Aynı sayfaya yönlendir (hesap değiştirme parametresi olmadan)
    $redirect_url = $_SERVER['REQUEST_URI'];
    $redirect_url = preg_replace('/[&?]switch_account=[^&]*/', '', $redirect_url);
    header("Location: " . $redirect_url);
    exit;
}

// Aktif hesap bilgisini al
$active_account = $_SESSION['active_mail_account'];

// Mailbox menüsünü oluştur
// $unread_count = get_unread_count(); . '(' . $unread_count . ')'
$mailbox = array();

// Hesap seçimi menüsü
$account_menu = '<div class="dropdown d-inline">';
$account_menu .= '<button class="btn btn-sm btn-light-info dropdown-toggle" type="button" id="accountDropdown" data-bs-toggle="dropdown" aria-expanded="false">';

if ($active_account === 'default') {
    $account_menu .= '<i class="ki-duotone ki-directbox-default fs-5 me-1"><span class="path1"></span><span class="path2"></span></i> ';
    $account_menu .= $mailbox_config['default']['display_name'] ?: $mailbox_config['default']['username'];
} else {
    foreach ($mailbox_config['accounts'] as $account) {
        if ($account['id'] == $active_account) {
            $account_menu .= '<i class="ki-duotone ki-directbox-default fs-5 me-1"><span class="path1"></span><span class="path2"></span></i> ';
            $account_menu .= $account['display_name'] ?: $account['username'];
            break;
        }
    }
}

$account_menu .= '</button>';
$account_menu .= '<ul class="dropdown-menu" aria-labelledby="accountDropdown">';

// Varsayılan hesap
$current_url = $_SERVER['REQUEST_URI'];
$switch_url = preg_replace('/[&?]switch_account=[^&]*/', '', $current_url);
$separator = (strpos($switch_url, '?') !== false) ? '&' : '?';

$account_menu .= '<li><a class="dropdown-item ' . ($active_account === 'default' ? 'active text-info' : '') . '" href="' . $switch_url . $separator . 'switch_account=default">';
$account_menu .= '<i class="ki-duotone ki-directbox-default fs-6 me-2"><span class="path1"></span><span class="path2"></span></i> ';
$account_menu .= $mailbox_config['default']['display_name'] ?: $mailbox_config['default']['username'];
$account_menu .= '</a></li>';

// Alternatif hesaplar
foreach ($mailbox_config['accounts'] as $account) {
    $account_menu .= '<li><a class="dropdown-item ' . ($active_account == $account['id'] ? 'active text-info' : '') . '" href="' . $switch_url . $separator . 'switch_account=' . $account['id'] . '">';
    $account_menu .= '<i class="ki-duotone ki-directbox-default fs-6 me-2"><span class="path1"></span><span class="path2"></span></i> ';
    $account_menu .= $account['display_name'] ?: $account['username'];
    $account_menu .= '</a></li>';
}

$account_menu .= '</ul></div>';

// Menü öğelerini ekle
$mailbox['mailbox/dashboard'] = array(_('dashboard'));
$mailbox['mailbox/inbox'] = array(_('Gelen Kutusu'));
$mailbox['mailbox/outbox'] = array(_('Giden Kutusu'));
$mailbox['mailbox/compose'] = array(_('Yeni Mail'));
$mailbox['mailbox/drafts'] = array(_('Taslaklar'));
$mailbox['mailbox/trash'] = array(_('Çöp Kutusu')); // Çöp Kutusu bağlantısı eklendi
$mailbox['mailbox/labels'] = array(_('Etiketler')); // Etiketler bağlantısı eklendi
$mailbox['mailbox/signatures'] = array(_('Imzalar'));
$mailbox['mailbox/templates'] = array(_('Hazır Mesajlar'));

// Ana menüye ekle
$addons_menu['mailbox'] = array(
    _('Mail Yönetimi'),
    '<i class="ki-duotone ki-message-text-2 fs-2">
        <span class="path1"></span>
        <span class="path2"></span>
        <span class="path3"></span>
    </i>',
    $mailbox
);
