<?php
// İstatistik değişkenlerini tanımla
// Gelen kutusu istatistikleri
$inbox_stats = array(
    'total' => 0,
    'unread' => 0
);

// Giden kutusu istatistikleri
$outbox_stats = array(
    'total' => 0
);

// Çöp kutusu istatistikleri
$trash_stats = array(
    'total' => 0
);

// Taslak istatistikleri
$draft_stats = array(
    'total' => 0
);

$recent_inbox = array('emails' => array());
$recent_outbox = array('emails' => array());

// Gelen kutusu istatistiklerini hesapla
try {
    // Okunmamış mail sayısını hesapla (bu fonksiyon kendi içinde bağlantı açıp kapatıyor)
    $inbox_stats['unread'] = get_unread_count();

    // Gelen kutusu istatistiklerini al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $inbox_result = get_email_list(null, 'INBOX', 1, 1000); // Tüm mailleri al
    $inbox_stats['total'] = $inbox_result['total'];

    // Son gelen mailleri al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $recent_inbox = get_email_list(null, 'INBOX', 1, 5); // Son 5 mail
} catch (Exception $e) {
    error_log("Gelen kutusu istatistikleri alınamadı: " . $e->getMessage());
}

// Giden kutusu istatistiklerini hesapla
try {
    // Giden kutusu istatistiklerini al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $outbox_result = get_sent_emails(null, 1, 1000); // Tüm mailleri al
    $outbox_stats['total'] = $outbox_result['total'];

    // Son gönderilen mailleri al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $recent_outbox = get_sent_emails(null, 1, 5); // Son 5 mail
} catch (Exception $e) {
    error_log("Giden kutusu istatistikleri alınamadı: " . $e->getMessage());
}

// Çöp kutusu istatistiklerini hesapla
try {
    // Çöp kutusu istatistiklerini al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $trash_result = get_trash_emails(null, 1, 1000); // Tüm mailleri al
    $trash_stats['total'] = $trash_result['total'];
} catch (Exception $e) {
    error_log("Çöp kutusu istatistikleri alınamadı: " . $e->getMessage());
}

// Taslak istatistiklerini hesapla
try {
    global $db, $user;
    $draft_stats['total'] = $db->get_var("SELECT COUNT(*) FROM mail_drafts WHERE user_id = " . (int) USER_LOGIN);
} catch (Exception $e) {
    error_log("Taslak istatistikleri alınamadı: " . $e->getMessage());
}
?>
<!--begin::Row-->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
    <!--begin::Col-->
    <?php /**
     * <div class="col-xl-3">
        <a href="<?php fn_menu_link('mailbox/compose'); ?>" class="card hover-elevate-up card-flush h-md-100 bg-light-info " style="">
            <!--begin::Card body-->
            <div class="card-body d-flex flex-column flex-center text-center">
                <!--begin::Illustrations-->
                <img class="mw-100 h-100px mb-7 mx-auto" src="<?php echo $s_w_tema; ?>/assets/media/illustrations/sigma-1/4.png">
                <!--end::Illustrations-->

                <!--begin::Heading-->
                <h4 class="text-white fw-bold text-uppercase">New Message</h4>
                <!--end::Heading-->
            </div>
            <!--end::Card body-->
        </a>
    </div>
     *  */ ?>
    <div class="col-xl-3">
        <!--begin::Card widget-->
        <a href="<?php fn_menu_link('mailbox/inbox'); ?>" class="card  hover-elevate-up card-flush h-md-100">
            <!--begin::Header-->
            <div class="card-header pt-5">
                <!--begin::Title-->
                <div class="card-title d-flex flex-column">
                    <!--begin::Amount-->

                    <span class="fs-2hx fw-bold text-dark me-2 lh-1" data-kt-countup="true" data-kt-countup-value="<?php echo $inbox_stats['total']; ?>" data-kt-countup-prefix="">0</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-gray-400 pt-1 fw-semibold fs-6">Toplam Mail</span>
                    <!--end::Subtitle-->
                </div>
                <!--end::Title-->
                <div class="card-toolbar">
                    <div class="symbol symbol-75px">
                        <span class="symbol-label bg-light-primary">
                            <i class="ki-duotone ki-directbox-default fs-1 text-primary">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </span>
                    </div>
                </div>
            </div>
            <!--end::Header-->
            <!--begin::Card body-->
            <div class="card-body d-flex flex-column justify-content-end  pe-0 bgi-no-repeat bgi-size-cover bgi-position-x-center " style="background-position: 100% 50%; background-image:url('<?php echo $s_w_tema; ?>/assets/media/stock/900x600/42.png')">
                <!--begin::Title-->
                <span class="fs-4 fw-bolder text-gray-800 d-block mb-2">Gelen Kutusu</span>
                <!--end::Title-->
            </div>
            <!--end::Card body-->
        </a>
        <!--end::Card widget-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col-xl-3">
        <!--begin::Card widget-->
        <a href="<?php fn_menu_link('mailbox/inbox'); ?>" class="card hover-elevate-up card-flush h-md-100">
            <!--begin::Header-->
            <div class="card-header pt-5">
                <!--begin::Title-->
                <div class="card-title d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bold text-dark me-2 lh-1"><?php echo $inbox_stats['unread']; ?></span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-gray-400 pt-1 fw-semibold fs-6">Okunmamış Mail</span>
                    <!--end::Subtitle-->
                </div>
                <!--end::Title-->
                <div class="card-toolbar">
                    <div class="symbol symbol-75px">
                        <span class="symbol-label bg-danger bg-opacity-10">
                            <i class="ki-duotone ki-message-text fs-1 text-danger">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </i>
                        </span>
                    </div>
                </div>
            </div>
            <!--end::Header-->
            <!--begin::Card body-->
            <div class="card-body d-flex flex-column justify-content-end  pe-0 bgi-no-repeat bgi-size-cover bgi-position-x-center " style="background-position: 100% 50%; background-image:url('<?php echo $s_w_tema; ?>/assets/media/stock/900x600/42.png')">
                <!--begin::Title-->
                <span class="fs-4 fw-bolder text-gray-800 d-block mb-2">Okunmamış</span>
                <!--end::Title-->
            </div>
            <!--end::Card body-->
        </a>
        <!--end::Card widget-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col-xl-3">
        <!--begin::Card widget-->
        <a href="<?php fn_menu_link('mailbox/outbox'); ?>" class="card hover-elevate-up card-flush h-md-100">
            <!--begin::Header-->
            <div class="card-header pt-5">
                <!--begin::Title-->
                <div class="card-title d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bold text-dark me-2 lh-1"><?php echo $outbox_stats['total']; ?></span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-gray-400 pt-1 fw-semibold fs-6">Gönderilen Mail</span>
                    <!--end::Subtitle-->
                </div>
                <!--end::Title-->
                <div class="card-toolbar">
                    <div class="symbol symbol-75px">
                        <span class="symbol-label bg-light-success">
                            <i class="ki-duotone ki-send fs-1 text-success">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </span>
                    </div>
                </div>
            </div>
            <!--end::Header-->
            <!--begin::Card body-->
            <div class="card-body d-flex flex-column justify-content-end  pe-0 bgi-no-repeat bgi-size-cover bgi-position-x-center " style="background-position: 100% 50%; background-image:url('<?php echo $s_w_tema; ?>/assets/media/stock/900x600/42.png')">
                <!--begin::Title-->
                <span class="fs-4 fw-bolder text-gray-800 d-block mb-2">Giden Kutusu</span>
                <!--end::Title-->
            </div>
            <!--end::Card body-->
        </a>
        <!--end::Card widget-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col-xl-3">
        <!--begin::Card widget-->
        <a href="<?php fn_menu_link('mailbox/drafts'); ?>" class="card hover-elevate-up card-flush h-md-100">
            <!--begin::Header-->
            <div class="card-header pt-5">
                <!--begin::Title-->
                <div class="card-title d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bold text-dark me-2 lh-1"><?php echo $draft_stats['total']; ?></span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-gray-400 pt-1 fw-semibold fs-6">Taslak</span>
                    <!--end::Subtitle-->
                </div>
                <!--end::Title-->
                <div class="card-toolbar">
                    <div class="symbol symbol-75px">
                        <span class="symbol-label bg-light-warning">
                            <i class="ki-duotone ki-notepad fs-1 text-warning">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                                <span class="path4"></span>
                                <span class="path5"></span>
                            </i>
                        </span>
                    </div>
                </div>
            </div>
            <!--end::Header-->
            <!--begin::Card body-->
            <div class="card-body d-flex flex-column justify-content-end  pe-0 bgi-no-repeat bgi-size-cover bgi-position-x-center " style="background-position: 100% 50%; background-image:url('<?php echo $s_w_tema; ?>/assets/media/stock/900x600/42.png')">
                <!--begin::Title-->
                <span class="fs-4 fw-bolder text-gray-800 d-block mb-2">Taslaklar</span>
                <!--end::Title-->
            </div>
            <!--end::Card body-->
        </a>
        <!--end::Card widget-->
    </div>
    <!--end::Col-->
</div>
<!--end::Row-->

<!--begin::Row-->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
    <!--begin::Col-->
    <div class="col-xl-6">
        <!--begin::Card-->
        <div class="card card-flush h-md-100">
            <!--begin::Card header-->
            <div class="card-header pt-7">
                <!--begin::Title-->
                <h3 class="card-title align-items-start flex-column">
                    <span class="card-label fw-bold text-gray-800">Son Gelen Mailler</span>
                    <span class="text-gray-400 mt-1 fw-semibold fs-6">Son 5 gelen mail</span>
                </h3>
                <!--end::Title-->
                <!--begin::Toolbar-->
                <div class="card-toolbar">
                    <a href="<?php fn_menu_link('mailbox/inbox'); ?>" class="btn btn-sm btn-light">Tümünü Gör</a>
                </div>
                <!--end::Toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-5">
                <!--begin::Table-->
                <div class="table-responsive">
                    <table class="table table-sm table-hover table-row-dashed table-row-gray-300 align-middle gs-0 gy-2">
                        <thead>
                            <tr class="fw-bold text-muted">
                                <th class="min-w-150px">Konu</th>
                                <th class="min-w-140px">Gönderen</th>
                                <th class="min-w-120px">Tarih</th>
                                <th class="min-w-100px text-end">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recent_inbox['emails'])): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-5">
                                        <div class="text-muted fs-6 mb-3">
                                            <i class="ki-duotone ki-message-text-2 fs-2x mb-3 d-block">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                            Gelen kutunuzda hiç mail bulunmuyor.
                                        </div>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recent_inbox['emails'] as $email): ?>
                                    <tr <?php echo $email['seen'] ? '' : 'class="fw-bold"'; ?>>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-30px me-3">
                                                    <span class="symbol-label bg-light-primary">
                                                        <i class="ki-duotone ki-message-text fs-3 text-primary">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                            <span class="path3"></span>
                                                        </i>
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-start flex-column">
                                                    <a href="<?php fn_menu_link('mailbox/view&id=' . $email['id']); ?>" class="text-dark fw-bold text-hover-primary mb-1 fs-6"><?php echo $email['subject']; ?></a>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo $email['from']; ?></td>
                                        <td><?php echo $email['date']; ?></td>
                                        <td class="text-end">
                                            <a href="<?php fn_menu_link('mailbox/view&id=' . $email['id']); ?>" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1">
                                                <i class="ki-duotone ki-eye fs-2">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                    <span class="path4"></span>
                                                </i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <!--end::Table-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col-xl-6">
        <!--begin::Card-->
        <div class="card card-flush h-md-100">
            <!--begin::Card header-->
            <div class="card-header pt-7">
                <!--begin::Title-->
                <h3 class="card-title align-items-start flex-column">
                    <span class="card-label fw-bold text-gray-800">Son Gönderilen Mailler</span>
                    <span class="text-gray-400 mt-1 fw-semibold fs-6">Son 5 gönderilen mail</span>
                </h3>
                <!--end::Title-->
                <!--begin::Toolbar-->
                <div class="card-toolbar">
                    <a href="<?php fn_menu_link('mailbox/outbox'); ?>" class="btn btn-sm btn-light">Tümünü Gör</a>
                </div>
                <!--end::Toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-5">
                <!--begin::Table-->
                <div class="table-responsive">
                    <table class="table table-sm table-hover table-row-dashed table-row-gray-300 align-middle gs-0 gy-2">
                        <thead>
                            <tr class="fw-bold text-muted">
                                <th class="min-w-150px">Konu</th>
                                <th class="min-w-140px">Alıcı</th>
                                <th class="min-w-120px">Tarih</th>
                                <th class="min-w-100px text-end">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recent_outbox['emails'])): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-5">
                                        <div class="text-muted fs-6 mb-3">
                                            <i class="ki-duotone ki-message-text-2 fs-2x mb-3 d-block">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                            Giden kutunuzda hiç mail bulunmuyor.
                                        </div>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recent_outbox['emails'] as $email): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-30px me-3">
                                                    <span class="symbol-label bg-light-success">
                                                        <i class="ki-duotone ki-send fs-2 text-success">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                        </i>
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-start flex-column">
                                                    <a href="<?php fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=sent'); ?>" class="text-dark fw-bold text-hover-primary mb-1 fs-6"><?php echo $email['subject']; ?></a>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo $email['to']; ?></td>
                                        <td><?php echo $email['date']; ?></td>
                                        <td class="text-end">
                                            <a href="<?php fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=sent'); ?>" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1">
                                                <i class="ki-duotone ki-eye fs-2">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                    <span class="path4"></span>
                                                </i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <!--end::Table-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Col-->
</div>
<!--end::Row-->