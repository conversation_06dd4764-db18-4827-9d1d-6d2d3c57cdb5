<?php

// Yönlendirme bağlantısını belirle
if (isset($_GET['folder'])) {
    if ($_GET['folder'] == 'sent') {
        $r_link = 'mailbox/outbox';
    } elseif ($_GET['folder'] == 'trash') {
        $r_link = 'mailbox/trash';
    } else {
        $r_link = 'mailbox/inbox';
    }
} else {
    $r_link = 'mailbox/inbox';
}

try {
    // Gerekli parametreleri kontrol et
    if (!isset($_GET['id']) || !isset($_GET['part'])) {
        throw new Exception('Geçersiz indirme isteği');
    }

    // IMAP bağlantısını aç
    $mailbox = mailbox_connect();

    // Klasör parametresine göre uygun klasöre geç
    if (isset($_GET['folder'])) {
        @imap_close($mailbox);
        $host = parse_url($mailbox_config['imap_host'], PHP_URL_HOST);

        if ($_GET['folder'] == 'sent') {
            // Sent klasörüne bağlan
            $possible_folders = array('Sent', 'Sent Items', 'Gönderilmiş Öğeler');

            foreach ($possible_folders as $folder) {
                $mailbox = @imap_open(
                    $host . ':993/imap/ssl/novalidate-cert}' . $folder,
                    $mailbox_config['username'],
                    $mailbox_config['password']
                );
                if ($mailbox)
                    break;
            }

            if (!$mailbox) {
                throw new Exception('Giden kutusu klasörüne erişilemiyor: ' . imap_last_error());
            }
        } elseif ($_GET['folder'] == 'trash') {
            // Çöp kutusu klasörüne bağlan
            $possible_folders = array('Trash', 'Deleted Items', 'Çöp Kutusu', 'Deleted');

            foreach ($possible_folders as $folder) {
                $mailbox = @imap_open(
                    $host . ':993/imap/ssl/novalidate-cert}' . $folder,
                    $mailbox_config['username'],
                    $mailbox_config['password']
                );
                if ($mailbox)
                    break;
            }

            if (!$mailbox) {
                throw new Exception('Çöp kutusu klasörüne erişilemiyor: ' . imap_last_error());
            }
        }
    }

    // Parametreleri al
    $email_id = (int) $_GET['id'];
    $part_num = (int) $_GET['part'];

    // Eki indir
    $attachment = download_attachment($mailbox, $email_id, $part_num);
    imap_close($mailbox);

    if (!$attachment) {
        throw new Exception('Ek indirilemedi');
    }

    // Dosya adı, MIME tipi ve geçici dosya yolunu al
    $filename = $attachment['filename'];
    $mime_type = $attachment['mime_type'];
    $temp_file = $attachment['temp_file'];

    // Dosya boyutunu kontrol et
    $file_size = filesize($temp_file);

    // Debug bilgisi
    error_log('Downloading file: ' . $filename);
    error_log('MIME type: ' . $mime_type);
    error_log('Temp file: ' . $temp_file);
    error_log('File size: ' . $file_size);

    // Dosya header'ını kontrol et
    $file_handle = fopen($temp_file, 'rb');
    if ($file_handle) {
        $header_bytes = fread($file_handle, 20);
        fclose($file_handle);
        $header_hex = bin2hex($header_bytes);
        error_log('File header (hex): ' . $header_hex);
        error_log('File header (first 10 chars): ' . substr($header_bytes, 0, 10));
    }

    // Debug mode için dosyayı browser'da göster
    if (isset($_GET['debug']) && $_GET['debug'] == '1') {
        echo "<h3>Debug Information</h3>";
        echo "<p><strong>Filename:</strong> " . htmlspecialchars($filename) . "</p>";
        echo "<p><strong>MIME Type:</strong> " . htmlspecialchars($mime_type) . "</p>";
        echo "<p><strong>File Size:</strong> " . $file_size . " bytes</p>";
        echo "<p><strong>File Header (hex):</strong> " . (isset($header_hex) ? $header_hex : 'N/A') . "</p>";
        echo "<p><strong>Temp File:</strong> " . htmlspecialchars($temp_file) . "</p>";

        if (strpos($mime_type, 'image/') === 0) {
            echo "<p><strong>Image Preview:</strong></p>";
            echo "<img src='data:" . $mime_type . ";base64," . base64_encode(file_get_contents($temp_file)) . "' style='max-width: 500px; max-height: 500px;' />";
        }

        @unlink($temp_file);
        exit;
    }

    // Dosya boşsa hata ver
    if ($file_size === 0) {
        @unlink($temp_file);
        throw new Exception('Dosya boş veya bozuk');
    }

    // Tüm çıktı tamponlarını temizle
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Dosya indirme header'larını ayarla
    header('Content-Description: File Transfer');
    header('Content-Type: ' . $mime_type);
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    header('Content-Length: ' . $file_size);

    // RFC 5987 ile uyumlu dosya adı kodlaması
    $filename_encoded = rawurlencode($filename);
    header("Content-Disposition: attachment; filename*=UTF-8''" . $filename_encoded);

    // Fallback için basit filename header'ı da ekle
    header('Content-Disposition: attachment; filename="' . addslashes($filename) . '"');

    // Dosyayı chunk'lar halinde gönder (büyük dosyalar için)
    $handle = fopen($temp_file, 'rb');
    if ($handle) {
        while (!feof($handle)) {
            echo fread($handle, 8192); // 8KB chunk'lar
            flush();
        }
        fclose($handle);
    } else {
        // Fallback: readfile kullan
        readfile($temp_file);
    }

    // Geçici dosyayı sil
    @unlink($temp_file);

    exit;
} catch (Exception $e) {
    error_log('Dosya indirme hatası: ' . $e->getMessage());
    fn_set_notice($e->getMessage(), 'E');
    fn_redirect(fn_menu_link($r_link, 1));
}
