<?php

/**
 * Test file for Crew Member Text Parser
 * Bu dosya crew member text parser fonksiyonunu test etmek için kullan<PERSON>l<PERSON>r
 */

// Include the main file to get the functions
require_once 'tomove.php';

// Test data - <PERSON><PERSON><PERSON><PERSON>ının verdiği örnek metin
$test_text = 'ON-SIGNERS: 7
****************
Name: ABANILLA,JOHN ALBERT ANSELMO     Rank: 2ND OFFICER
Passport:P0424228B   Issued:26/Jan/2019       Expiry: 25/Jan/2029      
Seaman Book: C1369525          Issued:11/Feb/2019     Expiry: 11/Feb/2029
Nationality: Filipino      Date of Birth: 15/Feb/1992        Place of Birth: CEBU CITY
                                

Name: DENISOV,ROMAN          Rank: CHIEF ENGINEER
Passport:*********    Issued:12/Aug/2023      Expiry: 12/Aug/2033     
Seaman Book: RUS0537177     Issued:28/Dec/2021     Expiry: 27/Dec/2026
Nationality: Russian     Date of Birth: 08/Feb/1976        Place of Birth: USSR
                                

Name: ONG,ANTHONY JAMES SILVERIO           Rank: ABLE BODIED SEAMAN
Passport:P4306977B   Issued:07/Jan/2020       Expiry: 06/Jan/2030      
Seaman Book: A0125617           Issued:08/Mar/2021     Expiry: 08/Mar/2031
Nationality: Filipino      Date of Birth: 13/Jul/1976           Place of Birth: KALIBO, AKLAN
                                

 ON-SIGNERS FLIGHT DETAILS:
7RXKTJ
QLIC02202/0002NS/11JUN25
  1.DENISOV/ROMAN MR
  2  PC 399 N 13JUN 5 LEDSAW HK1  0255 0650  13JUN  E  PC/1XRVWE
  3  PC2082 E 13JUN 5 SAWCOV HK1  1050 1220  13JUN  E  PC/1XRVWE

OFF-SIGNERS: 3
****************
Name: YOLA,GERALD XAVIER VILLAFLOR         Rank:2ND OFFICER
Passport: P7278580A  Issued: 23/May/2018   Expiry: 22/May/2028    
Seaman Book: C1262794          Issued: 06/Jun/2018      Expiry: 03/Jun/2028
Nationality: Filipino      Date of Birth: 22/Apr/1979         Place of Birth: BACOLOD CITY
                                

Name: SAVENKO,VIKTOR           Rank: CHIEF ENGINEER
Passport: *********   Issued: 11/Sep/2023    Expiry: 11/Sep/2028    
Seaman Book: RUS0538124     Issued: 01/Apr/2022     Expiry: 31/Mar/2027
Nationality: Russian     Date of Birth: 15/Apr/1962         Place of Birth: USSR
                                

Name: GARIEGA,FLORITO GENONA    Rank: ABLE BODIED SEAMAN
Passport: P1732043B  Issued: 25/May/2019   Expiry: 24/May/2029    
Seaman Book: C1356582          Issued: 11/Jun/2019      Expiry: 11/Jun/2029
Nationality: Filipino      Date of Birth: 03/Aug/1976        Place of Birth: GUIMBAL, ILOILO';

// Test attachments - Gerçek format ile
$test_attachments = [
    [
        'filename' => 'ABANILLA JOHN ALBERT -PP (PASSPORT) 1.jpg',
        'path' => '/home/<USER>/domains/cnmarportal.com/public_html/gp/addons/mailbox/tmp_move/ABANILLA_JOHN_ALBERT_-PP__PASSPORT__1_6.jpg',
        'size' => 312364
    ],
    [
        'filename' => 'ABANILLA JOHN ALBERT -SBK (Philippines) 1.pdf',
        'path' => '/home/<USER>/domains/cnmarportal.com/public_html/gp/addons/mailbox/tmp_move/ABANILLA_JOHN_ALBERT_-SBK__Philippines__1_6.pdf',
        'size' => 355900
    ],
    [
        'filename' => 'DENISOV ROMAN -PP (PASSPORT) 1.jpg',
        'path' => '/tmp/denisov_passport.jpg',
        'size' => 200000
    ]
];

// Test the parser
echo "<h1>Crew Member Text Parser Test</h1>";
echo "<h2>Test Metni:</h2>";
echo "<pre>" . htmlspecialchars($test_text) . "</pre>";

echo "<h2>Test Ekleri:</h2>";
echo "<pre>" . print_r($test_attachments, true) . "</pre>";

echo "<h2>Parsing Sonucu:</h2>";
$result = parse_crew_member_text($test_text, $test_attachments);

// Debug output
debug_name_matching($result, $test_attachments);

echo "<h2>Detaylı Sonuç:</h2>";
echo "<pre>" . print_r($result, true) . "</pre>";

echo "<h2>Özet İstatistikler:</h2>";
echo "<ul>";
echo "<li>ON-SIGNERS Sayısı: " . $result['statistics']['on_signers_count'] . "</li>";
echo "<li>OFF-SIGNERS Sayısı: " . $result['statistics']['off_signers_count'] . "</li>";
echo "<li>Toplam Ayrıştırılan ON-SIGNERS: " . count($result['on_signers']) . "</li>";
echo "<li>Toplam Ayrıştırılan OFF-SIGNERS: " . count($result['off_signers']) . "</li>";
echo "<li>Eşleşen Ek Dosya Sayısı: " . $result['statistics']['total_attachments_matched'] . "</li>";
echo "</ul>";

echo "<h2>Fonksiyon Kullanımı:</h2>";
echo "<pre>";
echo "// Temel kullanım:
\$parsed_data = parse_crew_member_text(\$selected_text, \$processed_attachments);

// Sadece metin ayrıştırma (ek dosya olmadan):
\$parsed_data = parse_crew_member_text(\$selected_text);

// Sonuçlara erişim:
\$on_signers = \$parsed_data['on_signers'];
\$off_signers = \$parsed_data['off_signers'];
\$flight_details = \$parsed_data['on_signers_flight_details'];

// Her crew member için:
foreach (\$on_signers as \$crew) {
    echo \$crew['name'] . ' - ' . \$crew['rank'];
    echo 'Passport: ' . \$crew['passport']['number'];
    echo 'Seaman Book: ' . \$crew['seaman_book']['number'];
    
    // Ek dosyalar
    foreach (\$crew['attachments'] as \$attachment) {
        echo \$attachment['type'] . ': ' . \$attachment['filename'];
    }
}";
echo "</pre>";

// Test Format 2
echo "<hr><h1>Format 2 Test (ONSIGNER/OFFSIGNER)</h1>";

$test_text_format_2 = 'ONSIGNER

Surname: MAJICA
Name: VOJISLAV
Rank: MASTER
Nationality: Croatian
DOB: 01.08.1968
POB: RIJEKA
Mobile Phone: +38598443816
Email: <EMAIL>
Passport No.:*********
Passport Issue Place:Rijeka
Passport Issue Date:07.11.2016
Passport Expiry Date:07.11.2026
Seaman\'s Book No.:00103934
Seaman\'s Book Issue Date:14.01.2021
Seaman\'s Book Expiry Date:14.01.2031

Surname: PROJILLO
Name: JERIVIC HENRY
Rank: THIRD ENGINEER
Nationality: Filipino
DOB: 29.12.1979
POB: OZAMIS CITY
Mobile Phone: +639686434714
Email:  <EMAIL>
Passport No.:P1318382B
Passport Issue Place:DFA MANILA
Passport Issue Date:02.04.2019
Passport Expiry Date:02.04.2029
Seaman\'s Book No.:C1464672
Seaman\'s Book Issue Date:29.03.2019
Seaman\'s Book Expiry Date:26.03.2029

OFFSIGNER

Surname: MATEI
Name: RADU
Rank: MASTER
Nationality: Romanian
DOB: 10.04.1979
POB: CONSTANTA
Mobile Phone: + 40 723 664624
Email: <EMAIL>
Passport No.:*********
Passport Issue Place:CONSTANTA
Passport Issue Date:13.01.2021
Passport Expiry Date:13.01.2031
Seaman\'s Book No.:1500ct
Seaman\'s Book Issue Place:CONSTANTA
Seaman\'s Book Issue Date:16.01.2017
Seaman\'s Book Expiry Date:05.07.2027';

echo "<h2>Format 2 Test Metni:</h2>";
echo "<pre>" . htmlspecialchars($test_text_format_2) . "</pre>";

echo "<h2>Format Tespiti:</h2>";
$detected_format = detect_text_format($test_text_format_2);
echo "<p><strong>Tespit Edilen Format:</strong> " . htmlspecialchars($detected_format) . "</p>";

// Format 2 için test attachments
$test_attachments_format_2 = [
    [
        'filename' => 'MAJICA VOJISLAV -PP (PASSPORT) 1.jpg',
        'path' => '/tmp/majica_passport.jpg',
        'size' => 200000
    ],
    [
        'filename' => 'PROJILLO JERIVIC -SBK (Philippines) 1.pdf',
        'path' => '/tmp/projillo_seaman.pdf',
        'size' => 300000
    ],
    [
        'filename' => 'MATEI RADU -PP (PASSPORT) 1.jpg',
        'path' => '/tmp/matei_passport.jpg',
        'size' => 250000
    ]
];

echo "<h2>Format 2 Test Attachments:</h2>";
echo "<pre>" . print_r($test_attachments_format_2, true) . "</pre>";

echo "<h2>Format 2 Parsing Sonucu:</h2>";
$result_format_2 = parse_crew_member_text($test_text_format_2, $test_attachments_format_2);

echo "<h3>Özet İstatistikler:</h3>";
echo "<ul>";
echo "<li>ON-SIGNERS Sayısı: " . $result_format_2['statistics']['on_signers_count'] . "</li>";
echo "<li>OFF-SIGNERS Sayısı: " . $result_format_2['statistics']['off_signers_count'] . "</li>";
echo "<li>Toplam Ayrıştırılan ON-SIGNERS: " . count($result_format_2['on_signers']) . "</li>";
echo "<li>Toplam Ayrıştırılan OFF-SIGNERS: " . count($result_format_2['off_signers']) . "</li>";
echo "</ul>";

echo "<h3>Detaylı Sonuç:</h3>";
echo "<pre>" . print_r($result_format_2, true) . "</pre>";
