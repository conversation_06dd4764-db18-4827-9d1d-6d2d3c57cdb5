<?php

// GET parametrelerini al
$message_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$folder = isset($_GET['folder']) ? $_GET['folder'] : 'inbox';
$reply_all = isset($_GET['reply_all']) ? (bool) $_GET['reply_all'] : false;

// Aktif hesap ID'sini al
$account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

$mailbox = mailbox_connect($account_id);

// Mesajı al
$message = get_email_content($mailbox, $message_id);
if (!$message) {
    die('Mesaj bulunamadı');
}

// Kullanıcının imzalarını al
$signatures = get_signatures();
$default_signature = get_default_signature();

// Hazır mesajları al
$templates = get_templates();

// Mail hesaplarını al
global $mailbox_config;

// Tümün<PERSON> yanıtla için CC'leri hazırla
$cc_list = array();
if ($reply_all) {
    global $mailbox_config;

    // Orijinal maildeki CC'leri al
    if (!empty($message['cc'])) {
        if (is_array($message['cc'])) {
            foreach ($message['cc'] as $cc_address) {
                // Kendimizi CC'ye eklemeyelim
                if ($cc_address != $mailbox_config['username']) {
                    $cc_list[] = $cc_address;
                }
            }
        } elseif ($message['cc'] != $mailbox_config['username']) {
            $cc_list[] = $message['cc'];
        }
    }

    // Orijinal maildeki alıcıları CC'ye ekle (gönderen hariç)
    if (!empty($message['to'])) {
        if (is_array($message['to'])) {
            foreach ($message['to'] as $to_address) {
                // Gönderen ve kendimizi CC'ye eklemeyelim
                if ($to_address != $message['from'] && $to_address != $mailbox_config['username']) {
                    $cc_list[] = $to_address;
                }
            }
        } elseif ($message['to'] != $message['from'] && $message['to'] != $mailbox_config['username']) {
            $cc_list[] = $message['to'];
        }
    }
}

// AJAX isteği kontrolü
$is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1 || isset($_GET['ajax']) && $_GET['ajax'] == 1;

// POST işlemi kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $reply_content = isset($_POST['reply_content']) ? trim($_POST['reply_content']) : '';

    // Hata kontrolü
    if ($message_id <= 0) {
        die(json_encode(['error' => 'Geçersiz mesaj ID']));
    }

    // CC ve BCC adreslerini al
    $cc = isset($_POST['cc']) ? $_POST['cc'] : '';
    $bcc = isset($_POST['bcc']) ? $_POST['bcc'] : '';

    // CC ve BCC adreslerini dizi haline getir
    $cc_array = array();
    if (!empty($cc)) {
        $cc_array = array_map('trim', explode(',', $cc));
        // Boş değerleri filtrele
        $cc_array = array_filter($cc_array, function ($value) {
            return !empty($value);
        });
    }

    $bcc_array = array();
    if (!empty($bcc)) {
        $bcc_array = array_map('trim', explode(',', $bcc));
        // Boş değerleri filtrele
        $bcc_array = array_filter($bcc_array, function ($value) {
            return !empty($value);
        });
    }

    // Hata ayıklama için
    error_log("CC: " . print_r($cc_array, true));
    error_log("BCC: " . print_r($bcc_array, true));

    // İmza ID'sini al
    $signature_id = isset($_POST['signature_id']) ? (int) $_POST['signature_id'] : null;

    // Hesap ID'sini al
    $account_id = isset($_POST['account_id']) && !empty($_POST['account_id']) ? (int) $_POST['account_id'] : null;

    // Mail gönder
    try {
        $result = mailbox_reply(
            $mailbox,
            $message_id,
            $message['from'],
            'Re: ' . $message['subject'],
            $reply_content,
            array(), // Ekler
            $signature_id,
            $cc_array,
            $bcc_array,
            $reply_all,
            $account_id
        );
    } catch (Exception $e) {
        error_log("Reply Error: " . $e->getMessage());
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }

    // Sonucu döndür
    header('Content-Type: application/json');
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'Mail başarıyla gönderildi.']);
    } else {
        echo json_encode(['success' => false, 'error' => 'Mail gönderilemedi.']);
    }
    exit;
}

// AJAX isteği değilse HTML çıktısı göster
if (!$is_ajax) {
    // GET isteği için arayüz gösterimi
    if ($message_id <= 0) {
        header('Location: mailbox.php');
        exit;
    }

    // Orijinal mesajı temizle ve formatla
    $original_content = clean_previous_email($message['body']);
?>
    <div class="d-flex flex-column flex-lg-row">
        <?php
        // Aside menüyü dahil et
        include(dirname(__FILE__) . '/includes/mailbox_aside.php');
        ?>
        <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Yanıtla: <?php echo htmlspecialchars($message['subject']); ?></h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Kimden:</strong> <?php echo htmlspecialchars($message['from']); ?><br>
                        <strong>Tarih:</strong> <?php echo htmlspecialchars($message['date']); ?>
                    </div>
                    <form id="replyForm" method="post" action="<?php fn_menu_link('mailbox/reply&id=' . $message_id . (isset($_GET['reply_all']) ? '&reply_all=1' : '') . (isset($_GET['folder']) && $_GET['folder'] == 'sent' ? '&folder=sent' : '') . '&ajax=1'); ?>" data-redirect="<?php fn_menu_link('mailbox/' . (isset($_GET['folder']) && $_GET['folder'] == 'sent' ? 'outbox' : 'inbox')); ?>">
                        <input type="hidden" name="message_id" value="<?php echo $message_id; ?>">
                        <input type="hidden" name="folder" value="<?php echo htmlspecialchars($folder); ?>">

                        <div class="mb-3">
                            <label class="form-label">Alıcı:</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($message['from']); ?>" readonly>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex align-items-center justify-content-between mb-2">
                                <label class="form-label mb-0">CC:</label>
                                <a href="#" class="text-muted text-hover-primary" id="toggleCc">
                                    <?php if (empty($cc_list)): ?>CC Ekle<?php else: ?>CC Gizle<?php endif; ?>
                                </a>
                            </div>
                            <div id="ccField" class="<?php echo empty($cc_list) ? 'd-none' : ''; ?>">
                                <input type="text" class="form-control" name="cc" value="<?php echo htmlspecialchars(implode(', ', $cc_list)); ?>" placeholder="Örnek: <EMAIL>, <EMAIL>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex align-items-center justify-content-between mb-2">
                                <label class="form-label mb-0">BCC:</label>
                                <a href="#" class="text-muted text-hover-primary" id="toggleBcc">BCC Ekle</a>
                            </div>
                            <div id="bccField" class="d-none">
                                <input type="text" class="form-control" name="bcc" placeholder="Örnek: <EMAIL>, <EMAIL>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reply_content" class="form-label">Yanıtınız:</label>
                            <textarea class="form-control ckeditor_mailbox border-1" id="reply_content" name="reply_content" rows="10"><?php echo $draft ? $draft->body : ''; ?></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Gönderen Hesap</label>
                                <select class="form-select" name="account_id">
                                    <option value="" <?php echo ($_SESSION['active_mail_account'] === 'default') ? 'selected' : ''; ?>>Varsayılan (<?php echo $mailbox_config['default']['username']; ?>)</option>
                                    <?php foreach ($mailbox_config['accounts'] as $account): ?>
                                        <option value="<?php echo $account['id']; ?>" <?php echo ($_SESSION['active_mail_account'] == $account['id']) ? 'selected' : ''; ?>>
                                            <?php echo isset($account['display_name']) ? $account['display_name'] . ' (' . $account['username'] . ')' : $account['username']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">İmza Seç</label>
                                <select class="form-select" name="signature_id">
                                    <option value="">Seçiniz (Varsayılan kullanılacak)</option>
                                    <?php foreach ($signatures as $signature): ?>
                                        <option value="<?php echo $signature->id; ?>" <?php echo ($default_signature && $default_signature->id == $signature->id) ? 'selected' : ''; ?>>
                                            <?php echo $signature->name; ?> <?php echo $signature->is_default ? '(Varsayılan)' : ''; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (empty($signatures)): ?>
                                    <div class="form-text text-muted">
                                        Henüz hiç imza eklenmemiş. <a href="<?php fn_menu_link('mailbox/signatures'); ?>">Buradan</a> yeni imza ekleyebilirsiniz.
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Hazır Mesaj Seç</label>
                                <select class="form-select" id="template_select">
                                    <option value="">Seçiniz</option>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?php echo $template->id; ?>"><?php echo $template->name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (empty($templates)): ?>
                                    <div class="form-text text-muted">
                                        Henüz hiç hazır mesaj eklenmemiş. <a href="<?php fn_menu_link('mailbox/templates'); ?>">Buradan</a> yeni hazır mesaj ekleyebilirsiniz.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Orijinal Mesaj:</label>
                            <div class="border p-3 bg-light">
                                <div class="email-quote">
                                    <div class="email-quote-header">
                                        <strong>Orijinal Mesaj</strong><br>
                                        <small>Kimden: <?php echo htmlspecialchars($message['from']); ?><br>
                                            Tarih: <?php echo htmlspecialchars($message['date']); ?></small>
                                    </div>
                                    <div class="email-quote-content">
                                        <?php echo (($original_content)); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Yanıtı Gönder</button>
                        <a href="mailbox.php" class="btn btn-secondary">İptal</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <style>
        .email-quote {
            border-left: 3px solid #ccc;
            padding-left: 15px;
            margin: 10px 0;
        }

        .email-quote-header {
            color: #666;
            margin-bottom: 10px;
        }

        .email-quote-content {
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.5;
        }
    </style>
    <!-- JavaScript kodları mailbox_admin.js dosyasında bulunuyor -->
<?php } ?>