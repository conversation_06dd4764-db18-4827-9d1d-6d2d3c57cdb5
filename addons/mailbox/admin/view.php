<?php
// pe($mailbox_config);
try {
    if (!isset($_GET['id'])) {
        fn_set_notice('Mail ID bulunamadı.', 'E');
        fn_redirect(fn_menu_link('mailbox/inbox'));
    }
    $mailbox = mailbox_connect();
    // pe($mailbox_config);


    // Klasör parametresine göre uygun klasöre geç
    if (isset($_GET['folder'])) {
        // Mevcut bağlantıyı kapat
        @imap_close($mailbox);

        $host = parse_url($mailbox_config['imap_host'], PHP_URL_HOST);
        $target_folder = null;

        if ($_GET['folder'] == 'sent') {
            // Sent klasörüne yeni bağlantı aç

            $possible_folders = array('Sent', 'Sent Items', 'Gönderilmiş Öğeler');

            foreach ($possible_folders as $folder) {
                $mailbox = @imap_open(
                    $host . ':993/imap/ssl/novalidate-cert}' . $folder,
                    $mailbox_config['username'],
                    $mailbox_config['password']
                );
                if ($mailbox) {
                    $target_folder = $folder;

                    break;
                }
            }


            if (!$mailbox) {
                throw new Exception('Giden kutusu klasörüne erişilemiyor: ' . imap_last_error());
                die;
            }
        } elseif ($_GET['folder'] == 'trash') {
            // Çöp kutusu klasörüne yeni bağlantı aç
            $possible_folders = array('Trash', 'Deleted Items', 'Çöp Kutusu', 'Deleted');

            foreach ($possible_folders as $folder) {
                $mailbox = @imap_open(
                    $host . ':993/imap/ssl/novalidate-cert}' . $folder,
                    $mailbox_config['username'],
                    $mailbox_config['password']
                );
                if ($mailbox) {
                    $target_folder = $folder;
                    break;
                }
            }

            if (!$mailbox) {
                throw new Exception('Çöp kutusu klasörüne erişilemiyor: ' . imap_last_error());
            }
        }

        error_log("Bağlanılan klasör: " . $target_folder);
    }

    $email_id = (int) $_GET['id'];
    $email = get_email_content($mailbox, $email_id);


    // Add this code to handle part type 1 (plain text) properly
    if (isset($email['part_type']) && $email['part_type'] == 1) {
        // Convert plain text to HTML with proper line breaks
        $email['body'] = nl2br(htmlspecialchars($email['body']));
    }

    // Maili okundu olarak işaretle
    if (isset($_GET['folder']) && $_GET['folder'] == 'sent') {
        // Giden kutusundaki mailler zaten okunmuş olarak işaretleniyor
    } else {
        // Maili okundu olarak işaretle
        imap_setflag_full($mailbox, $email_id, "\\Seen");
    }

    imap_close($mailbox);
} catch (Exception $e) {
    fn_set_notice($e->getMessage(), 'E');

    // Hata durumunda doğru sayfaya yönlendir
    if (isset($_GET['folder'])) {
        if ($_GET['folder'] == 'sent') {
            fn_redirect(fn_menu_link('mailbox/outbox', 1));
        } elseif ($_GET['folder'] == 'trash') {
            fn_redirect(fn_menu_link('mailbox/trash', 1));
        } else {
            fn_redirect(fn_menu_link('mailbox/inbox', 1));
        }
    } else {
        fn_redirect(fn_menu_link('mailbox/inbox', 1));
    }
}

// Mailin etiketlerini al
$email_labels = get_email_labels($email_id, isset($_GET['folder']) && $_GET['folder'] == 'sent' ? 'sent' : null, isset($_GET['folder']) ? $_GET['folder'] : 'INBOX');
$labels = get_labels();

// Geri dönüş URL'sini belirle
$return_url = '';
if (isset($_GET['folder'])) {
    if ($_GET['folder'] == 'sent') {
        $return_url = fn_menu_link('mailbox/outbox', 1);
    } elseif ($_GET['folder'] == 'trash') {
        $return_url = fn_menu_link('mailbox/trash', 1);
    } else {
        $return_url = fn_menu_link('mailbox/inbox', 1);
    }
} else {
    $return_url = fn_menu_link('mailbox/inbox', 1);
}
?>

<!--begin::Content-->
<div class="d-flex flex-column flex-lg-row">
    <!--begin::Sidebar-->
    <?php
    // Aktif sayfa bilgisini ayarla
    $current_page = isset($_GET['folder']) ? $_GET['folder'] : 'inbox';

    // Aside menüyü dahil et
    include(dirname(__FILE__) . '/includes/mailbox_aside.php');
    ?>
    <!--end::Sidebar-->

    <!--begin::Content-->
    <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
        <!--begin::Card-->
        <div class="card">
            <div class="card-header align-items-center py-5 gap-5">
                <!--begin::Actions-->
                <div class="d-flex">
                    <a href="<?php echo $return_url; ?>" class="btn btn-sm btn-icon btn-light-primary me-3">
                        <i class="ki-duotone ki-arrow-left fs-2">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </a>
                </div>
                <!--end::Actions-->

                <!--begin::Pagination-->
                <div class="d-flex align-items-center">
                    <div class="fw-semibold text-muted me-3"><?php echo date('d M Y H:i', strtotime($email['date'])); ?></div>
                </div>
                <!--end::Pagination-->

                <!--begin::Reply-->
                <div class="d-flex align-items-center gap-2">
                    <?php if (!isset($_GET['folder']) || $_GET['folder'] != 'trash'): ?>
                        <form method="post" action="<?php echo fn_menu_link('mailbox/delete'); ?>" class="d-inline me-2" onsubmit="return confirm('Bu maili silmek istediğinize emin misiniz?');">
                            <input type="hidden" name="email_id" value="<?php echo $email_id; ?>">
                            <input type="hidden" name="folder" value="<?php echo isset($_GET['folder']) ? $_GET['folder'] : ''; ?>">
                            <input type="hidden" name="return_url" value="<?php echo $return_url; ?>">
                            <button type="submit" class="btn btn-sm btn-icon btn-danger ">
                                <i class="ki-duotone ki-trash fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                    <span class="path4"></span>
                                    <span class="path5"></span>
                                </i>
                            </button>
                        </form>
                    <?php endif; ?>
                    <a href="<?php echo fn_menu_link('mailbox/reply&id=' . $email_id . (isset($_GET['folder']) && $_GET['folder'] == 'sent' ? '&folder=sent' : '')); ?>" class="btn btn-sm btn-secondary">
                        <i class="fa-solid fa-reply fs-3 me-1"></i>
                        Yanıtla
                    </a>

                    <div class="ms-auto">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-icon btn-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="ki-duotone ki-down fs-2x"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="<?php echo fn_menu_link('mailbox/reply&id=' . $email_id . '&reply_all=1' . (isset($_GET['folder']) && $_GET['folder'] == 'sent' ? '&folder=sent' : '')); ?>">
                                        <i class="ki-duotone ki-reply-all fs-2 me-1">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                        Tümünü Yanıtla
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo fn_menu_link('mailbox/forward&id=' . $email_id . (isset($_GET['folder']) && $_GET['folder'] == 'sent' ? '&folder=sent' : '')); ?>">
                                        <i class="ki-duotone ki-forward fs-2 me-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        Yönlendir
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#labelModal">
                                        <i class="ki-duotone ki-tag fs-2 me-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        Etiketler
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!--end::Reply-->
            </div>

            <div class="card-body">
                <!--begin::Message header-->
                <div class="d-flex flex-column gap-1 mb-5">
                    <div class="d-flex flex-column flex-sm-row justify-content-between gap-3">
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="symbol symbol-45px me-3">
                                <span class="symbol-label bg-light-primary text-primary fs-5 fw-bold">
                                    <?php echo strtoupper(substr($email['from_name'] ?: $email['from'], 0, 1)); ?>
                                </span>
                            </div>

                            <div class="d-flex flex-column">
                                <h3 class="fs-4 fw-bold mb-1"><?php echo $email['subject']; ?></h3>
                                <div class="d-flex align-items-center">
                                    <div class="fw-semibold text-muted me-2">
                                        <?php echo $email['from_name'] ? $email['from_name'] . ' &lt;' . $email['from'] . '&gt;' : $email['from']; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center flex-wrap gap-2">
                        <div class="fw-semibold text-muted me-2">Kime:</div>
                        <div class="fw-bold"><?php echo $email['to']; ?></div>
                    </div>

                    <?php if (!empty($email['cc'])): ?>
                        <div class="d-flex align-items-center flex-wrap gap-2">
                            <div class="fw-semibold text-muted me-2">CC:</div>
                            <div class="fw-bold"><?php echo $email['cc']; ?></div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($email_labels)): ?>
                        <div class="d-flex flex-wrap gap-2 mt-3">
                            <?php foreach ($email_labels as $label): ?>
                                <span class="badge" style="background-color: <?php echo $label->color; ?>; color: <?php echo get_contrast_color($label->color); ?>">
                                    <?php echo $label->name; ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <!--end::Message header-->

                <!--begin::Message content-->
                <div class="mb-5">
                    <iframe id="email-content-frame" style="width: 100%; height: 500px; border: none; background-color: #f5f8fa; border-radius: 0.475rem;"></iframe>
                    <script>
                        // E-posta içeriğini iframe'e yükle
                        document.addEventListener('DOMContentLoaded', function() {
                            var frame = document.getElementById('email-content-frame');
                            var content = <?php echo json_encode($email['body']); ?>;

                            // iframe içeriğini oluştur
                            var iframeContent = '<!DOCTYPE html><html><head><meta charset="UTF-8">';
                            iframeContent += '<style>';
                            iframeContent += 'body { font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; color: #181C32; padding: 20px; margin: 0; background-color: #ffffff; font-size: 14px; }';
                            iframeContent += 'h1, h2, h3, h4, h5, h6 { margin-top: 0; margin-bottom: 0.5rem; font-weight: 600; line-height: 1.2; color: #181C32; }';
                            iframeContent += 'h1 { font-size: 2rem; } h2 { font-size: 1.75rem; } h3 { font-size: 1.5rem; } h4 { font-size: 1.25rem; } h5 { font-size: 1.125rem; } h6 { font-size: 1rem; }';
                            iframeContent += 'p { margin-top: 0; margin-bottom: 1rem; }';
                            iframeContent += 'a { color: #009EF7; text-decoration: none; background-color: transparent; }';
                            iframeContent += 'a:hover { color: #0073c4; text-decoration: underline; }';
                            iframeContent += 'img { max-width: 100%; height: auto; border-style: none; vertical-align: middle; }';
                            iframeContent += 'table { border-collapse: collapse; width: 100%;  background-color: transparent; }';
                            iframeContent += 'table th, table td { padding: 0.75rem; vertical-align: top;  }';
                            iframeContent += 'table thead th { vertical-align: bottom; border-bottom: 2px solid #E4E6EF; background-color: #F5F8FA; font-weight: 600; }';
                            iframeContent += 'table tbody tr:nth-of-type(odd) { background-color: rgba(0, 0, 0, 0.02); }';
                            iframeContent += 'ul, ol { margin-top: 0; margin-bottom: 1rem; padding-left: 2rem; }';
                            iframeContent += 'li { margin-bottom: 0.25rem; }';
                            iframeContent += 'pre, code { font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 0.875rem; }';
                            iframeContent += 'pre { background-color: #F5F8FA; padding: 1rem; border-radius: 0.475rem; overflow: auto; margin-bottom: 1rem; }';
                            iframeContent += 'code { color: #d63384; word-wrap: break-word; padding: 0.2rem 0.4rem; background-color: #F5F8FA; border-radius: 0.25rem; }';
                            iframeContent += 'blockquote { margin: 0 0 1rem; padding: 0.5rem 1rem; border-left: 5px solid #E4E6EF; color: #6c757d; }';
                            iframeContent += 'hr { margin: 1.5rem 0; color: #E4E6EF; background-color: currentColor; border: 0; height: 1px; }';
                            iframeContent += 'b, strong { font-weight: 700; }';
                            iframeContent += 'i, em { font-style: italic; }';
                            iframeContent += 'small { font-size: 80%; }';
                            iframeContent += '.email-signature { margin-top: 20px; padding-top: 10px; border-top: 1px solid #E4E6EF; color: #6c757d; font-size: 0.9rem; }';
                            iframeContent += '.email-quote { margin: 10px 0; padding: 10px; border-left: 3px solid #009EF7; background-color: #F5F8FA; color: #6c757d; }';
                            iframeContent += '.email-attachment { display: inline-block; margin: 5px; padding: 8px 12px; border: 1px solid #E4E6EF; border-radius: 4px; background-color: #F5F8FA; }';
                            iframeContent += '@media (max-width: 768px) { body { padding: 15px; font-size: 13px; } table { display: block; width: 100%; overflow-x: auto; } }';
                            iframeContent += '</style>';
                            iframeContent += '<script>';
                            iframeContent += 'document.addEventListener("mouseup", function() {';
                            iframeContent += 'var selectedText = window.getSelection().toString().trim();';
                            iframeContent += 'if (selectedText && selectedText.length > 5) {';
                            iframeContent += 'window.parent.handleSelectedText(selectedText);';
                            iframeContent += '}';
                            iframeContent += '});';
                            iframeContent += '<\/script>';
                            iframeContent += '</head><body>' + content + '</body></html>';

                            // iframe'e içeriği yaz
                            frame.contentWindow.document.open();
                            frame.contentWindow.document.write(iframeContent);
                            frame.contentWindow.document.close();

                            // iframe yüksekliğini içeriğe göre ayarla
                            frame.onload = function() {
                                var height = frame.contentWindow.document.body.scrollHeight;
                                frame.style.height = (height + 30) + 'px';
                            };
                        });
                    </script>
                </div>
                <!--end::Message content-->

                <?php if (!empty($email['attachments'])): ?>
                    <!--begin::Message footer-->
                    <div class="d-flex flex-stack flex-wrap gap-2">
                        <div class="d-flex align-items-center">
                            <div class="fw-bold text-gray-600 fs-6 me-3">Ekler:</div>
                            <div class="d-flex">
                                <?php foreach ($email['attachments'] as $attachment): ?>
                                    <div class="d-flex align-items-center border border-dashed border-gray-300 rounded p-3 me-3">
                                        <i class="ki-duotone ki-file fs-3 text-primary me-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        <a href="<?php
                                                    if (isset($_GET['folder'])) {
                                                        if ($_GET['folder'] == 'sent') {
                                                            fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&folder=sent&filename=' . urlencode($attachment['filename']));
                                                        } elseif ($_GET['folder'] == 'trash') {
                                                            fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&folder=trash&filename=' . urlencode($attachment['filename']));
                                                        } else {
                                                            fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&filename=' . urlencode($attachment['filename']));
                                                        }
                                                    } else {
                                                        fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&filename=' . urlencode($attachment['filename']));
                                                    }
                                                    ?>" class="fs-5 fw-bold text-gray-900 text-hover-primary ms-1">
                                            <?php echo $attachment['filename']; ?>
                                            <?php if (isset($attachment['size'])): ?>
                                                <span class="text-muted">(<?php echo format_file_size($attachment['size']); ?>)</span>
                                            <?php endif; ?>
                                        </a>

                                        <!-- Debug linki -->
                                        <a href="<?php
                                                    if (isset($_GET['folder'])) {
                                                        if ($_GET['folder'] == 'sent') {
                                                            fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&folder=sent&filename=' . urlencode($attachment['filename']) . '&debug=1');
                                                        } elseif ($_GET['folder'] == 'trash') {
                                                            fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&folder=trash&filename=' . urlencode($attachment['filename']) . '&debug=1');
                                                        } else {
                                                            fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&filename=' . urlencode($attachment['filename']) . '&debug=1');
                                                        }
                                                    } else {
                                                        fn_menu_link('mailbox/download&id=' . $email_id . '&part=' . $attachment['part_num'] . '&filename=' . urlencode($attachment['filename']) . '&debug=1');
                                                    }
                                                    ?>" class="btn btn-sm btn-outline-info ms-2" title="Debug">
                                            <i class="fas fa-bug"></i> Debug
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <!--end::Message footer-->
                <?php endif; ?>
            </div>
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content-->
</div>
<!--end::Content-->

<!-- Etiket Modal -->
<div class="modal fade" id="labelModal" tabindex="-1" aria-labelledby="labelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="labelModalLabel">Etiketler</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (!empty($labels)): ?>
                    <form method="post" action="<?php fn_menu_link('mailbox/label_actions'); ?>" class="label-form">
                        <input type="hidden" name="action" value="update_email_labels">
                        <input type="hidden" name="email_id" value="<?php echo $email_id; ?>">
                        <input type="hidden" name="folder" value="<?php echo isset($_GET['folder']) ? $_GET['folder'] : 'INBOX'; ?>">
                        <input type="hidden" name="return_url" value="<?php echo $_SERVER['REQUEST_URI']; ?>">

                        <div class="mb-3">
                            <?php foreach ($labels as $label): ?>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="label_ids[]" value="<?php echo $label->id; ?>" id="label_<?php echo $label->id; ?>" <?php echo in_array($label->id, array_map(function ($l) {
                                                                                                                                                                                    return $l->id;
                                                                                                                                                                                }, $email_labels)) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="label_<?php echo $label->id; ?>">
                                        <span class="badge" style="background-color: <?php echo $label->color; ?>; color: <?php echo get_contrast_color($label->color); ?>">
                                            <?php echo $label->name; ?>
                                        </span>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo fn_menu_link('mailbox/labels'); ?>" class="btn btn-sm btn-light">Etiketleri Yönet</a>
                            <button type="submit" class="btn btn-sm btn-primary">Kaydet</button>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="alert alert-info mb-3">
                        Henüz hiç etiket oluşturmadınız.
                    </div>
                    <div class="d-flex justify-content-center">
                        <a href="<?php echo fn_menu_link('mailbox/labels'); ?>" class="btn btn-sm btn-primary">Etiket Oluştur</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Seçili Metin Modal -->
<div class="modal fade" id="selectedTextModal" tabindex="-1" aria-labelledby="selectedTextModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="selectedTextModalLabel">Seçili Metni İşle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="selectedTextForm" action="<?php echo fn_menu_link('mailbox/tomove'); ?>" method="post">
                    <input type="hidden" name="email_id" value="<?php echo $email_id; ?>">
                    <input type="hidden" name="folder" value="<?php echo isset($_GET['folder']) ? $_GET['folder'] : 'INBOX'; ?>">

                    <div class="mb-4">
                        <label class="form-label fw-bold">Seçili Metin:</label>
                        <div class="border rounded p-3 bg-light" id="selectedTextContent"></div>
                        <input type="hidden" name="selected_text" id="selectedTextInput">
                    </div>

                    <div class="mb-4">
                        <label class="form-label fw-bold">Aktarılacak Bölüm:</label>
                        <select class="form-select" name="target_section" required>
                            <option value="">Seçiniz...</option>
                            <option value="musteri_notlari">Müşteri Notları</option>
                            <option value="proje_dokumanlari">Proje Dökümanları</option>
                            <option value="gorev_listesi">Görev Listesi</option>
                            <option value="bilgi_bankasi">Bilgi Bankası</option>
                        </select>
                    </div>

                    <?php if (!empty($email['attachments'])): ?>
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label fw-bold mb-0">Ekleri Dahil Et:</label>
                                <div>
                                    <button type="button" class="btn btn-sm btn-light" id="selectAllAttachments">Hepsini Seç</button>
                                    <button type="button" class="btn btn-sm btn-light" id="deselectAllAttachments">Hiçbirini Seçme</button>
                                </div>
                            </div>
                            <div class="border rounded p-3">
                                <?php foreach ($email['attachments'] as $key => $attachment): ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input attachment-checkbox" type="checkbox" name="attachments[]" value="<?php echo $attachment['part_num']; ?>" id="attachment_<?php echo $key; ?>" checked>
                                        <label class="form-check-label" for="attachment_<?php echo $key; ?>">
                                            <?php echo $attachment['filename']; ?>
                                            <?php if (isset($attachment['size'])): ?>
                                                <span class="text-muted">(<?php echo format_file_size($attachment['size']); ?>)</span>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Vazgeç</button>
                <button type="button" class="btn btn-primary" id="submitSelectedText">Aktar</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Global fonksiyon tanımla - iframe içinden erişilebilir
    window.handleSelectedText = function(selectedText) {
        // Seçili metni modal içine yerleştir
        document.getElementById('selectedTextContent').textContent = selectedText;
        document.getElementById('selectedTextInput').value = selectedText;

        // Modal'i göster
        var modalElement = document.getElementById('selectedTextModal');
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            var modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else if (typeof $ !== 'undefined' && $.fn.modal) {
            $(modalElement).modal('show');
        } else {
            // Manuel olarak modalı göster
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
            document.body.classList.add('modal-open');

            // Arka plan oluştur
            var backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
    };

    // Ek seçme butonları için event listener'lar
    document.addEventListener('DOMContentLoaded', function() {
        // Hepsini Seç butonu
        var selectAllBtn = document.getElementById('selectAllAttachments');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                document.querySelectorAll('.attachment-checkbox').forEach(function(checkbox) {
                    checkbox.checked = true;
                });
            });
        }

        // Hiçbirini Seçme butonu
        var deselectAllBtn = document.getElementById('deselectAllAttachments');
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', function() {
                document.querySelectorAll('.attachment-checkbox').forEach(function(checkbox) {
                    checkbox.checked = false;
                });
            });
        }

        // Form gönderme butonu
        var submitBtn = document.getElementById('submitSelectedText');
        if (submitBtn) {
            submitBtn.addEventListener('click', function() {
                document.getElementById('selectedTextForm').submit();
            });
        }
    });
</script>