<?php

/**
 * Quick test for name matching with your specific format
 */

// Include the functions
require_once 'tomove.php';

// Test crew member data
$test_crew = [
    'name' => 'ABANILLA,JOHN ALBERT ANSELMO',
    'firstname' => 'JOHN ALBERT ANSELMO',
    'lastname' => 'ABANILLA',
    'rank' => '2ND OFFICER'
];

// Test attachment
$test_filename = 'ABANILLA JOHN ALBERT -PP (PASSPORT) 1.jpg';

echo "<h1>Quick Name Matching Test</h1>";

echo "<h2>Crew Member:</h2>";
echo "<pre>" . print_r($test_crew, true) . "</pre>";

echo "<h2>Attachment Filename:</h2>";
echo "<p>" . htmlspecialchars($test_filename) . "</p>";

echo "<h2>Generated Name Variations:</h2>";
$variations = generate_name_variations($test_crew);
echo "<ul>";
foreach ($variations as $variation) {
    echo "<li>" . htmlspecialchars($variation) . "</li>";
}
echo "</ul>";

echo "<h2>Matching Test:</h2>";
$filename_lower = strtolower($test_filename);
echo "<p><strong>Filename (lowercase):</strong> " . htmlspecialchars($filename_lower) . "</p>";

$found_match = false;
$matching_variation = '';
foreach ($variations as $variation) {
    $variation_lower = strtolower($variation);
    if (strpos($filename_lower, $variation_lower) !== false) {
        echo "<p style='color: green;'>✅ <strong>MATCH FOUND!</strong> Using variation: <code>" . htmlspecialchars($variation) . "</code></p>";
        $found_match = true;
        $matching_variation = $variation;
        break;
    }
}

if (!$found_match) {
    echo "<p style='color: red;'>❌ <strong>NO MATCH FOUND</strong></p>";

    echo "<h3>Detailed Check:</h3>";
    foreach ($variations as $variation) {
        $variation_lower = strtolower($variation);
        echo "<p>Checking: <code>" . htmlspecialchars($variation_lower) . "</code> in <code>" . htmlspecialchars($filename_lower) . "</code> = ";
        if (strpos($filename_lower, $variation_lower) !== false) {
            echo "<span style='color: green;'>FOUND</span>";
        } else {
            echo "<span style='color: red;'>NOT FOUND</span>";
        }
        echo "</p>";
    }
} else {
    echo "<h3>Match Details:</h3>";
    echo "<p>✅ Found '<code>" . htmlspecialchars(strtolower($matching_variation)) . "</code>' in '<code>" . htmlspecialchars($filename_lower) . "</code>'</p>";
}

echo "<h2>Document Type Detection:</h2>";
$doc_type = determine_document_type($test_filename);
echo "<p><strong>Detected Type:</strong> " . htmlspecialchars($doc_type) . "</p>";

// Test with second attachment
echo "<hr>";
echo "<h1>Test with Seaman Book</h1>";
$test_filename2 = 'ABANILLA JOHN ALBERT -SBK (Philippines) 1.pdf';
echo "<p><strong>Filename:</strong> " . htmlspecialchars($test_filename2) . "</p>";

$doc_type2 = determine_document_type($test_filename2);
echo "<p><strong>Detected Type:</strong> " . htmlspecialchars($doc_type2) . "</p>";

$filename_lower2 = strtolower($test_filename2);
$found_match2 = false;
foreach ($variations as $variation) {
    $variation_lower = strtolower($variation);
    if (strpos($filename_lower2, $variation_lower) !== false) {
        echo "<p style='color: green;'>✅ <strong>MATCH FOUND!</strong> Using variation: <code>" . htmlspecialchars($variation) . "</code></p>";
        $found_match2 = true;
        break;
    }
}

if (!$found_match2) {
    echo "<p style='color: red;'>❌ <strong>NO MATCH FOUND</strong></p>";
}

// Test Format 2 name matching
echo "<hr>";
echo "<h1>Format 2 Name Matching Test</h1>";

// Format 2 crew member data
$test_crew_format_2 = [
    'name' => 'MAJICA,VOJISLAV',  // Bu format 2'de oluşturulan name
    'firstname' => 'VOJISLAV',
    'lastname' => 'MAJICA',
    'rank' => 'MASTER'
];

// Format 2 attachment - Gerçek örnek
$test_filename_format_2 = 'Majica PP.jpg';

echo "<h2>Format 2 Crew Member:</h2>";
echo "<pre>" . print_r($test_crew_format_2, true) . "</pre>";

echo "<h2>Format 2 Attachment Filename:</h2>";
echo "<p>" . htmlspecialchars($test_filename_format_2) . "</p>";

echo "<h2>Format 2 Generated Name Variations:</h2>";
$variations_format_2 = generate_name_variations($test_crew_format_2);
echo "<ul>";
foreach ($variations_format_2 as $variation) {
    echo "<li>" . htmlspecialchars($variation) . "</li>";
}
echo "</ul>";

echo "<h2>Format 2 Matching Test:</h2>";
$filename_lower_format_2 = strtolower($test_filename_format_2);
echo "<p><strong>Filename (lowercase):</strong> " . htmlspecialchars($filename_lower_format_2) . "</p>";

$found_match_format_2 = false;
$matching_variation_format_2 = '';
foreach ($variations_format_2 as $variation) {
    $variation_lower = strtolower($variation);
    if (strpos($filename_lower_format_2, $variation_lower) !== false) {
        echo "<p style='color: green;'>✅ <strong>MATCH FOUND!</strong> Using variation: <code>" . htmlspecialchars($variation) . "</code></p>";
        $found_match_format_2 = true;
        $matching_variation_format_2 = $variation;
        break;
    }
}

if (!$found_match_format_2) {
    echo "<p style='color: red;'>❌ <strong>NO MATCH FOUND</strong></p>";

    echo "<h3>Detailed Check:</h3>";
    foreach ($variations_format_2 as $variation) {
        $variation_lower = strtolower($variation);
        echo "<p>Checking: <code>" . htmlspecialchars($variation_lower) . "</code> in <code>" . htmlspecialchars($filename_lower_format_2) . "</code> = ";
        if (strpos($filename_lower_format_2, $variation_lower) !== false) {
            echo "<span style='color: green;'>FOUND</span>";
        } else {
            echo "<span style='color: red;'>NOT FOUND</span>";
        }
        echo "</p>";
    }
} else {
    echo "<h3>Match Details:</h3>";
    echo "<p>✅ Found '<code>" . htmlspecialchars(strtolower($matching_variation_format_2)) . "</code>' in '<code>" . htmlspecialchars($filename_lower_format_2) . "</code>'</p>";
}

// Test gerçek kullanıcı örneği
echo "<hr>";
echo "<h1>Gerçek Kullanıcı Örneği Test</h1>";

$real_crew = [
    'name' => 'MAJICA,VOJISLAV',
    'firstname' => 'VOJISLAV',
    'lastname' => 'MAJICA',
    'rank' => 'MASTER'
];

$real_filename = 'Majica PP.jpg';

echo "<h2>Gerçek Crew Member:</h2>";
echo "<pre>" . print_r($real_crew, true) . "</pre>";

echo "<h2>Gerçek Attachment Filename:</h2>";
echo "<p>" . htmlspecialchars($real_filename) . "</p>";

echo "<h2>Generated Name Variations:</h2>";
$real_variations = generate_name_variations($real_crew);
echo "<p><strong>Total variations:</strong> " . count($real_variations) . "</p>";
echo "<ul>";
foreach ($real_variations as $variation) {
    echo "<li>" . htmlspecialchars($variation) . "</li>";
}
echo "</ul>";

echo "<h2>Matching Test:</h2>";
$real_filename_lower = strtolower($real_filename);
echo "<p><strong>Filename (lowercase):</strong> " . htmlspecialchars($real_filename_lower) . "</p>";

$found_real_match = false;
$matching_real_variation = '';
foreach ($real_variations as $variation) {
    $variation_lower = strtolower($variation);
    if (strpos($real_filename_lower, $variation_lower) !== false) {
        echo "<p style='color: green;'>✅ <strong>MATCH FOUND!</strong> Using variation: <code>" . htmlspecialchars($variation) . "</code></p>";
        $found_real_match = true;
        $matching_real_variation = $variation;
        break;
    }
}

if (!$found_real_match) {
    echo "<p style='color: red;'>❌ <strong>NO MATCH FOUND</strong></p>";

    echo "<h3>Detailed Check:</h3>";
    foreach ($real_variations as $variation) {
        $variation_lower = strtolower($variation);
        echo "<p>Checking: '<code>" . htmlspecialchars($variation_lower) . "</code>' in '<code>" . htmlspecialchars($real_filename_lower) . "</code>' = ";
        if (strpos($real_filename_lower, $variation_lower) !== false) {
            echo "<span style='color: green;'>FOUND</span>";
        } else {
            echo "<span style='color: red;'>NOT FOUND</span>";
        }
        echo "</p>";
    }
} else {
    echo "<h3>Match Details:</h3>";
    echo "<p>✅ Found '<code>" . htmlspecialchars(strtolower($matching_real_variation)) . "</code>' in '<code>" . htmlspecialchars($real_filename_lower) . "</code>'</p>";
}
