<?php
if ($_POST['email_id'])
    $_SESSION['calisma']['email_id'] = $_POST['email_id'];
if ($_POST['folder'])
    $_SESSION['calisma']['folder'] = $_POST['folder'];
if ($_POST['selected_text'])
    $_SESSION['calisma']['selected_text'] = $_POST['selected_text'];
if ($_POST['attachments'])
    $_SESSION['calisma']['attachments'] = $_POST['attachments'];

// Crew data export işlemi
if (isset($_POST['export_crew_data']) && $_POST['export_crew_data'] == '1') {
    $export_data = [
        'on_signers' => $_POST['on_signers'] ?? [],
        'off_signers' => $_POST['off_signers'] ?? [],
        'export_timestamp' => date('Y-m-d H:i:s'),
        'format_type' => detect_text_format($_SESSION['calisma']['selected_text'] ?? ''),
        'total_on_signers' => count($_POST['on_signers'] ?? []),
        'total_off_signers' => count($_POST['off_signers'] ?? [])
    ];

    // Export data'yı session'a kaydet
    $_SESSION['calisma']['exported_crew_data'] = $export_data;

    // JSON olarak da kaydet (isteğe bağlı)
    $json_data = json_encode($export_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

    // Success message
    $_SESSION['success_message'] = 'Crew verileri başarıyla aktarıldı! Toplam: ' .
        $export_data['total_on_signers'] . ' ON-SIGNERS, ' .
        $export_data['total_off_signers'] . ' OFF-SIGNERS';

    // Debug için JSON'u da göster
    $_SESSION['calisma']['exported_json'] = $json_data;
}

$_POST['selected_text'] = $_SESSION['calisma']['selected_text'];
$_POST['target_section'] = 'TArget Section';
$_POST['email_id'] = $_SESSION['calisma']['email_id'];
$_POST['folder'] = $_SESSION['calisma']['folder'];
$_POST['attachments'] = $_SESSION['calisma']['attachments'];

/**
 * Crew Member Text Parser - Mürettebat Metin Ayrıştırıcısı
 *
 * Bu fonksiyon crew member verilerini içeren metinleri ayrıştırır ve yapılandırılmış diziler oluşturur.
 * Farklı text formatlarını otomatik olarak tespit eder ve uygun parser'ı çağırır.
 *
 * @param string $text - Ayrıştırılacak metin
 * @param array $processed_attachments - İşlenmiş ek dosyalar dizisi
 * @return array - Yapılandırılmış crew member verileri
 */
function parse_crew_member_text($text, $processed_attachments = [])
{
    // Format tespiti yap
    $format = detect_text_format($text);

    switch ($format) {
        case 'format_1': // ON-SIGNERS/OFF-SIGNERS format
            return parse_crew_format_1($text, $processed_attachments);

        case 'format_2': // ONSIGNER/OFFSIGNER format
            return parse_crew_format_2($text, $processed_attachments);

        default:
            // Varsayılan olarak format 1'i dene
            return parse_crew_format_1($text, $processed_attachments);
    }
}

/**
 * Text formatını tespit eder
 *
 * @param string $text - Analiz edilecek metin
 * @return string - Format türü
 */
function detect_text_format($text)
{
    // Format 2 kontrolü: ONSIGNER/OFFSIGNER ve Surname:/Name: pattern
    if ((strpos($text, 'ONSIGNER') !== false || strpos($text, 'OFFSIGNER') !== false) &&
        (strpos($text, 'Surname:') !== false && strpos($text, 'Name:') !== false)
    ) {
        return 'format_2';
    }

    // Format 1 kontrolü: ON-SIGNERS:/OFF-SIGNERS: pattern
    if (strpos($text, 'ON-SIGNERS:') !== false || strpos($text, 'OFF-SIGNERS:') !== false) {
        return 'format_1';
    }

    return 'format_1'; // Varsayılan
}

/**
 * Format 1 Parser - Orijinal ON-SIGNERS/OFF-SIGNERS format
 *
 * @param string $text - Ayrıştırılacak metin
 * @param array $processed_attachments - İşlenmiş ek dosyalar dizisi
 * @return array - Yapılandırılmış crew member verileri
 */
function parse_crew_format_1($text, $processed_attachments = [])
{
    // ped($processed_attachments);
    $result = [
        'on_signers' => [],
        'off_signers' => [],
        'on_signers_flight_details' => '',
        'off_signers_flight_details' => '',
        'statistics' => [
            'on_signers_count' => 0,
            'off_signers_count' => 0,
            'total_attachments_matched' => 0
        ]
    ];

    // Metni satırlara böl
    $lines = explode("\n", $text);
    $current_section = '';
    $current_crew_data = '';

    foreach ($lines as $line) {
        $line = trim($line);

        // Bölüm başlıklarını tespit et
        if (preg_match('/^ON-SIGNERS:\s*(\d+)/', $line, $matches)) {
            $current_section = 'on_signers';
            $result['statistics']['on_signers_count'] = (int)$matches[1];
            continue;
        }

        if (preg_match('/^OFF-SIGNERS:\s*(\d+)/', $line, $matches)) {
            // Önceki bölümü tamamla
            if ($current_section == 'on_signers' && $current_crew_data) {
                $result['on_signers'][] = parse_single_crew_member($current_crew_data);
                $current_crew_data = '';
            }

            $current_section = 'off_signers';
            $result['statistics']['off_signers_count'] = (int)$matches[1];
            continue;
        }

        if (preg_match('/FLIGHT DETAILS?:/i', $line)) {
            // Önceki crew member verisini tamamla
            if ($current_crew_data) {
                if ($current_section == 'on_signers') {
                    $result['on_signers'][] = parse_single_crew_member($current_crew_data);
                } elseif ($current_section == 'off_signers') {
                    $result['off_signers'][] = parse_single_crew_member($current_crew_data);
                }
                $current_crew_data = '';
            }

            $current_section = ($current_section == 'on_signers') ? 'on_signers_flight' : 'off_signers_flight';
            continue;
        }

        // Yıldız çizgisi veya boş satırları atla
        if (empty($line) || preg_match('/^\*+$/', $line) || preg_match('/^-+$/', $line)) {
            continue;
        }

        // Flight details topla
        if ($current_section == 'on_signers_flight') {
            $result['on_signers_flight_details'] .= $line . "\n";
            continue;
        }

        if ($current_section == 'off_signers_flight') {
            $result['off_signers_flight_details'] .= $line . "\n";
            continue;
        }

        // Crew member verilerini topla
        if ($current_section == 'on_signers' || $current_section == 'off_signers') {
            // Yeni crew member başlangıcını tespit et
            if (preg_match('/^Name:\s*(.+)/', $line)) {
                // Önceki crew member verisini kaydet
                if ($current_crew_data) {
                    if ($current_section == 'on_signers') {
                        $result['on_signers'][] = parse_single_crew_member($current_crew_data);
                    } else {
                        $result['off_signers'][] = parse_single_crew_member($current_crew_data);
                    }
                }
                $current_crew_data = $line . "\n";
            } else {
                $current_crew_data .= $line . "\n";
            }
        }
    }

    // Son crew member verisini kaydet
    if ($current_crew_data) {
        if ($current_section == 'on_signers') {
            $result['on_signers'][] = parse_single_crew_member($current_crew_data);
        } elseif ($current_section == 'off_signers') {
            $result['off_signers'][] = parse_single_crew_member($current_crew_data);
        }
    }

    // Ek dosyaları crew member'lara eşleştir
    if (!empty($processed_attachments)) {
        $result = match_attachments_to_crew($result, $processed_attachments);
    }

    return $result;
}

/**
 * Tek bir crew member verisini ayrıştırır
 *
 * @param string $crew_data - Tek crew member'a ait ham metin verisi
 * @return array - Ayrıştırılmış crew member bilgileri
 */
function parse_single_crew_member($crew_data)
{
    $crew = [
        'name' => '',
        'firstname' => '',
        'lastname' => '',
        'rank' => '',
        'passport' => [
            'number' => '',
            'issued' => '',
            'expiry' => ''
        ],
        'seaman_book' => [
            'number' => '',
            'issued' => '',
            'expiry' => ''
        ],
        'nationality' => '',
        'date_of_birth' => '',
        'place_of_birth' => '',
        'attachments' => []
    ];

    $lines = explode("\n", trim($crew_data));

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // Name ve Rank
        if (preg_match('/^Name:\s*(.+?)\s+Rank:\s*(.+)$/i', $line, $matches)) {
            $crew['name'] = trim($matches[1]);
            $crew['rank'] = trim($matches[2]);

            // İsmi parçalara ayır
            $name_parts = explode(',', $crew['name']);
            if (count($name_parts) >= 2) {
                $crew['lastname'] = trim($name_parts[0]);
                $crew['firstname'] = trim($name_parts[1]);
            } else {
                $name_parts = explode(' ', $crew['name']);
                $crew['firstname'] = trim($name_parts[0]);
                if (count($name_parts) > 1) {
                    $crew['lastname'] = trim(implode(' ', array_slice($name_parts, 1)));
                }
            }
        }

        // Passport bilgileri
        if (preg_match('/^Passport:\s*(.+?)\s+Issued:\s*(.+?)\s+Expiry:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['number'] = trim($matches[1]);
            $crew['passport']['issued'] = trim($matches[2]);
            $crew['passport']['expiry'] = trim($matches[3]);
        }

        // Seaman Book bilgileri
        if (preg_match('/^Seaman Book:\s*(.+?)\s+Issued:\s*(.+?)\s+Expiry:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['number'] = trim($matches[1]);
            $crew['seaman_book']['issued'] = trim($matches[2]);
            $crew['seaman_book']['expiry'] = trim($matches[3]);
        }

        // Nationality, Date of Birth, Place of Birth
        if (preg_match('/^Nationality:\s*(.+?)\s+Date of Birth:\s*(.+?)\s+Place of Birth:\s*(.+)$/i', $line, $matches)) {
            $crew['nationality'] = trim($matches[1]);
            $crew['date_of_birth'] = trim($matches[2]);
            $crew['place_of_birth'] = trim($matches[3]);
        }
    }

    return $crew;
}

/**
 * Ek dosyaları crew member'lara eşleştirir
 *
 * @param array $crew_data - Crew member verileri
 * @param array $processed_attachments - İşlenmiş ek dosyalar
 * @return array - Ek dosyaları eşleştirilmiş crew member verileri
 */
function match_attachments_to_crew($crew_data, $processed_attachments)
{
    $total_matched = 0;

    // Tüm crew member'ları kontrol et
    $all_crews = array_merge($crew_data['on_signers'], $crew_data['off_signers']);

    foreach ($processed_attachments as $attachment) {
        $filename = strtolower($attachment['filename']);

        foreach ($all_crews as $crew) {
            // İsim eşleştirmesi için farklı formatları dene
            $name_variations = generate_name_variations($crew);

            foreach ($name_variations as $name_variation) {
                $name_variation_lower = strtolower($name_variation);
                if (strpos($filename, $name_variation_lower) !== false) {
                    // Dosya türünü belirle
                    $document_type = determine_document_type($filename);

                    $attachment_info = [
                        'filename' => $attachment['filename'],
                        'path' => $attachment['path'],
                        'size' => $attachment['size'],
                        'type' => $document_type,
                        'matched_name' => $name_variation
                    ];

                    // Crew member'ın hangi listede olduğunu bul ve ekle
                    $found_in_on_signers = false;
                    foreach ($crew_data['on_signers'] as &$on_signer) {
                        if ($on_signer['name'] == $crew['name']) {
                            $on_signer['attachments'][] = $attachment_info;
                            $found_in_on_signers = true;
                            break;
                        }
                    }

                    if (!$found_in_on_signers) {
                        foreach ($crew_data['off_signers'] as &$off_signer) {
                            if ($off_signer['name'] == $crew['name']) {
                                $off_signer['attachments'][] = $attachment_info;
                                break;
                            }
                        }
                    }

                    $total_matched++;
                    break 2; // İç ve dış döngüden çık
                }
            }
        }
    }

    $crew_data['statistics']['total_attachments_matched'] = $total_matched;
    return $crew_data;
}

/**
 * İsim varyasyonları oluşturur
 *
 * @param array $crew - Crew member bilgileri
 * @return array - İsim varyasyonları
 */
function generate_name_variations($crew)
{
    $variations = [];

    if (!empty($crew['name'])) {
        // Orijinal isim
        $variations[] = $crew['name'];
        $variations[] = str_replace(',', '', $crew['name']);
        $variations[] = str_replace(' ', '', $crew['name']);
        $variations[] = str_replace(',', '_', $crew['name']);
        $variations[] = str_replace(' ', '_', $crew['name']);

        // Virgüllü format: "ABANILLA,JOHN ALBERT ANSELMO" -> "ABANILLA JOHN ALBERT"
        if (strpos($crew['name'], ',') !== false) {
            $name_parts = explode(',', $crew['name']);
            $lastname = trim($name_parts[0]);
            $firstname_parts = trim($name_parts[1]);

            // "LASTNAME FIRSTNAME" formatı (tam isim)
            $variations[] = $lastname . ' ' . $firstname_parts;
            $variations[] = $lastname . '_' . str_replace(' ', '_', $firstname_parts);
            $variations[] = $lastname . str_replace(' ', '', $firstname_parts);

            // İsim parçalarını ayır
            $name_words = explode(' ', $firstname_parts);

            // Sadece ilk isim ile
            if (count($name_words) > 0) {
                $first_name_only = $name_words[0];
                $variations[] = $lastname . ' ' . $first_name_only;
                $variations[] = $lastname . '_' . $first_name_only;
                $variations[] = $lastname . $first_name_only;
            }

            // İlk iki isim ile (JOHN ALBERT)
            if (count($name_words) >= 2) {
                $first_two_names = $name_words[0] . ' ' . $name_words[1];
                $variations[] = $lastname . ' ' . $first_two_names;
                $variations[] = $lastname . '_' . str_replace(' ', '_', $first_two_names);
                $variations[] = $lastname . str_replace(' ', '', $first_two_names);
            }

            // İlk üç isim ile (eğer varsa)
            if (count($name_words) >= 3) {
                $first_three_names = $name_words[0] . ' ' . $name_words[1] . ' ' . $name_words[2];
                $variations[] = $lastname . ' ' . $first_three_names;
                $variations[] = $lastname . '_' . str_replace(' ', '_', $first_three_names);
                $variations[] = $lastname . str_replace(' ', '', $first_three_names);
            }
        }
    }

    // Format 2 için özel logic: firstname ve lastname ayrı ayrı gelir
    if (!empty($crew['firstname']) && !empty($crew['lastname'])) {
        // Format 2 style: "MAJICA VOJISLAV" (LASTNAME FIRSTNAME)
        $variations[] = $crew['lastname'] . ' ' . $crew['firstname'];
        $variations[] = $crew['lastname'] . '_' . $crew['firstname'];
        $variations[] = $crew['lastname'] . $crew['firstname'];

        // Ters sıra: "VOJISLAV MAJICA" (FIRSTNAME LASTNAME)
        $variations[] = $crew['firstname'] . ' ' . $crew['lastname'];
        $variations[] = $crew['firstname'] . '_' . $crew['lastname'];
        $variations[] = $crew['firstname'] . $crew['lastname'];

        // Format 2'de firstname birden fazla kelime olabilir: "JERIVIC HENRY"
        $firstname_words = explode(' ', trim($crew['firstname']));
        if (count($firstname_words) > 1) {
            // Sadece ilk isim ile: "MAJICA JERIVIC"
            $variations[] = $crew['lastname'] . ' ' . $firstname_words[0];
            $variations[] = $crew['lastname'] . '_' . $firstname_words[0];
            $variations[] = $crew['lastname'] . $firstname_words[0];

            // Ters: "JERIVIC MAJICA"
            $variations[] = $firstname_words[0] . ' ' . $crew['lastname'];
            $variations[] = $firstname_words[0] . '_' . $crew['lastname'];
            $variations[] = $firstname_words[0] . $crew['lastname'];

            // İlk iki isim ile: "MAJICA JERIVIC HENRY"
            if (count($firstname_words) >= 2) {
                $first_two = $firstname_words[0] . ' ' . $firstname_words[1];
                $variations[] = $crew['lastname'] . ' ' . $first_two;
                $variations[] = $crew['lastname'] . '_' . str_replace(' ', '_', $first_two);
                $variations[] = $crew['lastname'] . str_replace(' ', '', $first_two);
            }
        }

        // Büyük harf versiyonları
        $variations[] = strtoupper($crew['lastname'] . ' ' . $crew['firstname']);
        $variations[] = strtoupper($crew['lastname'] . '_' . $crew['firstname']);
        $variations[] = strtoupper($crew['lastname'] . $crew['firstname']);
        $variations[] = strtoupper($crew['firstname'] . ' ' . $crew['lastname']);
        $variations[] = strtoupper($crew['firstname'] . '_' . $crew['lastname']);
        $variations[] = strtoupper($crew['firstname'] . $crew['lastname']);
    }

    // Passport numarası da ekle
    if (!empty($crew['passport']['number'])) {
        $variations[] = $crew['passport']['number'];
    }

    // Seaman book numarası da ekle
    if (!empty($crew['seaman_book']['number'])) {
        $variations[] = $crew['seaman_book']['number'];
    }

    // Sadece isim ve soyisim ile basit kombinasyonlar (en yaygın durumlar)
    if (!empty($crew['firstname']) && !empty($crew['lastname'])) {
        // Sadece soyisim (MAJICA)
        $variations[] = $crew['lastname'];

        // Sadece isim (VOJISLAV)
        $variations[] = $crew['firstname'];

        // İsmin ilk kelimesi (JERIVIC HENRY -> JERIVIC)
        $first_name_parts = explode(' ', trim($crew['firstname']));
        if (count($first_name_parts) > 0) {
            $variations[] = $first_name_parts[0];
        }
    }

    // Tüm varyasyonları hem büyük hem küçük harfe çevir
    $all_case_variations = [];
    foreach ($variations as $variation) {
        $all_case_variations[] = $variation;                    // Orijinal
        $all_case_variations[] = strtoupper($variation);        // BÜYÜK HARF
        $all_case_variations[] = strtolower($variation);        // küçük harf
        $all_case_variations[] = ucfirst(strtolower($variation)); // İlk harf büyük
        $all_case_variations[] = ucwords(strtolower($variation)); // Her kelimenin ilk harfi büyük
    }

    return array_unique($all_case_variations);
}

/**
 * Dosya türünü belirler
 *
 * @param string $filename - Dosya adı
 * @return string - Dosya türü
 */
function determine_document_type($filename)
{
    $filename_lower = strtolower($filename);

    // Passport kontrolü - PP, PASSPORT, PASAPORT
    if (
        strpos($filename_lower, 'passport') !== false ||
        strpos($filename_lower, 'pasaport') !== false ||
        strpos($filename_lower, '-pp ') !== false ||
        strpos($filename_lower, '_pp_') !== false ||
        strpos($filename_lower, '(passport)') !== false
    ) {
        return 'Passport';
    }

    // Seaman Book kontrolü - SBK, SEAMAN, BOOK
    if (
        strpos($filename_lower, 'seaman') !== false ||
        strpos($filename_lower, 'book') !== false ||
        strpos($filename_lower, '-sbk ') !== false ||
        strpos($filename_lower, '_sbk_') !== false ||
        strpos($filename_lower, 'seaman book') !== false
    ) {
        return 'Seaman Book';
    }

    // Medical Certificate kontrolü
    if (
        strpos($filename_lower, 'medical') !== false ||
        strpos($filename_lower, 'health') !== false ||
        strpos($filename_lower, 'saglik') !== false ||
        strpos($filename_lower, '-med ') !== false ||
        strpos($filename_lower, '_med_') !== false ||
        strpos($filename_lower, 'certificate') !== false
    ) {
        return 'Medical Certificate';
    }

    // SID kontrolü
    if (
        strpos($filename_lower, 'sid') !== false ||
        strpos($filename_lower, '-sid ') !== false ||
        strpos($filename_lower, '_sid_') !== false
    ) {
        return 'SID';
    }

    return 'Other';
}

/**
 * Debug fonksiyonu - İsim eşleştirme sürecini gösterir
 */
function debug_name_matching($crew_data, $processed_attachments)
{
    echo "<h3>Debug: İsim Eşleştirme Süreci</h3>";

    $all_crews = array_merge($crew_data['on_signers'], $crew_data['off_signers']);

    echo "<h4>Crew Members:</h4>";
    foreach ($all_crews as $index => $crew) {
        echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
        echo "<strong>Crew #" . ($index + 1) . ":</strong> " . htmlspecialchars($crew['name']) . "<br>";
        echo "<strong>Firstname:</strong> " . htmlspecialchars($crew['firstname']) . "<br>";
        echo "<strong>Lastname:</strong> " . htmlspecialchars($crew['lastname']) . "<br>";
        echo "<strong>Rank:</strong> " . htmlspecialchars($crew['rank']) . "<br>";

        // Format 2 ek bilgileri
        if (isset($crew['mobile_phone']) && $crew['mobile_phone']) {
            echo "<strong>Mobile:</strong> " . htmlspecialchars($crew['mobile_phone']) . "<br>";
        }
        if (isset($crew['email']) && $crew['email']) {
            echo "<strong>Email:</strong> " . htmlspecialchars($crew['email']) . "<br>";
        }

        $variations = generate_name_variations($crew);
        echo "<strong>Name Variations (" . count($variations) . " total):</strong><br>";
        foreach ($variations as $variation) {
            echo "- " . htmlspecialchars($variation) . "<br>";
        }
        echo "</div>";
    }

    echo "<h4>Attachments:</h4>";
    foreach ($processed_attachments as $index => $attachment) {
        echo "<div style='border: 1px solid #ddd; margin: 10px; padding: 10px; background: #f9f9f9;'>";
        echo "<strong>Attachment #" . ($index + 1) . ":</strong> " . htmlspecialchars($attachment['filename']) . "<br>";
        echo "<strong>Lowercase:</strong> " . htmlspecialchars(strtolower($attachment['filename'])) . "<br>";
        echo "<strong>Document Type:</strong> " . determine_document_type($attachment['filename']) . "<br>";

        echo "<strong>Matching attempts:</strong><br>";
        foreach ($all_crews as $crew_index => $crew) {
            $variations = generate_name_variations($crew);
            $found_match = false;
            foreach ($variations as $variation) {
                if (strpos(strtolower($attachment['filename']), strtolower($variation)) !== false) {
                    echo "✅ MATCH with Crew #" . ($crew_index + 1) . " (" . htmlspecialchars($crew['name']) . ") using variation: " . htmlspecialchars($variation) . "<br>";
                    $found_match = true;
                    break;
                }
            }
            if (!$found_match) {
                echo "❌ No match with Crew #" . ($crew_index + 1) . " (" . htmlspecialchars($crew['name']) . ")<br>";
            }
        }
        echo "</div>";
    }
}

/**
 * Format 2 Parser - ONSIGNER/OFFSIGNER format
 *
 * @param string $text - Ayrıştırılacak metin
 * @param array $processed_attachments - İşlenmiş ek dosyalar dizisi
 * @return array - Yapılandırılmış crew member verileri
 */
function parse_crew_format_2($text, $processed_attachments = [])
{
    $result = [
        'on_signers' => [],
        'off_signers' => [],
        'on_signers_flight_details' => '',
        'off_signers_flight_details' => '',
        'statistics' => [
            'on_signers_count' => 0,
            'off_signers_count' => 0,
            'total_attachments_matched' => 0
        ]
    ];

    // Metni satırlara böl
    $lines = explode("\n", $text);
    $current_section = '';
    $current_crew_data = '';

    foreach ($lines as $line) {
        $line = trim($line);

        // Bölüm başlıklarını tespit et
        if (strtoupper($line) == 'ONSIGNER') {
            // Önceki crew member verisini tamamla
            if ($current_crew_data && $current_section == 'off_signers') {
                $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                $current_crew_data = '';
            }

            $current_section = 'on_signers';
            continue;
        }

        if (strtoupper($line) == 'OFFSIGNER') {
            // Önceki crew member verisini tamamla
            if ($current_crew_data && $current_section == 'on_signers') {
                $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                $current_crew_data = '';
            }

            $current_section = 'off_signers';
            continue;
        }

        // Boş satırları atla
        if (empty($line)) {
            // Eğer crew data varsa ve boş satır gelirse, crew member'ı tamamla
            if ($current_crew_data) {
                if ($current_section == 'on_signers') {
                    $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                } elseif ($current_section == 'off_signers') {
                    $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                }
                $current_crew_data = '';
            }
            continue;
        }

        // Crew member verilerini topla
        if ($current_section == 'on_signers' || $current_section == 'off_signers') {
            // Yeni crew member başlangıcını tespit et (Surname: ile başlayan satır)
            if (preg_match('/^Surname:\s*(.+)/', $line)) {
                // Önceki crew member verisini kaydet
                if ($current_crew_data) {
                    if ($current_section == 'on_signers') {
                        $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                    } else {
                        $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                    }
                }
                $current_crew_data = $line . "\n";
            } else {
                $current_crew_data .= $line . "\n";
            }
        }
    }

    // Son crew member verisini kaydet
    if ($current_crew_data) {
        if ($current_section == 'on_signers') {
            $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
        } elseif ($current_section == 'off_signers') {
            $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
        }
    }

    // İstatistikleri güncelle
    $result['statistics']['on_signers_count'] = count($result['on_signers']);
    $result['statistics']['off_signers_count'] = count($result['off_signers']);

    // Ek dosyaları crew member'lara eşleştir
    if (!empty($processed_attachments)) {
        $result = match_attachments_to_crew($result, $processed_attachments);
    }

    return $result;
}

/**
 * Format 2 için tek bir crew member verisini ayrıştırır
 *
 * @param string $crew_data - Tek crew member'a ait ham metin verisi
 * @return array - Ayrıştırılmış crew member bilgileri
 */
function parse_single_crew_member_format_2($crew_data)
{
    $crew = [
        'name' => '',
        'firstname' => '',
        'lastname' => '',
        'rank' => '',
        'passport' => [
            'number' => '',
            'issued' => '',
            'expiry' => '',
            'issue_place' => ''
        ],
        'seaman_book' => [
            'number' => '',
            'issued' => '',
            'expiry' => '',
            'issue_place' => ''
        ],
        'nationality' => '',
        'date_of_birth' => '',
        'place_of_birth' => '',
        'mobile_phone' => '',
        'email' => '',
        'attachments' => []
    ];

    $lines = explode("\n", trim($crew_data));

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // Surname
        if (preg_match('/^Surname:\s*(.+)$/i', $line, $matches)) {
            $crew['lastname'] = trim($matches[1]);
        }

        // Name (firstname)
        if (preg_match('/^Name:\s*(.+)$/i', $line, $matches)) {
            $crew['firstname'] = trim($matches[1]);
        }

        // Rank
        if (preg_match('/^Rank:\s*(.+)$/i', $line, $matches)) {
            $crew['rank'] = trim($matches[1]);
        }

        // Nationality
        if (preg_match('/^Nationality:\s*(.+)$/i', $line, $matches)) {
            $crew['nationality'] = trim($matches[1]);
        }

        // DOB (Date of Birth)
        if (preg_match('/^DOB:\s*(.+)$/i', $line, $matches)) {
            $crew['date_of_birth'] = trim($matches[1]);
        }

        // POB (Place of Birth)
        if (preg_match('/^POB:\s*(.+)$/i', $line, $matches)) {
            $crew['place_of_birth'] = trim($matches[1]);
        }

        // Mobile Phone
        if (preg_match('/^Mobile Phone:\s*(.+)$/i', $line, $matches)) {
            $crew['mobile_phone'] = trim($matches[1]);
        }

        // Email
        if (preg_match('/^Email:\s*(.+)$/i', $line, $matches)) {
            $crew['email'] = trim($matches[1]);
        }

        // Passport No
        if (preg_match('/^Passport No\.?:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['number'] = trim($matches[1]);
        }

        // Passport Issue Place
        if (preg_match('/^Passport Issue Place:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['issue_place'] = trim($matches[1]);
        }

        // Passport Issue Date
        if (preg_match('/^Passport Issue Date:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['issued'] = trim($matches[1]);
        }

        // Passport Expiry Date
        if (preg_match('/^Passport Expiry Date:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['expiry'] = trim($matches[1]);
        }

        // Seaman's Book No
        if (preg_match('/^Seaman\'?s Book No\.?:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['number'] = trim($matches[1]);
        }

        // Seaman's Book Issue Place
        if (preg_match('/^Seaman\'?s Book Issue Place:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['issue_place'] = trim($matches[1]);
        }

        // Seaman's Book Issue Date
        if (preg_match('/^Seaman\'?s Book Issue Date:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['issued'] = trim($matches[1]);
        }

        // Seaman's Book Expiry Date
        if (preg_match('/^Seaman\'?s Book Expiry Date:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['expiry'] = trim($matches[1]);
        }
    }

    // Tam ismi oluştur
    if ($crew['lastname'] && $crew['firstname']) {
        $crew['name'] = $crew['lastname'] . ',' . $crew['firstname'];
    }

    return $crew;
}

// Gerekli kontroller
if (!isset($_POST['selected_text']) || empty($_POST['selected_text'])) {
    fn_set_notice('Seçili metin bulunamadı.', 'E');
    fn_redirect(fn_menu_link('mailbox/inbox'));
}

if (!isset($_POST['target_section']) || empty($_POST['target_section'])) {
    fn_set_notice('Aktarılacak bölüm seçilmedi.', 'E');
    fn_redirect(fn_menu_link('mailbox/inbox'));
}

// Gelen verileri al
$selected_text = $_POST['selected_text'];
$target_section = $_POST['target_section'];
$email_id = isset($_POST['email_id']) ? (int)$_POST['email_id'] : 0;
$folder = isset($_POST['folder']) ? $_POST['folder'] : 'INBOX';
$attachments = isset($_POST['attachments']) ? $_POST['attachments'] : [];

// Ekleri işle
$processed_attachments = [];
if (!empty($attachments) && $email_id > 0) {
    try {
        // IMAP bağlantısı aç
        $mailbox = mailbox_connect();

        // Klasör parametresine göre uygun klasöre geç
        if ($folder != 'INBOX') {
            // Mevcut bağlantıyı kapat
            @imap_close($mailbox);

            $host = parse_url($mailbox_config['imap_host'], PHP_URL_HOST);
            $target_folder = null;

            if ($folder == 'sent') {
                // Sent klasörüne yeni bağlantı aç
                $possible_folders = array('Sent', 'Sent Items', 'Gönderilmiş Öğeler');

                foreach ($possible_folders as $folder_name) {
                    $mailbox = @imap_open(
                        $host . ':993/imap/ssl/novalidate-cert}' . $folder_name,
                        $mailbox_config['username'],
                        $mailbox_config['password']
                    );
                    if ($mailbox) {
                        $target_folder = $folder_name;
                        break;
                    }
                }

                if (!$mailbox) {
                    throw new Exception('Giden kutusu klasörüne erişilemiyor: ' . imap_last_error());
                }
            } elseif ($folder == 'trash') {
                // Çöp kutusu klasörüne yeni bağlantı aç
                $possible_folders = array('Trash', 'Deleted Items', 'Çöp Kutusu', 'Deleted');

                foreach ($possible_folders as $folder_name) {
                    $mailbox = @imap_open(
                        $host . ':993/imap/ssl/novalidate-cert}' . $folder_name,
                        $mailbox_config['username'],
                        $mailbox_config['password']
                    );
                    if ($mailbox) {
                        $target_folder = $folder_name;
                        break;
                    }
                }

                if (!$mailbox) {
                    throw new Exception('Çöp kutusu klasörüne erişilemiyor: ' . imap_last_error());
                }
            }
        }

        // Mail içeriğini al
        $email = get_email_content($mailbox, $email_id);

        // tmp_move klasörünün varlığını kontrol et, yoksa oluştur
        $tmp_move_dir = ADDONS_DIR . '/mailbox/tmp_move';
        if (!file_exists($tmp_move_dir)) {
            mkdir($tmp_move_dir, 0755, true);
        }

        // Seçilen ekleri işle
        foreach ($attachments as $part_num) {
            foreach ($email['attachments'] as $attachment) {
                if ($attachment['part_num'] == $part_num) {
                    // Ek dosyasını indir
                    $attachment_data = imap_fetchbody($mailbox, $email_id, $attachment['part_num']);

                    // Encoding'e göre decode et
                    if ($attachment['encoding'] == 3) { // BASE64
                        $attachment_data = base64_decode($attachment_data);
                    } elseif ($attachment['encoding'] == 4) { // QUOTED-PRINTABLE
                        $attachment_data = quoted_printable_decode($attachment_data);
                    }

                    // Dosyayı tmp_move klasörüne kaydet
                    $safe_filename = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $attachment['filename']);
                    $file_path = $tmp_move_dir . '/' . $safe_filename;

                    // Aynı isimde dosya varsa, isim sonuna sayı ekle
                    $counter = 1;
                    $original_name = pathinfo($file_path, PATHINFO_FILENAME);
                    $extension = pathinfo($file_path, PATHINFO_EXTENSION);

                    while (file_exists($file_path)) {
                        $file_path = $tmp_move_dir . '/' . $original_name . '_' . $counter . '.' . $extension;
                        $counter++;
                    }

                    // Dosyayı kaydet
                    if (file_put_contents($file_path, $attachment_data)) {
                        $processed_attachments[] = [
                            'filename' => $attachment['filename'],
                            'path' => $file_path,
                            'size' => isset($attachment['size']) ? $attachment['size'] : filesize($file_path)
                        ];
                    }

                    break;
                }
            }
        }

        // IMAP bağlantısını kapat
        imap_close($mailbox);
    } catch (Exception $e) {
        fn_set_notice('Ekler işlenirken hata oluştu: ' . $e->getMessage(), 'E');
    }
}

// Crew member text parsing işlemi
$parsed_crew_data = null;
if (!empty($selected_text)) {
    // Metinde crew member verisi olup olmadığını kontrol et
    if (
        strpos($selected_text, 'ON-SIGNERS:') !== false ||
        strpos($selected_text, 'OFF-SIGNERS:') !== false ||
        strpos($selected_text, 'ONSIGNER') !== false ||
        strpos($selected_text, 'OFFSIGNER') !== false ||
        (strpos($selected_text, 'Surname:') !== false && strpos($selected_text, 'Name:') !== false)
    ) {

        $parsed_crew_data = parse_crew_member_text($selected_text, $processed_attachments);

        // Debug için parsed data'yı session'a kaydet
        $_SESSION['calisma']['parsed_crew_data'] = $parsed_crew_data;
    }
}

// Sonuçları göster
?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Seçili İçerik Aktarıldı</h3>
    </div>
    <div class="card-body">
        <div class="mb-4">
            <h4 class="fw-bold">Aktarılan Metin</h4>
            <div class="border rounded p-3 bg-light">
                <?php echo nl2br(htmlspecialchars($selected_text)); ?>
            </div>
        </div>

        <div class="mb-4">
            <h4 class="fw-bold">Aktarılan Bölüm</h4>
            <div class="border rounded p-3">
                <?php
                switch ($target_section) {
                    case 'musteri_notlari':
                        echo 'Müşteri Notları';
                        break;
                    case 'proje_dokumanlari':
                        echo 'Proje Dökümanları';
                        break;
                    case 'gorev_listesi':
                        echo 'Görev Listesi';
                        break;
                    case 'bilgi_bankasi':
                        echo 'Bilgi Bankası';
                        break;
                    default:
                        echo $target_section;
                }
                ?>
            </div>
        </div>

        <?php if (!empty($processed_attachments)): ?>
            <div class="mb-4">
                <h4 class="fw-bold">Aktarılan Ekler</h4>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Dosya Adı</th>
                                <th>Boyut</th>
                                <th>Geçici Konum</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($processed_attachments as $attachment): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($attachment['filename']); ?></td>
                                    <td><?php echo format_file_size($attachment['size']); ?></td>
                                    <td><code><?php echo htmlspecialchars($attachment['path']); ?></code></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($parsed_crew_data): ?>
            <div class="mb-4">
                <h4 class="fw-bold">Ayrıştırılmış Crew Member Verileri</h4>

                <!-- Format bilgisi -->
                <?php
                $detected_format = detect_text_format($selected_text);
                $format_name = ($detected_format == 'format_2') ? 'ONSIGNER/OFFSIGNER Format' : 'ON-SIGNERS/OFF-SIGNERS Format';
                ?>
                <div class="alert alert-success">
                    <strong>Tespit Edilen Format:</strong> <?php echo htmlspecialchars($format_name); ?> (<?php echo htmlspecialchars($detected_format); ?>)
                </div>

                <!-- Success Message -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Başarılı!</strong> <?php echo htmlspecialchars($_SESSION['success_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <!-- Debug output -->
                <?php if (!empty($processed_attachments)): ?>
                    <div class="alert alert-info">
                        <h5>Debug: İsim Eşleştirme Süreci</h5>
                        <?php debug_name_matching($parsed_crew_data, $processed_attachments); ?>
                    </div>
                <?php endif; ?>

                <!-- İstatistikler -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5><?php echo $parsed_crew_data['statistics']['on_signers_count']; ?></h5>
                                <small>ON-SIGNERS</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5><?php echo $parsed_crew_data['statistics']['off_signers_count']; ?></h5>
                                <small>OFF-SIGNERS</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5><?php echo $parsed_crew_data['statistics']['total_attachments_matched']; ?></h5>
                                <small>Eşleşen Ek Dosya</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ON-SIGNERS Tablosu -->
                <?php if (!empty($parsed_crew_data['on_signers'])): ?>
                    <div class="mb-4">
                        <h5 class="text-primary">ON-SIGNERS</h5>
                        <form id="crewDataForm" method="post" action="">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>Ad Soyad</th>
                                            <th>Rütbe</th>
                                            <th>Pasaport</th>
                                            <th>Seaman Book</th>
                                            <th>Uyruk</th>
                                            <th>Doğum Tarihi</th>
                                            <?php if ($detected_format == 'format_2'): ?>
                                                <th>İletişim</th>
                                            <?php endif; ?>
                                            <th>Ek Dosyalar</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($parsed_crew_data['on_signers'] as $index => $crew): ?>
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][name]"
                                                        value="<?php echo htmlspecialchars($crew['name']); ?>"
                                                        placeholder="Ad Soyad">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][firstname]"
                                                        value="<?php echo htmlspecialchars($crew['firstname']); ?>"
                                                        placeholder="Ad">
                                                    <input type="text" class="form-control form-control-sm mt-1"
                                                        name="on_signers[<?php echo $index; ?>][lastname]"
                                                        value="<?php echo htmlspecialchars($crew['lastname']); ?>"
                                                        placeholder="Soyad">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][rank]"
                                                        value="<?php echo htmlspecialchars($crew['rank']); ?>"
                                                        placeholder="Rütbe">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][passport][number]"
                                                        value="<?php echo htmlspecialchars($crew['passport']['number']); ?>"
                                                        placeholder="Pasaport No">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][passport][issued]"
                                                        value="<?php echo htmlspecialchars($crew['passport']['issued']); ?>"
                                                        placeholder="Veriliş Tarihi">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][passport][expiry]"
                                                        value="<?php echo htmlspecialchars($crew['passport']['expiry']); ?>"
                                                        placeholder="Bitiş Tarihi">
                                                    <?php if ($detected_format == 'format_2'): ?>
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="on_signers[<?php echo $index; ?>][passport][issue_place]"
                                                            value="<?php echo htmlspecialchars($crew['passport']['issue_place'] ?? ''); ?>"
                                                            placeholder="Veriliş Yeri">
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][seaman_book][number]"
                                                        value="<?php echo htmlspecialchars($crew['seaman_book']['number']); ?>"
                                                        placeholder="Seaman Book No">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][seaman_book][issued]"
                                                        value="<?php echo htmlspecialchars($crew['seaman_book']['issued']); ?>"
                                                        placeholder="Veriliş Tarihi">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][seaman_book][expiry]"
                                                        value="<?php echo htmlspecialchars($crew['seaman_book']['expiry']); ?>"
                                                        placeholder="Bitiş Tarihi">
                                                    <?php if ($detected_format == 'format_2'): ?>
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="on_signers[<?php echo $index; ?>][seaman_book][issue_place]"
                                                            value="<?php echo htmlspecialchars($crew['seaman_book']['issue_place'] ?? ''); ?>"
                                                            placeholder="Veriliş Yeri">
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][nationality]"
                                                        value="<?php echo htmlspecialchars($crew['nationality']); ?>"
                                                        placeholder="Uyruk">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][date_of_birth]"
                                                        value="<?php echo htmlspecialchars($crew['date_of_birth']); ?>"
                                                        placeholder="Doğum Tarihi">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][place_of_birth]"
                                                        value="<?php echo htmlspecialchars($crew['place_of_birth']); ?>"
                                                        placeholder="Doğum Yeri">
                                                </td>
                                                <?php if ($detected_format == 'format_2'): ?>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="on_signers[<?php echo $index; ?>][mobile_phone]"
                                                            value="<?php echo htmlspecialchars($crew['mobile_phone'] ?? ''); ?>"
                                                            placeholder="Cep Telefonu">
                                                        <input type="email" class="form-control form-control-sm"
                                                            name="on_signers[<?php echo $index; ?>][email]"
                                                            value="<?php echo htmlspecialchars($crew['email'] ?? ''); ?>"
                                                            placeholder="E-posta">
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <div class="attachment-manager" data-crew-index="<?php echo $index; ?>" data-crew-type="on_signers">
                                                        <label class="form-label small">Ek Dosyalar:</label>

                                                        <!-- Tüm attachments için select -->
                                                        <div class="mb-2">
                                                            <select class="form-select form-select-sm attachment-select" multiple>
                                                                <?php foreach ($processed_attachments as $att_index => $attachment): ?>
                                                                    <?php
                                                                    $is_matched = false;
                                                                    foreach ($crew['attachments'] as $crew_att) {
                                                                        if ($crew_att['filename'] == $attachment['filename']) {
                                                                            $is_matched = true;
                                                                            break;
                                                                        }
                                                                    }
                                                                    ?>
                                                                    <option value="<?php echo $att_index; ?>"
                                                                        <?php echo $is_matched ? 'selected' : ''; ?>
                                                                        data-filename="<?php echo htmlspecialchars($attachment['filename']); ?>">
                                                                        <?php echo htmlspecialchars($attachment['filename']); ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>

                                                        <!-- Seçili dosyalar için document type -->
                                                        <div class="selected-attachments">
                                                            <?php foreach ($crew['attachments'] as $att_index => $attachment): ?>
                                                                <div class="attachment-item mb-2 p-2 border rounded">
                                                                    <input type="hidden"
                                                                        name="on_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][filename]"
                                                                        value="<?php echo htmlspecialchars($attachment['filename']); ?>">
                                                                    <input type="hidden"
                                                                        name="on_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][path]"
                                                                        value="<?php echo htmlspecialchars($attachment['path']); ?>">

                                                                    <small class="d-block text-muted mb-1"><?php echo htmlspecialchars($attachment['filename']); ?></small>
                                                                    <select class="form-select form-select-sm"
                                                                        name="on_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][type]">
                                                                        <option value="Passport" <?php echo ($attachment['type'] == 'Passport') ? 'selected' : ''; ?>>Passport</option>
                                                                        <option value="Seaman Book" <?php echo ($attachment['type'] == 'Seaman Book') ? 'selected' : ''; ?>>Seaman Book</option>
                                                                        <option value="Medical Certificate" <?php echo ($attachment['type'] == 'Medical Certificate') ? 'selected' : ''; ?>>Medical Certificate</option>
                                                                        <option value="SID" <?php echo ($attachment['type'] == 'SID') ? 'selected' : ''; ?>>SID</option>
                                                                        <option value="Other" <?php echo ($attachment['type'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                                                    </select>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Aktar Butonu -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">Toplam:
                                                <span class="badge bg-primary"><?php echo count($parsed_crew_data['on_signers']); ?> ON-SIGNERS</span>
                                                <span class="badge bg-success"><?php echo count($parsed_crew_data['off_signers']); ?> OFF-SIGNERS</span>
                                            </h6>
                                        </div>
                                        <div>
                                            <button type="submit" name="export_crew_data" value="1" class="btn btn-success btn-lg">
                                                <i class="fas fa-download me-2"></i>Crew Verilerini Aktar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- OFF-SIGNERS Tablosu -->
                    <?php if (!empty($parsed_crew_data['off_signers'])): ?>
                        <div class="mb-4">
                            <h5 class="text-success">OFF-SIGNERS</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead class="table-success">
                                        <tr>
                                            <th>Ad Soyad</th>
                                            <th>Rütbe</th>
                                            <th>Pasaport</th>
                                            <th>Seaman Book</th>
                                            <th>Uyruk</th>
                                            <th>Doğum Tarihi</th>
                                            <?php if ($detected_format == 'format_2'): ?>
                                                <th>İletişim</th>
                                            <?php endif; ?>
                                            <th>Ek Dosyalar</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($parsed_crew_data['off_signers'] as $index => $crew): ?>
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="off_signers[<?php echo $index; ?>][name]"
                                                        value="<?php echo htmlspecialchars($crew['name']); ?>"
                                                        placeholder="Ad Soyad">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="off_signers[<?php echo $index; ?>][firstname]"
                                                        value="<?php echo htmlspecialchars($crew['firstname']); ?>"
                                                        placeholder="Ad">
                                                    <input type="text" class="form-control form-control-sm mt-1"
                                                        name="off_signers[<?php echo $index; ?>][lastname]"
                                                        value="<?php echo htmlspecialchars($crew['lastname']); ?>"
                                                        placeholder="Soyad">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="off_signers[<?php echo $index; ?>][rank]"
                                                        value="<?php echo htmlspecialchars($crew['rank']); ?>"
                                                        placeholder="Rütbe">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="off_signers[<?php echo $index; ?>][passport][number]"
                                                        value="<?php echo htmlspecialchars($crew['passport']['number']); ?>"
                                                        placeholder="Pasaport No">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="off_signers[<?php echo $index; ?>][passport][issued]"
                                                        value="<?php echo htmlspecialchars($crew['passport']['issued']); ?>"
                                                        placeholder="Veriliş Tarihi">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="off_signers[<?php echo $index; ?>][passport][expiry]"
                                                        value="<?php echo htmlspecialchars($crew['passport']['expiry']); ?>"
                                                        placeholder="Bitiş Tarihi">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="off_signers[<?php echo $index; ?>][seaman_book][number]"
                                                        value="<?php echo htmlspecialchars($crew['seaman_book']['number']); ?>"
                                                        placeholder="Seaman Book No">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="off_signers[<?php echo $index; ?>][seaman_book][issued]"
                                                        value="<?php echo htmlspecialchars($crew['seaman_book']['issued']); ?>"
                                                        placeholder="Veriliş Tarihi">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="off_signers[<?php echo $index; ?>][seaman_book][expiry]"
                                                        value="<?php echo htmlspecialchars($crew['seaman_book']['expiry']); ?>"
                                                        placeholder="Bitiş Tarihi">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="off_signers[<?php echo $index; ?>][nationality]"
                                                        value="<?php echo htmlspecialchars($crew['nationality']); ?>"
                                                        placeholder="Uyruk">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="off_signers[<?php echo $index; ?>][date_of_birth]"
                                                        value="<?php echo htmlspecialchars($crew['date_of_birth']); ?>"
                                                        placeholder="Doğum Tarihi">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="off_signers[<?php echo $index; ?>][place_of_birth]"
                                                        value="<?php echo htmlspecialchars($crew['place_of_birth']); ?>"
                                                        placeholder="Doğum Yeri">
                                                </td>
                                                <?php if ($detected_format == 'format_2'): ?>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="off_signers[<?php echo $index; ?>][mobile_phone]"
                                                            value="<?php echo htmlspecialchars($crew['mobile_phone'] ?? ''); ?>"
                                                            placeholder="Cep Telefonu">
                                                        <input type="email" class="form-control form-control-sm"
                                                            name="off_signers[<?php echo $index; ?>][email]"
                                                            value="<?php echo htmlspecialchars($crew['email'] ?? ''); ?>"
                                                            placeholder="E-posta">
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <div class="attachment-manager" data-crew-index="<?php echo $index; ?>" data-crew-type="off_signers">
                                                        <label class="form-label small">Ek Dosyalar:</label>

                                                        <!-- Basit attachment listesi -->
                                                        <?php foreach ($crew['attachments'] as $att_index => $attachment): ?>
                                                            <div class="attachment-item mb-2 p-2 border rounded">
                                                                <input type="hidden"
                                                                    name="off_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][filename]"
                                                                    value="<?php echo htmlspecialchars($attachment['filename']); ?>">
                                                                <input type="hidden"
                                                                    name="off_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][path]"
                                                                    value="<?php echo htmlspecialchars($attachment['path']); ?>">

                                                                <small class="d-block text-muted mb-1"><?php echo htmlspecialchars($attachment['filename']); ?></small>
                                                                <select class="form-select form-select-sm"
                                                                    name="off_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][type]">
                                                                    <option value="Passport" <?php echo ($attachment['type'] == 'Passport') ? 'selected' : ''; ?>>Passport</option>
                                                                    <option value="Seaman Book" <?php echo ($attachment['type'] == 'Seaman Book') ? 'selected' : ''; ?>>Seaman Book</option>
                                                                    <option value="Medical Certificate" <?php echo ($attachment['type'] == 'Medical Certificate') ? 'selected' : ''; ?>>Medical Certificate</option>
                                                                    <option value="SID" <?php echo ($attachment['type'] == 'SID') ? 'selected' : ''; ?>>SID</option>
                                                                    <option value="Other" <?php echo ($attachment['type'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                                                </select>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Flight Details -->
                    <?php if (!empty($parsed_crew_data['on_signers_flight_details']) || !empty($parsed_crew_data['off_signers_flight_details'])): ?>
                        <div class="mb-4">
                            <h5>Uçuş Detayları</h5>
                            <?php if (!empty($parsed_crew_data['on_signers_flight_details'])): ?>
                                <div class="mb-3">
                                    <h6 class="text-primary">ON-SIGNERS Uçuş Detayları</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <pre><?php echo htmlspecialchars(trim($parsed_crew_data['on_signers_flight_details'])); ?></pre>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($parsed_crew_data['off_signers_flight_details'])): ?>
                                <div class="mb-3">
                                    <h6 class="text-success">OFF-SIGNERS Uçuş Detayları</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <pre><?php echo htmlspecialchars(trim($parsed_crew_data['off_signers_flight_details'])); ?></pre>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    </div>
                <?php endif; ?>

                <div class="alert alert-info">
                    <i class="ki-duotone ki-information-5 fs-2 me-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                    </i>
                    Bu sayfada gösterilen bilgiler, seçtiğiniz içeriğin başarıyla işlendiğini göstermektedir. Gerçek bir uygulamada, bu veriler ilgili modüle aktarılacaktır.
                </div>
            </div>
            <div class="card-footer">
                <a href="<?php fn_menu_link('mailbox/inbox'); ?>" class="btn btn-primary">Gelen Kutusuna Dön</a>
            </div>
    </div>