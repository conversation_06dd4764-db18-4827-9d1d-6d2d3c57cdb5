<?php
// Aktif hesap ID'sini al
$account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

// Aktif sayfa bilgisini al
$current_page = isset($current_page) ? $current_page : '';

// Mail sayılarını al
$inbox_count = get_folder_email_count('INBOX', $account_id);
$outbox_count = get_folder_email_count('SENT', $account_id);
$drafts_count = get_folder_email_count('DRAFTS', $account_id);
$trash_count = get_folder_email_count('TRASH', $account_id);

// Etiketleri getir
$labels = get_labels();
?>

<!--begin::Sidebar-->
<div class="flex-column flex-lg-row-auto w-100 w-lg-275px mb-10 mb-lg-0">
    <!--begin::Sticky aside-->
    <div class="card card-flush mb-0" data-kt-sticky="true" data-kt-sticky-name="inbox-aside-sticky" data-kt-sticky-offset="{default: false, xl: '0px'}" data-kt-sticky-width="{lg: '275px'}" data-kt-sticky-left="auto" data-kt-sticky-top="150px" data-kt-sticky-animation="false" data-kt-sticky-zindex="95">
        <!--begin::Aside content-->
        <div class="card-body">
            <!--begin::Button-->
            <a href="<?php echo fn_menu_link('mailbox/compose'); ?>" class="btn btn-sm w-100 btn-info mb-6 btn-evo-loading"><i class="ki-duotone ki-send fs-2 "><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span></i>New Message</a>
            <!--end::Button-->

            <!--begin::Menu-->
            <div class="menu menu-info menu-hover-danger menu-active-info menu-column menu-rounded menu-state-bg menu-state-title-info menu-state-icon-primary menu-state-bullet-info mb-10">
                <!--begin::Menu item-->
                <div class="menu-item mb-3">
                    <!--begin::Inbox-->
                    <a href="<?php echo fn_menu_link('mailbox/inbox'); ?>" class="menu-link <?php echo $current_page == 'inbox' ? 'bg-light' : ''; ?>">
                        <span class="menu-icon">
                            <i class="ki-duotone ki-sms fs-2 me-3">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </span>
                        <span class="menu-title fw-bold">Gelen Kutusu</span>
                        <span class="menu-badge">
                            <span class="badge badge-light-info"><?php echo $inbox_count; ?></span>
                        </span>
                    </a>
                    <!--end::Inbox-->
                </div>
                <!--end::Menu item-->

                <!--begin::Menu item-->
                <div class="menu-item mb-3">
                    <!--begin::Outbox-->
                    <a href="<?php echo fn_menu_link('mailbox/outbox'); ?>" class="menu-link <?php echo $current_page == 'outbox' ? 'bg-light ' : ''; ?>">
                        <span class="menu-icon">
                            <i class="ki-duotone ki-send fs-2 me-3">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </span>
                        <span class="menu-title menu-active-info fw-bold">Giden Kutusu</span>
                        <span class="menu-badge">
                            <span class="badge badge-light-info"><?php echo $outbox_count; ?></span>
                        </span>
                    </a>
                    <!--end::Outbox-->
                </div>
                <!--end::Menu item-->

                <!--begin::Menu item-->
                <div class="menu-item mb-3">
                    <!--begin::Drafts-->
                    <a href="<?php echo fn_menu_link('mailbox/drafts'); ?>" class="menu-link <?php echo $current_page == 'drafts' ? 'bg-light' : ''; ?>">
                        <span class="menu-icon">
                            <i class="ki-duotone ki-document fs-2 me-3">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </span>
                        <span class="menu-title fw-bold">Taslaklar</span>
                        <span class="menu-badge">
                            <span class="badge badge-light-info"><?php echo $drafts_count; ?></span>
                        </span>
                    </a>
                    <!--end::Drafts-->
                </div>
                <!--end::Menu item-->

                <!--begin::Menu item-->
                <div class="menu-item mb-3">
                    <!--begin::Trash-->
                    <a href="<?php echo fn_menu_link('mailbox/trash'); ?>" class="menu-link <?php echo $current_page == 'trash' ? 'bg-light' : ''; ?>">
                        <span class="menu-icon">
                            <i class="ki-duotone ki-trash fs-2 me-3">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                                <span class="path4"></span>
                                <span class="path5"></span>
                            </i>
                        </span>
                        <span class="menu-title fw-bold">Çöp Kutusu</span>
                        <span class="menu-badge">
                            <span class="badge badge-light-info"><?php echo $trash_count; ?></span>
                        </span>
                    </a>
                    <!--end::Trash-->
                </div>
                <!--end::Menu item-->
            </div>
            <!--end::Menu-->

            <!--begin::Menu-->
            <div class="menu menu-column menu-rounded menu-state-bg menu-state-title-info">
                <!--begin::Menu content-->
                <div class="menu-item">
                    <div class="menu-content">
                        <span class="menu-heading fw-bold text-uppercase fs-7">Etiketler</span>
                    </div>
                </div>
                <!--end::Menu content-->

                <?php if (!empty($labels)): ?>
                    <?php foreach ($labels as $label): ?>
                        <!--begin::Menu item-->
                        <div class="menu-item mb-2">
                            <a href="<?php echo fn_menu_link('mailbox/inbox&label=' . $label->id); ?>" class="menu-link text-gray-700 <?php echo ($_GET['label'] == $label->id) ? 'bg-light' : ''; ?>">
                                <span class="menu-icon"><span class="bullet me-1"></span></span>
                                <span class="menu-title fw-semibold"><?php echo $label->name; ?></span>
                                <?php
                                // Etiket mail sayısını al
                                $label_count = get_label_email_count($label->id, $account_id);
                                ?>
                                <span class="menu-badge">
                                    <span class="badge" style="background-color: <?php echo $label->color; ?>; color: <?php echo get_contrast_color($label->color); ?>"><?php echo $label_count; ?></span>
                                </span>
                            </a>
                        </div>
                        <!--end::Menu item-->
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="menu-item">
                        <div class="menu-content">
                            <span class="text-muted fs-7 px-5">Henüz etiket oluşturulmadı</span>
                        </div>
                    </div>
                <?php endif; ?>

                <!--begin::Menu item-->
                <div class="menu-item">
                    <a href="<?php echo fn_menu_link('mailbox/labels'); ?>" class="menu-link text-gray-500">
                        <span class="menu-icon">
                            <i class="ki-duotone ki-setting-2 fs-2 me-1">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </span>
                        <span class="menu-title fw-semibold">Etiketleri Yönet</span>
                    </a>
                </div>
                <div class="separator my-5 mx-5"></div>
                <!--end::Menu item-->
                <div class="menu-item">
                    <div class="menu-content">
                        <span class="menu-heading fw-bold text-uppercase fs-7">Tanımlamalar</span>
                    </div>
                </div>
                <div class="menu-item">
                    <a href="<?php echo fn_menu_link('mailbox/signatures'); ?>" class="menu-link text-gray-500">
                        <span class="menu-icon">
                            <i class="ki-duotone ki-feather fs-2 me-1">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </span>
                        <span class="menu-title fw-semibold">Imzalar</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="<?php echo fn_menu_link('mailbox/templates'); ?>" class="menu-link text-gray-500">
                        <span class="menu-icon">
                            <i class="ki-duotone ki-message-programming fs-2 me-1">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                                <span class="path4"></span>
                            </i>
                        </span>
                        <span class="menu-title fw-semibold">Hazır Mesajlar</span>
                    </a>
                </div>
            </div>
            <!--end::Menu-->
        </div>
        <!--end::Aside content-->
    </div>
    <!--end::Sticky aside-->
</div>
<!--end::Sidebar-->