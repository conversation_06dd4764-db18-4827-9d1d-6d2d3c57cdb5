<?php
// Aktif hesap bilgisini yazdır (debug için)
if (isset($_SESSION['active_mail_account'])) {
    error_log('Aktif hesap ID: ' . $_SESSION['active_mail_account']);
} else {
    error_log('Aktif hesap ID bulunamadı!');
}

try {
    // Mail silme işlemi
    if ($_POST && isset($_POST['delete_email'])) {
        try {
            // Aktif hesap ID'sini al
            $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

            $mailbox = mailbox_connect($account_id);
            $email_id = (int) $_POST['email_id'];
            if (delete_email($mailbox, $email_id)) {
                fn_set_notice('Mail başarıyla silindi.', 'S');
            }
            // Bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                @imap_close($mailbox);
            }
        } catch (Exception $e) {
            fn_set_notice($e->getMessage(), 'E');
        }
        fn_redirect(fn_menu_link('mailbox/inbox', 1));
    }

    // Seçili mailleri silme işlemi
    if ($_POST && isset($_POST['delete_selected_emails']) && isset($_POST['email_ids'])) {
        try {
            // Aktif hesap ID'sini al
            $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

            $mailbox = mailbox_connect($account_id);
            $email_ids = json_decode($_POST['email_ids'], true);

            if (is_array($email_ids) && !empty($email_ids)) {
                $deleted_count = 0;

                foreach ($email_ids as $email_id) {
                    $email_id = (int) $email_id;
                    if (delete_email($mailbox, $email_id)) {
                        $deleted_count++;
                    }
                }

                if ($deleted_count > 0) {
                    fn_set_notice($deleted_count . ' mail başarıyla silindi.', 'S');
                } else {
                    fn_set_notice('Mail silme işlemi başarısız oldu.', 'E');
                }
            } else {
                fn_set_notice('Silinecek mail seçilmedi.', 'E');
            }

            // Bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                @imap_close($mailbox);
            }
        } catch (Exception $e) {
            fn_set_notice($e->getMessage(), 'E');
        }
        fn_redirect(fn_menu_link('mailbox/inbox', 1));
    }

    // Tüm mailleri silme işlemi
    if ($_POST && isset($_POST['delete_all_emails'])) {
        try {
            // Aktif hesap ID'sini al
            $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

            $mailbox = mailbox_connect($account_id);
            if (delete_all_emails($mailbox, 'INBOX', $account_id)) {
                fn_set_notice('Tüm mailler başarıyla silindi.', 'S');
            }
            // Bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                @imap_close($mailbox);
            }
        } catch (Exception $e) {
            fn_set_notice($e->getMessage(), 'E');
        }
        fn_redirect(fn_menu_link('mailbox/inbox', 1));
    }

    // Aktif hesap ID'sini al
    $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

    // Etiket filtresini kontrol et
    $label_filter = isset($_GET['label']) ? (int) $_GET['label'] : null;

    // Etiketleri getir
    $labels = get_labels();

    // Mail listesini al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;

    if ($label_filter) {
        // Belirli bir etikete sahip mailleri getir
        $email_ids = get_emails_by_label($label_filter, $account_id, 'INBOX');
        if (!empty($email_ids)) {
            // Etiketli mailleri getir
            $mailbox = mailbox_connect($account_id);
            $emails = array();
            $total_emails = count($email_ids);
            $total_pages = ceil($total_emails / 20);
            $page = max(1, min($page, $total_pages));
            $start = ($page - 1) * 20;
            $page_email_ids = array_slice($email_ids, $start, 20);

            foreach ($page_email_ids as $email_id) {
                $header = imap_headerinfo($mailbox, $email_id);
                if ($header) {
                    // Okunma durumunu kontrol et
                    $seen = 1; // Varsayılan olarak okunmuş

                    // Unseen bayrağını kontrol et
                    if (isset($header->Unseen) && trim($header->Unseen) == 'U') {
                        $seen = 0;
                    }

                    $emails[] = array(
                        'id' => $email_id,
                        'subject' => decode_mime_string($header->subject),
                        'from' => decode_mime_string($header->from[0]->mailbox . '@' . $header->from[0]->host),
                        'date' => date('Y-m-d H:i', strtotime($header->date)),
                        'seen' => $seen
                    );
                }
            }

            // Bağlantıyı kapat
            imap_close($mailbox);

            $result = array(
                'emails' => $emails,
                'pages' => $total_pages,
                'total' => $total_emails
            );
        } else {
            // Etiketli mail yoksa boş sonuç döndür
            $result = array('emails' => array(), 'pages' => 0, 'total' => 0);
        }
    } else {
        // Tüm mailleri getir
        $result = get_email_list(null, 'INBOX', $page, 20, $account_id);
    }
} catch (Exception $e) {
    fn_set_notice($e->getMessage(), 'E');
}
?>

<!--begin::Content-->
<div class="d-flex flex-column flex-lg-row">
    <?php
    // Aktif sayfa bilgisini ayarla
    $current_page = 'inbox';

    // Aside menüyü dahil et
    include(dirname(__FILE__) . '/includes/mailbox_aside.php');
    ?>

    <!--begin::Content-->
    <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
        <!--begin::Card-->
        <div class="card">
            <div class="card-header align-items-center ps-5 py-5 gap-5">
                <!--begin::Actions-->
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative">
                        <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        <input type="text" id="mailbox-search" class="form-control form-control-sm form-control-solid mw-100 min-w-125px min-w-lg-300px ps-11" placeholder="Mail Ara...">
                    </div>
                    <!--end::Search-->

                    <?php if (!empty($result['emails'])): ?>
                        <!--begin::Bulk Actions-->
                        <div class="d-flex align-items-center">
                            <button type="button" id="delete-selected" class="btn btn-sm btn-secondary me-2" disabled>
                                <i class="ki-duotone ki-trash fs-2 me-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                    <span class="path4"></span>
                                    <span class="path5"></span>
                                </i>
                                Seçilenleri Sil (<span id="selected-count">0</span>)
                            </button>
                        </div>
                        <!--end::Bulk Actions-->

                        <!--begin::Delete All-->
                        <form method="post" class="d-inline" id="delete-all-form" onsubmit="return confirm('Tüm mailleri silmek istediğinize emin misiniz? Bu işlem geri alınamaz!')">
                            <input type="hidden" name="delete_all_emails" value="1">
                            <button type="submit" class="btn btn-sm btn-icon btn-light me-2" data-bs-toggle="tooltip" title="Tüm Mailleri Sil">
                                <i class="ki-duotone ki-trash fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                    <span class="path4"></span>
                                    <span class="path5"></span>
                                </i>
                            </button>
                        </form>
                        <!--end::Delete All-->

                        <!--begin::Filter-->
                        <?php if (!empty($labels) && 1 == 2): ?>
                            <div class="d-flex align-items-center">
                                <div class="bullet bg-primary me-3"></div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-light-primary dropdown-toggle" type="button" id="labelFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <?php if ($label_filter): ?>
                                            <?php
                                            $current_label = null;
                                            foreach ($labels as $label) {
                                                if ($label->id == $label_filter) {
                                                    $current_label = $label;
                                                    break;
                                                }
                                            }
                                            if ($current_label):
                                            ?>
                                                <span class="badge" style="background-color: <?php echo $current_label->color; ?>; color: <?php echo get_contrast_color($current_label->color); ?>">
                                                    <?php echo $current_label->name; ?>
                                                </span>
                                            <?php else: ?>
                                                Tüm Mailler
                                            <?php endif; ?>
                                        <?php else: ?>
                                            Tüm Mailler
                                        <?php endif; ?>
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="labelFilterDropdown">
                                        <li>
                                            <a class="dropdown-item <?php echo !$label_filter ? 'active' : ''; ?>" href="<?php echo fn_menu_link('mailbox/inbox'); ?>">
                                                Tüm Mailler
                                            </a>
                                        </li>
                                        <li>
                                            <hr class="dropdown-divider">
                                        </li>
                                        <?php foreach ($labels as $label): ?>
                                            <li>
                                                <a class="dropdown-item <?php echo $label_filter == $label->id ? 'active' : ''; ?>" href="<?php echo fn_menu_link('mailbox/inbox&label=' . $label->id); ?>">
                                                    <span class="badge" style="background-color: <?php echo $label->color; ?>; color: <?php echo get_contrast_color($label->color); ?>">
                                                        <?php echo $label->name; ?>
                                                    </span>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        <?php endif; ?>
                        <!--end::Filter-->
                    <?php endif; ?>
                </div>
                <!--end::Actions-->

                <!--begin::Pagination-->
                <div class="d-flex align-items-center pages_div">
                    <?php if ($result['pages'] > 1): ?>
                        <span class="fs-7 fw-semibold text-muted me-2"><?php echo ($page - 1) * 20 + 1; ?>-<?php echo min($page * 20, $result['total']); ?> / <?php echo $result['total']; ?></span>
                        <a href="<?php echo fn_menu_link('mailbox/inbox' . ($label_filter ? '&label=' . $label_filter : '') . '&page=' . max(1, $page - 1)); ?>" class="btn btn-sm btn-icon btn-light me-2 <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <i class="ki-duotone ki-left fs-2 text-gray-500">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </a>
                        <a href="<?php echo fn_menu_link('mailbox/inbox' . ($label_filter ? '&label=' . $label_filter : '') . '&page=' . min($result['pages'], $page + 1)); ?>" class="btn btn-sm btn-icon btn-light <?php echo $page >= $result['pages'] ? 'disabled' : ''; ?>">
                            <i class="ki-duotone ki-right fs-2 text-gray-500">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </a>
                    <?php endif; ?>
                </div>
                <!--end::Pagination-->
            </div>

            <div class="card-body p-0">
                <!--begin::Table-->
                <?php if (empty($result['emails'])): ?>
                    <div class="text-center py-10">
                        <i class="ki-duotone ki-message-text-2 fs-3x text-gray-400 mb-5">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        <div class="text-gray-600 fw-semibold fs-4 mb-3">Gelen kutunuzda hiç mail bulunmuyor.</div>
                        <a href="<?php echo fn_menu_link('mailbox/compose'); ?>" class="btn btn-sm btn-primary">Yeni Mail Oluştur</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table align-middle table-hover table-row-dashed table-sm">
                            <thead>
                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-35px ps-5">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" id="select-all-emails">
                                        </div>
                                    </th>
                                    <th>Gönderen</th>
                                    <th>Konu</th>
                                    <th class="text-end">Tarih</th>
                                    <th class="text-end pe-5">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($result['emails'] as $email): ?>
                                    <tr class="<?php echo $email['seen'] ? '' : ' bg-light'; ?>">
                                        <!--begin::Checkbox-->
                                        <td class="ps-5">
                                            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                <input class="form-check-input email-checkbox" type="checkbox" data-email-id="<?php echo $email['id']; ?>">
                                            </div>
                                        </td>
                                        <!--end::Checkbox-->

                                        <!--begin::Author-->
                                        <td class="w-150px">
                                            <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id']); ?>" class="d-flex align-items-center text-gray-900">
                                                <div class="symbol symbol-35px me-3">
                                                    <span class="symbol-label bg-light-primary text-primary fw-bold"><?php echo strtoupper(substr($email['from'], 0, 1)); ?></span>
                                                </div>
                                                <span class="fw-semibold"><?php echo $email['from']; ?></span>
                                            </a>
                                        </td>
                                        <!--end::Author-->

                                        <!--begin::Title-->
                                        <td>
                                            <div class="text-gray-900 mb-1">
                                                <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id']); ?>" class="text-gray-900">
                                                    <span class="fw-bold"><?php echo $email['subject']; ?></span>
                                                </a>
                                            </div>
                                            <?php
                                            // Mail etiketlerini getir
                                            $email_labels = get_email_labels($email['id'] ?? 0, $account_id, 'INBOX');
                                            if (is_array($email_labels) && !empty($email_labels)):
                                            ?>
                                                <div class="d-flex flex-wrap gap-1 mt-1">
                                                    <?php foreach ($email_labels as $label): ?>
                                                        <span class="badge" style="background-color: <?php echo $label->color; ?>; color: <?php echo get_contrast_color($label->color); ?>">
                                                            <?php echo $label->name; ?>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <!--end::Title-->

                                        <!--begin::Date-->
                                        <td class="text-end ">
                                            <span class="fw-semibold"><?php echo $email['date']; ?></span>
                                        </td>
                                        <!--end::Date-->

                                        <!--begin::Actions-->
                                        <td class="w-100px text-end pe-5">
                                            <div class="d-flex justify-content-end">
                                                <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id']); ?>" class="btn btn-sm btn-icon btn-light btn-active-light-primary me-2" data-bs-toggle="tooltip" title="Oku">
                                                    <i class="ki-duotone ki-eye fs-2">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                        <span class="path3"></span>
                                                        <span class="path4"></span>
                                                    </i>
                                                </a>

                                                <form method="post" class="d-inline delete-form" onsubmit="return confirm('Bu maili silmek istediğinize emin misiniz?');">
                                                    <input type="hidden" name="email_id" value="<?php echo $email['id']; ?>">
                                                    <input type="hidden" name="delete_email" value="1">
                                                    <button type="submit" class="btn btn-sm btn-icon btn-light btn-active-light-danger" data-bs-toggle="tooltip" title="Sil">
                                                        <i class="ki-duotone ki-trash fs-2">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                            <span class="path3"></span>
                                                            <span class="path4"></span>
                                                            <span class="path5"></span>
                                                        </i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                        <!--end::Actions-->
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
                <!--end::Table-->
            </div>
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content-->
</div>
<!--end::Content-->

<script>
    // Mail arama fonksiyonu
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('mailbox-search');
        let searchTimeout;

        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.trim();

                // Timeout ile arama yapma sıklığını sınırla
                clearTimeout(searchTimeout);

                // Arama metni 2 karakterden kısaysa ve boş değilse, bekle
                if (searchText.length > 0 && searchText.length < 2) {
                    return;
                }

                // Arama metni boşsa, sayfayı yenile
                if (searchText === '') {
                    window.location.reload();
                    return;
                }

                // 500ms bekle ve sonra arama yap
                searchTimeout = setTimeout(function() {
                    // Arama işlemi için yükleniyor göstergesi
                    const tableBody = document.querySelector('tbody');
                    if (tableBody) {
                        tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div><div class="mt-3">Mailler aranıyor...</div></td></tr>';
                    }

                    // AJAX ile arama yap
                    $.ajax({
                        url: WEB_DIR + '/index.php?do=mailbox/mailbox_admin&ajax=1',
                        type: 'POST',
                        data: {
                            action: 'search_emails',
                            search_text: searchText,
                            folder: 'INBOX',
                            ajax: 1
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success && response.result) {
                                // Sonuçları göster
                                displaySearchResults(response.result);
                            } else {
                                // Hata durumunda boş tablo göster
                                const tableBody = document.querySelector('tbody');
                                if (tableBody) {
                                    tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-5">Arama sonuçları bulunamadı.</td></tr>';
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Arama hatası:', error);
                            // Hata durumunda boş tablo göster
                            const tableBody = document.querySelector('tbody');
                            if (tableBody) {
                                tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-5">Arama sırasında bir hata oluştu.</td></tr>';
                            }
                        }
                    });
                }, 500);
            });
        }

        // Arama sonuçlarını gösterme fonksiyonu
        function displaySearchResults(result) {
            console.log('Arama sonuçları gösteriliyor:', result);

            // Arama sonuçları modunda olduğumuzu işaretle
            window.isSearchResults = true;
            const tableBody = document.querySelector('tbody');
            if (!tableBody) return;

            // Sonuç yoksa boş mesaj göster
            if (!result.emails || result.emails.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-5">Arama sonuçları bulunamadı.</td></tr>';
                return;
            }

            // Sonuçları tabloya ekle
            let html = '';
            result.emails.forEach(email => {
                html += `
                <tr class="${email.seen ? '' : 'fw-bold'}">
                    <!--begin::Checkbox-->
                    <td class="ps-9">
                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                            <input class="form-check-input email-checkbox" type="checkbox" data-email-id="${email.id}">
                        </div>
                    </td>
                    <!--end::Checkbox-->

                    <!--begin::Author-->
                    <td class="w-150px">
                        <a href="${WEB_DIR}/index.php?do=mailbox/view&id=${email.id}" class="d-flex align-items-center text-gray-900">
                            <div class="symbol symbol-35px me-3">
                                <span class="symbol-label bg-light-primary text-primary fw-bold">${email.from.charAt(0).toUpperCase()}</span>
                            </div>
                            <span class="fw-semibold">${email.from}</span>
                        </a>
                    </td>
                    <!--end::Author-->

                    <!--begin::Title-->
                    <td>
                        <div class="text-gray-900 mb-1">
                            <a href="${WEB_DIR}/index.php?do=mailbox/view&id=${email.id}" class="text-gray-900">
                                <span class="fw-bold">${email.subject}</span>
                            </a>
                        </div>
                    </td>
                    <!--end::Title-->

                    <!--begin::Date-->
                    <td class="w-100px text-end pe-9">
                        <span class="fw-semibold">${email.date}</span>
                    </td>
                    <!--end::Date-->

                    <!--begin::Actions-->
                    <td class="w-100px text-end pe-9">
                        <div class="d-flex justify-content-end">
                            <a href="${WEB_DIR}/index.php?do=mailbox/view&id=${email.id}" class="btn btn-sm btn-icon btn-light btn-active-light-primary me-2" data-bs-toggle="tooltip" title="Oku">
                                <i class="ki-duotone ki-eye fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                    <span class="path4"></span>
                                </i>
                            </a>

                            <form method="post" class="d-inline delete-form" onsubmit="return confirm('Bu maili silmek istediğinize emin misiniz?');">
                                <input type="hidden" name="email_id" value="${email.id}">
                                <input type="hidden" name="delete_email" value="1">
                                <button type="submit" class="btn btn-sm btn-icon btn-light btn-active-light-danger" data-bs-toggle="tooltip" title="Sil">
                                    <i class="ki-duotone ki-trash fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                        <span class="path4"></span>
                                        <span class="path5"></span>
                                    </i>
                                </button>
                            </form>
                        </div>
                    </td>
                    <!--end::Actions-->
                </tr>
                `;
            });

            tableBody.innerHTML = html;

            // Sayfalama bilgisini güncelle
            const paginationDiv = document.querySelector('.d-flex.pages_div');
            if (paginationDiv) {
                paginationDiv.innerHTML = `<span class="fs-7 fw-semibold text-muted me-2">Arama Sonuçları: ${result.total} mail bulundu</span>`;
            }

            // Bulk Actions butonlarını göster
            const bulkActionsDiv = document.querySelector('.d-flex.align-items-center:has(#delete-selected)');
            if (bulkActionsDiv) {
                bulkActionsDiv.style.display = 'flex';
            }

            // Checkbox işlevlerini yeniden bağla
            // Önce tüm event listener'ları temizle
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                const newCheckbox = checkbox.cloneNode(true);
                checkbox.parentNode.replaceChild(newCheckbox, checkbox);
            });

            // Yeni event listener'ları ekle
            setTimeout(function() {
                // Global değişkenleri temizle
                window.checkboxesInitialized = false;

                // Checkbox işlevlerini yeniden bağla
                initCheckboxes();
                console.log('Checkbox işlevleri yeniden bağlandı');
            }, 100);
        }

        // Checkbox işlevlerini bağlama fonksiyonu
        function initCheckboxes() {
            console.log('Checkbox işlevleri bağlanıyor...');

            // Eğer zaten bağlanmışsa ve arama sonuçları değilse, tekrar bağlama
            if (window.checkboxesInitialized && !window.isSearchResults) {
                console.log('Checkbox işlevleri zaten bağlı, tekrar bağlanmıyor.');
                return;
            }

            // Bağlandığını işaretle
            window.checkboxesInitialized = true;

            // Önce tüm event listener'ları temizle
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                const newCheckbox = checkbox.cloneNode(true);
                checkbox.parentNode.replaceChild(newCheckbox, checkbox);
            });

            // Elementleri seç
            const selectAllCheckbox = document.getElementById('select-all-emails');
            const deleteSelectedButton = document.getElementById('delete-selected');
            const selectedCountSpan = document.getElementById('selected-count');

            // Tümünü seç checkbox'u
            if (selectAllCheckbox) {
                // Önceki event listener'ları temizle
                selectAllCheckbox.onclick = null;
                selectAllCheckbox.onchange = null;

                // Yeni event listener ekle
                selectAllCheckbox.addEventListener('change', function() {
                    console.log('Tümünü seç değişti:', this.checked);
                    const isChecked = this.checked;
                    let selectedCount = 0;

                    document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                        checkbox.checked = isChecked;
                        if (isChecked) selectedCount++;
                    });

                    // Seçilenleri sil butonunu güncelle
                    if (deleteSelectedButton) {
                        deleteSelectedButton.disabled = selectedCount === 0;
                    }
                    if (selectedCountSpan) {
                        selectedCountSpan.textContent = selectedCount;
                    }
                });
            }

            // Tek tek checkbox'lar için event listener'ları ekle
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    console.log('Checkbox değişti:', this.checked);
                    let selectedCount = 0;
                    let allSelected = true;
                    const currentCheckboxes = document.querySelectorAll('.email-checkbox');

                    currentCheckboxes.forEach(cb => {
                        if (cb.checked) selectedCount++;
                        else allSelected = false;
                    });

                    console.log('Seçili mail sayısı:' + selectedCount);

                    // Tümünü seç checkbox'unu güncelle
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = allSelected && currentCheckboxes.length > 0;
                    }

                    // Seçilenleri sil butonunu güncelle
                    if (deleteSelectedButton) {
                        deleteSelectedButton.disabled = selectedCount === 0;
                    }
                    if (selectedCountSpan) {
                        selectedCountSpan.textContent = selectedCount;
                    }
                });
            });

            // Seçilenleri sil butonu
            if (deleteSelectedButton) {
                // Tüm event listener'ları temizle

                // Tek bir event listener ekle
                const clickHandler = function(e) {
                    console.log('Seçilenleri sil butonuna tıklandı');
                    e.preventDefault();
                    e.stopPropagation(); // Event'in yayılmasını durdur

                    const selectedEmails = [];

                    document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                        if (checkbox.checked) {
                            selectedEmails.push(checkbox.getAttribute('data-email-id'));
                        }
                    });

                    console.log('Seçili mailler:', selectedEmails);

                    if (selectedEmails.length > 0) {
                        if (confirm(selectedEmails.length + ' adet maili silmek istediğinize emin misiniz?')) {
                            // Form oluştur ve gönder
                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = window.location.href;
                            form.style.display = 'none';

                            const emailIdsInput = document.createElement('input');
                            emailIdsInput.type = 'hidden';
                            emailIdsInput.name = 'email_ids';
                            emailIdsInput.value = JSON.stringify(selectedEmails);

                            const actionInput = document.createElement('input');
                            actionInput.type = 'hidden';
                            actionInput.name = 'delete_selected_emails';
                            actionInput.value = '1';

                            form.appendChild(emailIdsInput);
                            form.appendChild(actionInput);
                            document.body.appendChild(form);

                            console.log('Form gönderiliyor...');
                            form.submit();
                        }
                    }
                };
                // Event listener'ları ekle
                if (!deleteSelectedButton.hasEventListener) {
                    deleteSelectedButton.addEventListener('click', clickHandler);
                    deleteSelectedButton.hasEventListener = true;
                }

            }

        }

        // Sayfa yüklenirken checkbox işlevlerini bağla
        initCheckboxes();
    });
</script>