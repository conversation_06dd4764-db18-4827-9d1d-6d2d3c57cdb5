<?php
/**
 * tmp_move klasöründeki dosyaları indirmek için handler
 */

// Güvenlik kontrolü
if (!defined('ADMIN_PANEL')) {
    die('Direct access not allowed');
}

// Parametreleri al
$filename = isset($_GET['file']) ? $_GET['file'] : '';

if (empty($filename)) {
    die('Dosya adı belirtilmedi');
}

// Güvenlik: sadece alfanumerik karak<PERSON>ler, nokta, tire ve alt çizgi
if (!preg_match('/^[a-zA-Z0-9._-]+$/', $filename)) {
    die('Geçersiz dosya adı');
}

// Dosya yolunu oluştur
$tmp_move_dir = ADDONS_DIR . '/mailbox/tmp_move';
$file_path = $tmp_move_dir . '/' . $filename;

// Dosya var mı kontrol et
if (!file_exists($file_path)) {
    die('Dosya bulunamadı');
}

// Dosya tmp_move klasörü içinde mi kontrol et (directory traversal saldırısını önle)
$real_file_path = realpath($file_path);
$real_tmp_dir = realpath($tmp_move_dir);

if (!$real_file_path || !$real_tmp_dir || strpos($real_file_path, $real_tmp_dir) !== 0) {
    die('Güvenlik hatası: Geçersiz dosya yolu');
}

// MIME tipini belirle
$mime_type = 'application/octet-stream';
$extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

$mime_map = [
    'pdf' => 'application/pdf',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls' => 'application/vnd.ms-excel',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'txt' => 'text/plain',
    'zip' => 'application/zip',
    'rar' => 'application/x-rar-compressed'
];

if (isset($mime_map[$extension])) {
    $mime_type = $mime_map[$extension];
}

// Dosya boyutunu al
$file_size = filesize($file_path);

// Tüm çıktı tamponlarını temizle
while (ob_get_level()) {
    ob_end_clean();
}

// Header'ları ayarla
header('Content-Description: File Transfer');
header('Content-Type: ' . $mime_type);
header('Content-Transfer-Encoding: binary');
header('Expires: 0');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');
header('Content-Length: ' . $file_size);

// Dosya adını encode et
$encoded_filename = rawurlencode($filename);
header("Content-Disposition: attachment; filename*=UTF-8''" . $encoded_filename);

// Dosyayı chunk'lar halinde gönder
$handle = fopen($file_path, 'rb');
if ($handle) {
    while (!feof($handle)) {
        echo fread($handle, 8192);
        flush();
    }
    fclose($handle);
} else {
    die('Dosya okunamadı');
}

exit;
?>
