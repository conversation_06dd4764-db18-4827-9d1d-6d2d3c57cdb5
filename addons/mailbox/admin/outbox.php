<?php
try {
    // Mail silme işlemi
    if ($_POST && isset($_POST['delete_email'])) {
        try {
            // Aktif hesap ID'sini al
            $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

            // Giden kutusu için SENT klasörünü belirt
            $_POST['folder'] = 'SENT';

            // Önce IMAP bağlantısı aç
            $mailbox = mailbox_connect($account_id);

            // IMAP bağlantı dizesini oluştur
            global $mailbox_config;
            $account = null;
            if ($account_id === 'default' || $account_id === null) {
                $account = $mailbox_config['default'];
            } else {
                // Belirtilen hesabı bul
                foreach ($mailbox_config['accounts'] as $acc) {
                    if ($acc['id'] == $account_id) {
                        $account = $acc;
                        break;
                    }
                }

                // Hesap bulunamadı<PERSON>a varsay<PERSON> kullan
                if ($account === null) {
                    $account = $mailbox_config['default'];
                }
            }

            // IMAP bağlantı dizesini oluştur
            $imap_string = parse_url($account['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';

            // Mevcut bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                try {
                    @imap_close($mailbox);
                } catch (\Throwable $e) {
                    error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
                }
            }

            // Sent klasörünü bul
            $folders = imap_list(imap_open($imap_string, $account['username'], $account['password']), $imap_string, '*');
            $sent_folder = null;
            foreach ($folders as $folder) {
                if (
                    stripos($folder, 'Sent') !== false ||
                    stripos($folder, 'Gönderilmiş') !== false ||
                    stripos($folder, 'Giden') !== false
                ) {
                    $sent_folder = $folder;
                    break;
                }
            }

            if (!$sent_folder) {
                throw new Exception('Giden kutusu klasörü bulunamadı.');
            }

            // Sent klasörüne bağlan
            $mailbox = imap_open($sent_folder, $account['username'], $account['password']);

            $email_id = (int) $_POST['email_id'];
            if (delete_email($mailbox, $email_id, false, 'SENT')) {
                fn_set_notice('Mail başarıyla silindi.', 'S');
            }

            // Bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                try {
                    @imap_close($mailbox);
                } catch (\Throwable $e) {
                    error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
                }
            }
        } catch (Exception $e) {
            fn_set_notice($e->getMessage(), 'E');
        }
        fn_redirect(fn_menu_link('mailbox/outbox', 1));
    }

    // Tüm mailleri silme işlemi
    if ($_POST && isset($_POST['delete_all_emails'])) {
        try {
            // Aktif hesap ID'sini al
            $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

            // Giden kutusu için SENT klasörünü belirt
            $_POST['folder'] = 'SENT';

            // Önce IMAP bağlantısı aç
            $mailbox = mailbox_connect($account_id);

            // IMAP bağlantı dizesini oluştur
            global $mailbox_config;
            $account = null;
            if ($account_id === 'default' || $account_id === null) {
                $account = $mailbox_config['default'];
            } else {
                // Belirtilen hesabı bul
                foreach ($mailbox_config['accounts'] as $acc) {
                    if ($acc['id'] == $account_id) {
                        $account = $acc;
                        break;
                    }
                }

                // Hesap bulunamadıysa varsayılanı kullan
                if ($account === null) {
                    $account = $mailbox_config['default'];
                }
            }

            // IMAP bağlantı dizesini oluştur
            $imap_string = parse_url($account['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';

            // Mevcut bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                try {
                    @imap_close($mailbox);
                } catch (\Throwable $e) {
                    error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
                }
            }

            // Sent klasörünü bul
            $folders = imap_list(imap_open($imap_string, $account['username'], $account['password']), $imap_string, '*');
            $sent_folder = null;
            foreach ($folders as $folder) {
                if (
                    stripos($folder, 'Sent') !== false ||
                    stripos($folder, 'Gönderilmiş') !== false ||
                    stripos($folder, 'Giden') !== false
                ) {
                    $sent_folder = $folder;
                    break;
                }
            }

            if (!$sent_folder) {
                throw new Exception('Giden kutusu klasörü bulunamadı.');
            }

            // Sent klasörüne bağlan
            $mailbox = imap_open($sent_folder, $account['username'], $account['password']);

            if (delete_all_emails($mailbox, 'SENT', $account_id)) {
                fn_set_notice('Tüm gönderilmiş mailler başarıyla silindi.', 'S');
            }

            // Bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                try {
                    @imap_close($mailbox);
                } catch (\Throwable $e) {
                    error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
                }
            }
        } catch (Exception $e) {
            fn_set_notice($e->getMessage(), 'E');
        }
        fn_redirect(fn_menu_link('mailbox/outbox', 1));
    }

    // Aktif hesap ID'sini al
    $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

    // Mail listesini al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
    $result = get_sent_emails(null, $page, 20, $account_id);
} catch (Exception $e) {
    fn_set_notice($e->getMessage(), 'E');
}
?>


<!--begin::Content-->
<div class="d-flex flex-column flex-lg-row">
    <?php
    // Aktif sayfa bilgisini ayarla
    $current_page = 'outbox';

    // Aside menüyü dahil et
    include(dirname(__FILE__) . '/includes/mailbox_aside.php');
    ?>

    <!--begin::Content-->
    <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
        <!--begin::Card-->
        <div class="card">
            <div class="card-header align-items-center ps-5 py-5 gap-5">
                <!--begin::Actions-->
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative">
                        <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        <input type="text" id="mailbox-search" class="form-control form-control-sm form-control-solid mw-100 min-w-125px min-w-lg-300px ps-11" placeholder="Mail Ara...">
                    </div>
                    <!--end::Search-->

                    <?php if (!empty($result['emails'])): ?>
                        <!--begin::Delete All-->
                        <form method="post" class="d-inline" id="delete-all-form" onsubmit="return confirm('Tüm gönderilmiş mailleri silmek istediğinize emin misiniz? Bu işlem geri alınamaz!')">
                            <input type="hidden" name="delete_all_emails" value="1">
                            <button type="submit" class="btn btn-sm btn-secondary me-2">
                                <i class="ki-duotone ki-trash fs-2 me-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                    <span class="path4"></span>
                                    <span class="path5"></span>
                                </i>
                                Tüm Mailleri Sil
                            </button>
                        </form>
                        <!--end::Delete All-->
                    <?php endif; ?>
                </div>
                <!--end::Actions-->

                <!--begin::Pagination-->
                <?php if ($result['pages'] > 1): ?>
                    <div class="d-flex align-items-center">
                        <span class="fs-7 fw-semibold text-muted me-2"><?php echo ($page - 1) * 20 + 1; ?>-<?php echo min($page * 20, $result['total']); ?> / <?php echo $result['total']; ?></span>
                        <a href="<?php echo fn_menu_link('mailbox/outbox&page=' . max(1, $page - 1)); ?>" class="btn btn-sm btn-icon btn-light me-2 <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <i class="ki-duotone ki-left fs-2 text-gray-500">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </a>
                        <a href="<?php echo fn_menu_link('mailbox/outbox&page=' . min($result['pages'], $page + 1)); ?>" class="btn btn-sm btn-icon btn-light <?php echo $page >= $result['pages'] ? 'disabled' : ''; ?>">
                            <i class="ki-duotone ki-right fs-2 text-gray-500">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </a>
                    </div>
                <?php endif; ?>
                <!--end::Pagination-->
            </div>

            <div class="card-body p-0">
                <!--begin::Table-->
                <?php if (empty($result['emails'])): ?>
                    <div class="text-center py-10">
                        <i class="ki-duotone ki-message-text-2 fs-3x text-gray-400 mb-5">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        <div class="text-gray-600 fw-semibold fs-4 mb-3">Giden kutunuzda hiç mail bulunmuyor.</div>
                        <a href="<?php echo fn_menu_link('mailbox/compose'); ?>" class="btn btn-sm btn-primary">Yeni Mail Oluştur</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table align-middle table-hover table-row-dashed table-sm">
                            <thead>
                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-35px ps-9">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" id="select-all-emails">
                                        </div>
                                    </th>
                                    <th>Alıcı</th>
                                    <th>Konu</th>
                                    <th class="text-end">Tarih</th>
                                    <th class="text-end pe-2">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($result['emails'] as $email): ?>
                                    <tr>
                                        <!--begin::Checkbox-->
                                        <td class="ps-9">
                                            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                <input class="form-check-input email-checkbox" type="checkbox" data-email-id="<?php echo $email['id']; ?>">
                                            </div>
                                        </td>
                                        <!--end::Checkbox-->

                                        <!--begin::Recipient-->
                                        <td class="w-150px">
                                            <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=sent'); ?>" class="d-flex align-items-center text-dark">
                                                <div class="symbol symbol-35px me-3">
                                                    <span class="symbol-label bg-light text-gray-600 fw-bold"><?php echo strtoupper(substr($email['to'], 0, 1)); ?></span>
                                                </div>
                                                <span class="fw-semibold text-gray-700"><?php echo $email['to']; ?></span>
                                            </a>
                                        </td>
                                        <!--end::Recipient-->

                                        <!--begin::Title-->
                                        <td valign="middle">

                                            <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=sent'); ?>" class="text-dark">
                                                <span class="fw-bold"><?php echo $email['subject']; ?></span>
                                            </a>

                                            <?php
                                            // Mail etiketlerini getir
                                            $email_labels = get_email_labels($email['id'] ?? 0, $account_id, 'sent');
                                            if (is_array($email_labels) && !empty($email_labels)):
                                            ?>
                                                <div class="d-flex flex-wrap gap-1 mt-1">
                                                    <?php foreach ($email_labels as $label): ?>
                                                        <span class="badge" style="background-color: <?php echo $label->color; ?>; color: <?php echo get_contrast_color($label->color); ?>">
                                                            <?php echo $label->name; ?>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <!--end::Title-->

                                        <!--begin::Date-->
                                        <td class="text-end ">
                                            <span class="fw-semibold"><?php echo $email['date']; ?></span>
                                        </td>
                                        <!--end::Date-->

                                        <!--begin::Actions-->
                                        <td class="w-100px text-end pe-2">
                                            <div class="d-flex justify-content-end">
                                                <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=sent'); ?>" class="btn btn-sm btn-icon btn-light btn-active-light-primary me-2" data-bs-toggle="tooltip" title="Görüntüle">
                                                    <i class="ki-duotone ki-eye fs-2">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                        <span class="path3"></span>
                                                        <span class="path4"></span>
                                                    </i>
                                                </a>

                                                <form method="post" class="d-inline delete-form" onsubmit="return confirm('Bu maili silmek istediğinize emin misiniz?');">
                                                    <input type="hidden" name="email_id" value="<?php echo $email['id']; ?>">
                                                    <input type="hidden" name="delete_email" value="1">
                                                    <button type="submit" class="btn btn-sm btn-icon btn-light btn-active-danger" data-bs-toggle="tooltip" title="Sil">
                                                        <i class="ki-duotone ki-trash fs-2">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                            <span class="path3"></span>
                                                            <span class="path4"></span>
                                                            <span class="path5"></span>
                                                        </i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                        <!--end::Actions-->
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
                <!--end::Table-->
            </div>
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content-->
</div>
<!--end::Content-->

<script>
    // Mail arama fonksiyonu
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('mailbox-search');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const subject = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                    const recipient = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

                    if (subject.includes(searchText) || recipient.includes(searchText)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // Checkbox işlemleri
        const selectAllCheckbox = document.getElementById('select-all-emails');
        const emailCheckboxes = document.querySelectorAll('.email-checkbox');

        // Tümünü seç checkbox'u
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;

                emailCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });
        }

        // Tek tek checkbox'lar
        emailCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                let allSelected = true;

                emailCheckboxes.forEach(cb => {
                    if (!cb.checked) allSelected = false;
                });

                // Tümünü seç checkbox'unu güncelle
                if (selectAllCheckbox) selectAllCheckbox.checked = allSelected && emailCheckboxes.length > 0;
            });
        });
    });
</script>