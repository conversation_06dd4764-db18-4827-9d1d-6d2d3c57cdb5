<?php
if ($_POST['email_id'])
    $_SESSION['calisma']['email_id'] = $_POST['email_id'];
if ($_POST['folder'])
    $_SESSION['calisma']['folder'] = $_POST['folder'];
if ($_POST['selected_text'])
    $_SESSION['calisma']['selected_text'] = $_POST['selected_text'];
if ($_POST['attachments'])
    $_SESSION['calisma']['attachments'] = $_POST['attachments'];
pe($_SESSION['calisma']);
// Gerekli kontroller
if (!isset($_POST['selected_text']) || empty($_POST['selected_text'])) {
    fn_set_notice('Seçili metin bulunamadı.', 'E');
    fn_redirect(fn_menu_link('mailbox/inbox'));
}

if (!isset($_POST['target_section']) || empty($_POST['target_section'])) {
    fn_set_notice('Aktarılacak bölüm seçilmedi.', 'E');
    fn_redirect(fn_menu_link('mailbox/inbox'));
}

// Gelen verileri al
$selected_text = $_POST['selected_text'];
$target_section = $_POST['target_section'];
$email_id = isset($_POST['email_id']) ? (int)$_POST['email_id'] : 0;
$folder = isset($_POST['folder']) ? $_POST['folder'] : 'INBOX';
$attachments = isset($_POST['attachments']) ? $_POST['attachments'] : [];

// Ekleri işle
$processed_attachments = [];
if (!empty($attachments) && $email_id > 0) {
    try {
        // IMAP bağlantısı aç
        $mailbox = mailbox_connect();

        // Klasör parametresine göre uygun klasöre geç
        if ($folder != 'INBOX') {
            // Mevcut bağlantıyı kapat
            @imap_close($mailbox);

            $host = parse_url($mailbox_config['imap_host'], PHP_URL_HOST);
            $target_folder = null;

            if ($folder == 'sent') {
                // Sent klasörüne yeni bağlantı aç
                $possible_folders = array('Sent', 'Sent Items', 'Gönderilmiş Öğeler');

                foreach ($possible_folders as $folder_name) {
                    $mailbox = @imap_open(
                        $host . ':993/imap/ssl/novalidate-cert}' . $folder_name,
                        $mailbox_config['username'],
                        $mailbox_config['password']
                    );
                    if ($mailbox) {
                        $target_folder = $folder_name;
                        break;
                    }
                }

                if (!$mailbox) {
                    throw new Exception('Giden kutusu klasörüne erişilemiyor: ' . imap_last_error());
                }
            } elseif ($folder == 'trash') {
                // Çöp kutusu klasörüne yeni bağlantı aç
                $possible_folders = array('Trash', 'Deleted Items', 'Çöp Kutusu', 'Deleted');

                foreach ($possible_folders as $folder_name) {
                    $mailbox = @imap_open(
                        $host . ':993/imap/ssl/novalidate-cert}' . $folder_name,
                        $mailbox_config['username'],
                        $mailbox_config['password']
                    );
                    if ($mailbox) {
                        $target_folder = $folder_name;
                        break;
                    }
                }

                if (!$mailbox) {
                    throw new Exception('Çöp kutusu klasörüne erişilemiyor: ' . imap_last_error());
                }
            }
        }

        // Mail içeriğini al
        $email = get_email_content($mailbox, $email_id);

        // tmp_move klasörünün varlığını kontrol et, yoksa oluştur
        $tmp_move_dir = ADDONS_DIR . '/mailbox/tmp_move';
        if (!file_exists($tmp_move_dir)) {
            mkdir($tmp_move_dir, 0755, true);
        }

        // Seçilen ekleri işle
        foreach ($attachments as $part_num) {
            foreach ($email['attachments'] as $attachment) {
                if ($attachment['part_num'] == $part_num) {
                    // Ek dosyasını indir
                    $attachment_data = imap_fetchbody($mailbox, $email_id, $attachment['part_num']);

                    // Encoding'e göre decode et
                    if ($attachment['encoding'] == 3) { // BASE64
                        $attachment_data = base64_decode($attachment_data);
                    } elseif ($attachment['encoding'] == 4) { // QUOTED-PRINTABLE
                        $attachment_data = quoted_printable_decode($attachment_data);
                    }

                    // Dosyayı tmp_move klasörüne kaydet
                    $safe_filename = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $attachment['filename']);
                    $file_path = $tmp_move_dir . '/' . $safe_filename;

                    // Aynı isimde dosya varsa, isim sonuna sayı ekle
                    $counter = 1;
                    $original_name = pathinfo($file_path, PATHINFO_FILENAME);
                    $extension = pathinfo($file_path, PATHINFO_EXTENSION);

                    while (file_exists($file_path)) {
                        $file_path = $tmp_move_dir . '/' . $original_name . '_' . $counter . '.' . $extension;
                        $counter++;
                    }

                    // Dosyayı kaydet
                    if (file_put_contents($file_path, $attachment_data)) {
                        $processed_attachments[] = [
                            'filename' => $attachment['filename'],
                            'path' => $file_path,
                            'size' => isset($attachment['size']) ? $attachment['size'] : filesize($file_path)
                        ];
                    }

                    break;
                }
            }
        }

        // IMAP bağlantısını kapat
        imap_close($mailbox);
    } catch (Exception $e) {
        fn_set_notice('Ekler işlenirken hata oluştu: ' . $e->getMessage(), 'E');
    }
}

// Sonuçları göster
?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Seçili İçerik Aktarıldı</h3>
    </div>
    <div class="card-body">
        <div class="mb-4">
            <h4 class="fw-bold">Aktarılan Metin</h4>
            <div class="border rounded p-3 bg-light">
                <?php echo nl2br(htmlspecialchars($selected_text)); ?>
            </div>
        </div>

        <div class="mb-4">
            <h4 class="fw-bold">Aktarılan Bölüm</h4>
            <div class="border rounded p-3">
                <?php
                switch ($target_section) {
                    case 'musteri_notlari':
                        echo 'Müşteri Notları';
                        break;
                    case 'proje_dokumanlari':
                        echo 'Proje Dökümanları';
                        break;
                    case 'gorev_listesi':
                        echo 'Görev Listesi';
                        break;
                    case 'bilgi_bankasi':
                        echo 'Bilgi Bankası';
                        break;
                    default:
                        echo $target_section;
                }
                ?>
            </div>
        </div>

        <?php if (!empty($processed_attachments)): ?>
            <div class="mb-4">
                <h4 class="fw-bold">Aktarılan Ekler</h4>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Dosya Adı</th>
                                <th>Boyut</th>
                                <th>Geçici Konum</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($processed_attachments as $attachment): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($attachment['filename']); ?></td>
                                    <td><?php echo format_file_size($attachment['size']); ?></td>
                                    <td><code><?php echo htmlspecialchars($attachment['path']); ?></code></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <div class="alert alert-info">
            <i class="ki-duotone ki-information-5 fs-2 me-2">
                <span class="path1"></span>
                <span class="path2"></span>
                <span class="path3"></span>
            </i>
            Bu sayfada gösterilen bilgiler, seçtiğiniz içeriğin başarıyla işlendiğini göstermektedir. Gerçek bir uygulamada, bu veriler ilgili modüle aktarılacaktır.
        </div>
    </div>
    <div class="card-footer">
        <a href="<?php fn_menu_link('mailbox/inbox'); ?>" class="btn btn-primary">Gelen Kutusuna Dön</a>
    </div>
</div>