<?php
// pe(fn_menu_items());
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($mode == 'save_draft') {
        try {
            $result = save_draft($_POST);
            if ($result !== false) {
                if (empty($_POST['to']) && empty($_POST['subject']) && empty($_POST['body'])) {
                    send_json(array('success' => true, 'message' => 'Değişiklik yapılmadı'));
                } else {
                    send_json(array('success' => true));
                }
            } else {
                send_json(array('success' => false, 'error' => 'Taslak kaydedilemedi'));
            }
        } catch (Exception $e) {
            send_json(array('success' => false, 'error' => $e->getMessage()));
        }
    } elseif ($mode == 'delete_attachment') {
        try {
            if (empty($_POST['draft_id']) || !isset($_POST['attachment_index'])) {
                throw new Exception('Geçersiz istek');
            }

            $result = delete_draft_attachment((int) $_POST['draft_id'], (int) $_POST['attachment_index']);
            if ($result) {
                send_json(array('success' => true));
            } else {
                send_json(array('success' => false, 'error' => 'Ek silinemedi'));
            }
        } catch (Exception $e) {
            send_json(array('success' => false, 'error' => $e->getMessage()));
        }
    }
}

// AJAX isteği kontrolü
$is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == 1 || isset($_POST['ajax']) && $_POST['ajax'] == 1;

// AJAX isteği değilse hesap menüsünü göster
if (!$is_ajax) {
    include 'toolbar.php';
    // echo '<div class="card mb-3">';
    // echo '<div class="card-body py-3">';
    // echo '<div class="d-flex align-items-center justify-content-between">';
    // echo '<div class="d-flex align-items-center">';
    // echo '<i class="ki-duotone ki-message-text-2 fs-1 me-3"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i>';
    // echo '<div>';
    // echo '<h3 class="card-title m-0">Mail Hesabı Seçin</h3>';
    // echo '<p class="text-muted mb-0">Farklı mail hesapları arasında geçiş yapabilirsiniz</p>';
    // echo '</div>';
    // echo '</div>';
    // echo $account_menu;
    // echo '</div>';
    // echo '</div>';
    // echo '</div>';
}

// Yanıtla sayfası için özel işlem
include(ADDONS_DIR . '/' . $target . '/admin/' . $mode . '.php');
