<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// $m = get_results('murettebatlar', "proje_id=706");
// foreach ($m as $key => $value) {
//     crew_delete_document($value->id);
// }
// Crew data export işlemi
if (isset($_POST['export_crew_data']) && $_POST['export_crew_data'] == '1') {

    $project_id = 706;
    $export_data = [
        'on_signers' => $_POST['on_signers'] ?? [],
        'off_signers' => $_POST['off_signers'] ?? [],
        'export_timestamp' => date('Y-m-d H:i:s'),
        'format_type' => detect_text_format($_SESSION['calisma']['selected_text'] ?? ''),
        'total_on_signers' => count($_POST['on_signers'] ?? []),
        'total_off_signers' => count($_POST['off_signers'] ?? [])
    ];


    $on_signers = $_POST['on_signers'];
    $off_signers = $_POST['off_signers'];

    // Var olan murettebatlari temizle


    tocrew_from_email($on_signers, $status = 'EBMERKATION', $project_id);


    // foreach ($off_signers as $key => $off_signer) {
    //     $off_signers[$key]['project_id'] = $project_id;
    // }


    fn_set_notice('Crew verileri başarıyla aktarıldı!');
    fn_redirect(fn_menu_link('mailbox/tocrew.php&id=' . $id, 1));
    // Export data
}

function crew_delete_document($murettebat_id)
{
    global $murettebat_images, $db;
    $murettebat_dosyalar = get_results('murettebat_dokumanlari', "murettebat_id=$murettebat_id");
    if (!$murettebat_dosyalar) return;
    foreach ($murettebat_dosyalar as $key => $murettebat_dosya) {
        if ($murettebat_dosya->dosyaadi)
            fn_delete_file($murettebat_images . $murettebat_dosya->dosyaadi);
        $db->query("DELETE FROM murettebat_dokumanlari WHERE id=$murettebat_dosya->id");
    }
}

function tocrew_from_email($on_signers, $status = 'EBMERKATION', $project_id)
{
    global $murettebat_ulkesi_vizeli, $murettebat_images;

    foreach ($on_signers as $key => $on_signer) {

        // Tüm değerleri temizle (trim ve &nbsp; karakterini kaldır)
        array_walk_recursive($on_signer, function (&$value) {
            if (is_string($value)) {
                $value = trim(str_replace("\xC2\xA0", '', $value)); // &nbsp; karakterini temizle
            }
        });


        $_data = array();
        $_data['proje_id'] = $project_id;
        $_data['name'] = $on_signer['firstname'];
        $_data['lastname'] = $on_signer['lastname'];
        // $_data['firstname'] = $on_signer['firstname'];
        $_data['dateofbirth'] = parse_date_to_unix($on_signer['date_of_birth']);
        $_data['placeofbirth'] = $on_signer['place_of_birth'];

        $_data['passport_no'] = $on_signer['passport']['number'];
        $_data['pasaport_issued'] = parse_date_to_unix($on_signer['passport']['issued']);
        $_data['pasaport_expiry'] = parse_date_to_unix($on_signer['passport']['expiry']);

        $_data['seamanbook_no'] = $on_signer['seaman_book']['number'];
        $_data['seamanbook_issued'] = parse_date_to_unix($on_signer['seaman_book']['issued']);
        $_data['seamanbook_expiry'] = parse_date_to_unix($on_signer['seaman_book']['expiry']);

        // Uyruk ismini $murettebat_ulkesi dizisinde fuzzy olarak ara ve eşleşen değeri bul
        $_data['nationolaity'] = $on_signer['nationality'];
        if (!empty($on_signer['nationality']) && !empty($murettebat_ulkesi) && is_array($murettebat_ulkesi)) {
            $input_nationality = mb_strtolower(trim($on_signer['nationality']));
            $best_match = '';
            $best_score = 0;
            foreach ($murettebat_ulkesi as $ulkesi) {
                $ulkesi_lower = mb_strtolower(trim($ulkesi));
                similar_text($input_nationality, $ulkesi_lower, $percent);
                if ($percent > $best_score) {
                    $best_score = $percent;
                    $best_match = $ulkesi;
                }
                // Tam eşleşme varsa hemen döndür
                if ($percent == 100) {
                    $best_match = $ulkesi;
                    break;
                }
            }
            // Eşik değeri %70 ve üzeri ise eşleşmeyi kullan
            if ($best_score >= 70) {
                $_data['nationolaity'] = $best_match;
            }
        }
        $_data['status'] = $status;
        $_data['rank'] = $on_signer['rank'];
        $_data['vize_durumu'] = (in_array($on_signer['nationality'], $murettebat_ulkesi_vizeli)) ? 1 : 0;

        // pe($on_signer);
        // ped($_data);
        // Bu murettebat var mi?
        $existing_murettebat = get_row('murettebatlar', "name='{$_data['name']}' AND lastname='{$_data['lastname']}' AND proje_id=$project_id");
        if ($existing_murettebat) {
            // Update
            fn_update_array('murettebatlar', $_data, "id={$existing_murettebat->id}");
            $murettebat_id = $existing_murettebat->id;
        } else {
            // Insert
            $insert_id = fn_insert_array('murettebatlar', $_data);
            $murettebat_id = $insert_id;
        }
        // // Ekler
        if ($on_signer['attachments'] && is_array($on_signer['attachments'])) {
            foreach ($on_signer['attachments'] as $attachment) {
                $file = array();
                $file_path = $attachment['path'];
                $file_name = $attachment['filename'];
                $label_id = $attachment['type'] ?? '';
                if ($label_id == 'Passport') {
                    $label_id = 'Pasaport';
                }
                // $file_path'ı $murettebat_images klasörüne taşı

                $filename = basename($file_path);
                $target_path = $murettebat_images . DS . $filename;

                if (rename($file_path, $target_path)) {
                    // Taşıma başarılıysa eski dosyayı silmeye gerek yok, $file_path artık yok
                    $file_path = $target_path;
                } else {
                    // Taşıma başarısızsa eski dosya kalır, $file_path değişmez
                }

                list($name, $uzanti) = explode('.', $filename);
                $_data = array();
                $_data['dosyaadi'] = $filename;
                $_data['murettebat_id'] = ($murettebat_id > 0) ? $murettebat_id : 0;
                $_data['label'] = ($label_id) ? $label_id : '';
                $_data['uzanti'] = $uzanti;
                $_data['proje_id'] = $project_id;
                $_data['position'] = set_postion('murettebat_dokumanlari');
                fn_insert_array('murettebat_dokumanlari', $_data);
            }
        }
    }
}
// Example usage:
/*
try {
    echo parse_date_to_unix("2025-06-16") . "\n";
    echo parse_date_to_unix("16.06.2025 13:45:30") . "\n";
    echo parse_date_to_unix("06/16/2025") . "\n";
} catch (Exception $e) {
    echo $e->getMessage();
}
*/


function parse_date_to_unix($date_string)
{
    // Supported formats array
    $formats = [
        'Y-m-d',
        'Y-m-d H:i:s',
        'd.m.Y',
        'd.m.Y H:i:s',
        'm/d/Y',
        'm/d/Y H:i:s',
        'd/M/Y',       // for 15/Feb/1992
        'd/M/Y H:i:s', // for 15/Feb/1992 13:45:30
        'd-M-Y',       // for 15-Feb-1992
        'd-M-Y H:i:s'  // for 15-Feb-1992 13:45:30
    ];

    // Try to parse with each format
    foreach ($formats as $format) {
        $date = DateTime::createFromFormat($format, $date_string);
        if ($date !== false) {
            return $date->getTimestamp();
        }
    }

    // If we still can't parse, try strtotime as fallback
    $timestamp = strtotime($date_string);
    if ($timestamp !== false) {
        return $timestamp;
    }

    throw new Exception("Could not parse date string: " . $date_string);
}

/**
 * Crew Member Text Parser - Mürettebat Metin Ayrıştırıcısı
 *
 * Bu fonksiyon crew member verilerini içeren metinleri ayrıştırır ve yapılandırılmış diziler oluşturur.
 * Farklı text formatlarını otomatik olarak tespit eder ve uygun parser'ı çağırır.
 *
 * @param string $text - Ayrıştırılacak metin
 * @param array $processed_attachments - İşlenmiş ek dosyalar dizisi
 * @return array - Yapılandırılmış crew member verileri
 */
function parse_crew_member_text($text, $processed_attachments = [])
{
    // Format tespiti yap
    $format = detect_text_format($text);

    switch ($format) {
        case 'format_1': // ON-SIGNERS/OFF-SIGNERS format
            return parse_crew_format_1($text, $processed_attachments);

        case 'format_2': // ONSIGNER/OFFSIGNER format
            return parse_crew_format_2($text, $processed_attachments);

        default:
            // Varsayılan olarak format 1'i dene
            return parse_crew_format_1($text, $processed_attachments);
    }
}

/**
 * Text formatını tespit eder
 *
 * @param string $text - Analiz edilecek metin
 * @return string - Format türü
 */
function detect_text_format($text)
{
    // Format 2 kontrolü: ONSIGNER/OFFSIGNER ve Surname:/Name: pattern
    if ((strpos($text, 'ONSIGNER') !== false || strpos($text, 'OFFSIGNER') !== false) &&
        (strpos($text, 'Surname:') !== false && strpos($text, 'Name:') !== false)
    ) {
        return 'format_2';
    }

    // Format 1 kontrolü: ON-SIGNERS:/OFF-SIGNERS: pattern
    if (strpos($text, 'ON-SIGNERS:') !== false || strpos($text, 'OFF-SIGNERS:') !== false) {
        return 'format_1';
    }

    return 'format_1'; // Varsayılan
}

/**
 * Format 1 Parser - Orijinal ON-SIGNERS/OFF-SIGNERS format
 *
 * @param string $text - Ayrıştırılacak metin
 * @param array $processed_attachments - İşlenmiş ek dosyalar dizisi
 * @return array - Yapılandırılmış crew member verileri
 */
function parse_crew_format_1($text, $processed_attachments = [])
{
    // ped($processed_attachments);
    $result = [
        'on_signers' => [],
        'off_signers' => [],
        'on_signers_flight_details' => '',
        'off_signers_flight_details' => '',
        'statistics' => [
            'on_signers_count' => 0,
            'off_signers_count' => 0,
            'total_attachments_matched' => 0
        ]
    ];

    // Metni satırlara böl
    $lines = explode("\n", $text);
    $current_section = '';
    $current_crew_data = '';

    foreach ($lines as $line) {
        $line = trim($line);

        // Bölüm başlıklarını tespit et
        if (preg_match('/^ON-SIGNERS:\s*(\d+)/', $line, $matches)) {
            $current_section = 'on_signers';
            $result['statistics']['on_signers_count'] = (int)$matches[1];
            continue;
        }

        if (preg_match('/^OFF-SIGNERS:\s*(\d+)/', $line, $matches)) {
            // Önceki bölümü tamamla
            if ($current_section == 'on_signers' && $current_crew_data) {
                $result['on_signers'][] = parse_single_crew_member($current_crew_data);
                $current_crew_data = '';
            }

            $current_section = 'off_signers';
            $result['statistics']['off_signers_count'] = (int)$matches[1];
            continue;
        }

        if (preg_match('/FLIGHT DETAILS?:/i', $line)) {
            // Önceki crew member verisini tamamla
            if ($current_crew_data) {
                if ($current_section == 'on_signers') {
                    $result['on_signers'][] = parse_single_crew_member($current_crew_data);
                } elseif ($current_section == 'off_signers') {
                    $result['off_signers'][] = parse_single_crew_member($current_crew_data);
                }
                $current_crew_data = '';
            }

            $current_section = ($current_section == 'on_signers') ? 'on_signers_flight' : 'off_signers_flight';
            continue;
        }

        // Yıldız çizgisi veya boş satırları atla
        if (empty($line) || preg_match('/^\*+$/', $line) || preg_match('/^-+$/', $line)) {
            continue;
        }

        // Flight details topla
        if ($current_section == 'on_signers_flight') {
            $result['on_signers_flight_details'] .= $line . "\n";
            continue;
        }

        if ($current_section == 'off_signers_flight') {
            $result['off_signers_flight_details'] .= $line . "\n";
            continue;
        }

        // Crew member verilerini topla
        if ($current_section == 'on_signers' || $current_section == 'off_signers') {
            // Yeni crew member başlangıcını tespit et
            if (preg_match('/^Name:\s*(.+)/', $line)) {
                // Önceki crew member verisini kaydet
                if ($current_crew_data) {
                    if ($current_section == 'on_signers') {
                        $result['on_signers'][] = parse_single_crew_member($current_crew_data);
                    } else {
                        $result['off_signers'][] = parse_single_crew_member($current_crew_data);
                    }
                }
                $current_crew_data = $line . "\n";
            } else {
                $current_crew_data .= $line . "\n";
            }
        }
    }

    // Son crew member verisini kaydet
    if ($current_crew_data) {
        if ($current_section == 'on_signers') {
            $result['on_signers'][] = parse_single_crew_member($current_crew_data);
        } elseif ($current_section == 'off_signers') {
            $result['off_signers'][] = parse_single_crew_member($current_crew_data);
        }
    }

    // Ek dosyaları crew member'lara eşleştir
    if (!empty($processed_attachments)) {
        $result = match_attachments_to_crew($result, $processed_attachments);
    }

    return $result;
}

/**
 * Tek bir crew member verisini ayrıştırır
 *
 * @param string $crew_data - Tek crew member'a ait ham metin verisi
 * @return array - Ayrıştırılmış crew member bilgileri
 */
function parse_single_crew_member($crew_data)
{
    $crew = [
        'name' => '',
        'firstname' => '',
        'lastname' => '',
        'rank' => '',
        'passport' => [
            'number' => '',
            'issued' => '',
            'expiry' => ''
        ],
        'seaman_book' => [
            'number' => '',
            'issued' => '',
            'expiry' => ''
        ],
        'nationality' => '',
        'date_of_birth' => '',
        'place_of_birth' => '',
        'attachments' => []
    ];

    $lines = explode("\n", trim($crew_data));

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // Name ve Rank
        if (preg_match('/^Name:\s*(.+?)\s+Rank:\s*(.+)$/i', $line, $matches)) {
            $crew['name'] = trim($matches[1]);
            $crew['rank'] = trim($matches[2]);

            // İsmi parçalara ayır
            $name_parts = explode(',', $crew['name']);
            if (count($name_parts) >= 2) {
                $crew['lastname'] = trim($name_parts[0]);
                $crew['firstname'] = trim($name_parts[1]);
            } else {
                $name_parts = explode(' ', $crew['name']);
                $crew['firstname'] = trim($name_parts[0]);
                if (count($name_parts) > 1) {
                    $crew['lastname'] = trim(implode(' ', array_slice($name_parts, 1)));
                }
            }
        }

        // Passport bilgileri
        if (preg_match('/^Passport:\s*(.+?)\s+Issued:\s*(.+?)\s+Expiry:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['number'] = trim($matches[1]);
            $crew['passport']['issued'] = trim($matches[2]);
            $crew['passport']['expiry'] = trim($matches[3]);
        }

        // Seaman Book bilgileri
        if (preg_match('/^Seaman Book:\s*(.+?)\s+Issued:\s*(.+?)\s+Expiry:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['number'] = trim($matches[1]);
            $crew['seaman_book']['issued'] = trim($matches[2]);
            $crew['seaman_book']['expiry'] = trim($matches[3]);
        }

        // Nationality, Date of Birth, Place of Birth
        if (preg_match('/^Nationality:\s*(.+?)\s+Date of Birth:\s*(.+?)\s+Place of Birth:\s*(.+)$/i', $line, $matches)) {
            $crew['nationality'] = trim($matches[1]);
            $crew['date_of_birth'] = trim($matches[2]);
            $crew['place_of_birth'] = trim($matches[3]);
        }
    }

    return $crew;
}

/**
 * Ek dosyaları crew member'lara eşleştirir
 *
 * @param array $crew_data - Crew member verileri
 * @param array $processed_attachments - İşlenmiş ek dosyalar
 * @return array - Ek dosyaları eşleştirilmiş crew member verileri
 */
function match_attachments_to_crew($crew_data, $processed_attachments)
{
    $total_matched = 0;

    // Tüm crew member'ları kontrol et
    $all_crews = array_merge($crew_data['on_signers'], $crew_data['off_signers']);

    foreach ($processed_attachments as $attachment) {
        $filename = strtolower($attachment['filename']);

        foreach ($all_crews as $crew) {
            // İsim eşleştirmesi için farklı formatları dene
            $name_variations = generate_name_variations($crew);

            foreach ($name_variations as $name_variation) {
                $name_variation_lower = strtolower($name_variation);
                if (strpos($filename, $name_variation_lower) !== false) {
                    // Dosya türünü belirle
                    $document_type = determine_document_type($filename);

                    $attachment_info = [
                        'filename' => $attachment['filename'],
                        'path' => $attachment['path'],
                        'size' => $attachment['size'],
                        'type' => $document_type,
                        'matched_name' => $name_variation
                    ];

                    // Crew member'ın hangi listede olduğunu bul ve ekle
                    $found_in_on_signers = false;
                    foreach ($crew_data['on_signers'] as &$on_signer) {
                        if ($on_signer['name'] == $crew['name']) {
                            $on_signer['attachments'][] = $attachment_info;
                            $found_in_on_signers = true;
                            break;
                        }
                    }

                    if (!$found_in_on_signers) {
                        foreach ($crew_data['off_signers'] as &$off_signer) {
                            if ($off_signer['name'] == $crew['name']) {
                                $off_signer['attachments'][] = $attachment_info;
                                break;
                            }
                        }
                    }

                    $total_matched++;
                    break 2; // İç ve dış döngüden çık
                }
            }
        }
    }

    $crew_data['statistics']['total_attachments_matched'] = $total_matched;
    return $crew_data;
}

/**
 * İsim varyasyonları oluşturur
 *
 * @param array $crew - Crew member bilgileri
 * @return array - İsim varyasyonları
 */
function generate_name_variations($crew)
{
    $variations = [];

    if (!empty($crew['name'])) {
        // Orijinal isim
        $variations[] = $crew['name'];
        $variations[] = str_replace(',', '', $crew['name']);
        $variations[] = str_replace(' ', '', $crew['name']);
        $variations[] = str_replace(',', '_', $crew['name']);
        $variations[] = str_replace(' ', '_', $crew['name']);

        // Virgüllü format: "ABANILLA,JOHN ALBERT ANSELMO" -> "ABANILLA JOHN ALBERT"
        if (strpos($crew['name'], ',') !== false) {
            $name_parts = explode(',', $crew['name']);
            $lastname = trim($name_parts[0]);
            $firstname_parts = trim($name_parts[1]);

            // "LASTNAME FIRSTNAME" formatı (tam isim)
            $variations[] = $lastname . ' ' . $firstname_parts;
            $variations[] = $lastname . '_' . str_replace(' ', '_', $firstname_parts);
            $variations[] = $lastname . str_replace(' ', '', $firstname_parts);

            // İsim parçalarını ayır
            $name_words = explode(' ', $firstname_parts);

            // Sadece ilk isim ile
            if (count($name_words) > 0) {
                $first_name_only = $name_words[0];
                $variations[] = $lastname . ' ' . $first_name_only;
                $variations[] = $lastname . '_' . $first_name_only;
                $variations[] = $lastname . $first_name_only;
            }

            // İlk iki isim ile (JOHN ALBERT)
            if (count($name_words) >= 2) {
                $first_two_names = $name_words[0] . ' ' . $name_words[1];
                $variations[] = $lastname . ' ' . $first_two_names;
                $variations[] = $lastname . '_' . str_replace(' ', '_', $first_two_names);
                $variations[] = $lastname . str_replace(' ', '', $first_two_names);
            }

            // İlk üç isim ile (eğer varsa)
            if (count($name_words) >= 3) {
                $first_three_names = $name_words[0] . ' ' . $name_words[1] . ' ' . $name_words[2];
                $variations[] = $lastname . ' ' . $first_three_names;
                $variations[] = $lastname . '_' . str_replace(' ', '_', $first_three_names);
                $variations[] = $lastname . str_replace(' ', '', $first_three_names);
            }
        }
    }

    // Format 2 için özel logic: firstname ve lastname ayrı ayrı gelir
    if (!empty($crew['firstname']) && !empty($crew['lastname'])) {
        // Format 2 style: "MAJICA VOJISLAV" (LASTNAME FIRSTNAME)
        $variations[] = $crew['lastname'] . ' ' . $crew['firstname'];
        $variations[] = $crew['lastname'] . '_' . $crew['firstname'];
        $variations[] = $crew['lastname'] . $crew['firstname'];

        // Ters sıra: "VOJISLAV MAJICA" (FIRSTNAME LASTNAME)
        $variations[] = $crew['firstname'] . ' ' . $crew['lastname'];
        $variations[] = $crew['firstname'] . '_' . $crew['lastname'];
        $variations[] = $crew['firstname'] . $crew['lastname'];

        // Format 2'de firstname birden fazla kelime olabilir: "JERIVIC HENRY"
        $firstname_words = explode(' ', trim($crew['firstname']));
        if (count($firstname_words) > 1) {
            // Sadece ilk isim ile: "MAJICA JERIVIC"
            $variations[] = $crew['lastname'] . ' ' . $firstname_words[0];
            $variations[] = $crew['lastname'] . '_' . $firstname_words[0];
            $variations[] = $crew['lastname'] . $firstname_words[0];

            // Ters: "JERIVIC MAJICA"
            $variations[] = $firstname_words[0] . ' ' . $crew['lastname'];
            $variations[] = $firstname_words[0] . '_' . $crew['lastname'];
            $variations[] = $firstname_words[0] . $crew['lastname'];

            // İlk iki isim ile: "MAJICA JERIVIC HENRY"
            if (count($firstname_words) >= 2) {
                $first_two = $firstname_words[0] . ' ' . $firstname_words[1];
                $variations[] = $crew['lastname'] . ' ' . $first_two;
                $variations[] = $crew['lastname'] . '_' . str_replace(' ', '_', $first_two);
                $variations[] = $crew['lastname'] . str_replace(' ', '', $first_two);
            }
        }

        // Büyük harf versiyonları
        $variations[] = strtoupper($crew['lastname'] . ' ' . $crew['firstname']);
        $variations[] = strtoupper($crew['lastname'] . '_' . $crew['firstname']);
        $variations[] = strtoupper($crew['lastname'] . $crew['firstname']);
        $variations[] = strtoupper($crew['firstname'] . ' ' . $crew['lastname']);
        $variations[] = strtoupper($crew['firstname'] . '_' . $crew['lastname']);
        $variations[] = strtoupper($crew['firstname'] . $crew['lastname']);
    }

    // Passport numarası da ekle
    if (!empty($crew['passport']['number'])) {
        $variations[] = $crew['passport']['number'];
    }

    // Seaman book numarası da ekle
    if (!empty($crew['seaman_book']['number'])) {
        $variations[] = $crew['seaman_book']['number'];
    }

    // Sadece isim ve soyisim ile basit kombinasyonlar (en yaygın durumlar)
    if (!empty($crew['firstname']) && !empty($crew['lastname'])) {
        // Sadece soyisim (MAJICA)
        $variations[] = $crew['lastname'];

        // Sadece isim (VOJISLAV)
        $variations[] = $crew['firstname'];

        // İsmin ilk kelimesi (JERIVIC HENRY -> JERIVIC)
        $first_name_parts = explode(' ', trim($crew['firstname']));
        if (count($first_name_parts) > 0) {
            $variations[] = $first_name_parts[0];
        }
    }

    // Tüm varyasyonları hem büyük hem küçük harfe çevir
    $all_case_variations = [];
    foreach ($variations as $variation) {
        $all_case_variations[] = $variation;                    // Orijinal
        $all_case_variations[] = strtoupper($variation);        // BÜYÜK HARF
        $all_case_variations[] = strtolower($variation);        // küçük harf
        $all_case_variations[] = ucfirst(strtolower($variation)); // İlk harf büyük
        $all_case_variations[] = ucwords(strtolower($variation)); // Her kelimenin ilk harfi büyük
    }

    return array_unique($all_case_variations);
}

/**
 * Dosya türünü belirler
 *
 * @param string $filename - Dosya adı
 * @return string - Dosya türü
 */
function determine_document_type($filename)
{
    $filename_lower = strtolower($filename);

    // Passport kontrolü - PP, PASSPORT, PASAPORT
    if (
        strpos($filename_lower, 'passport') !== false ||
        strpos($filename_lower, 'pasaport') !== false ||
        strpos($filename_lower, '-pp ') !== false ||
        strpos($filename_lower, '_pp_') !== false ||
        strpos($filename_lower, '(passport)') !== false
    ) {
        return 'Passport';
    }

    // Seaman Book kontrolü - SBK, SEAMAN, BOOK
    if (
        strpos($filename_lower, 'seaman') !== false ||
        strpos($filename_lower, 'book') !== false ||
        strpos($filename_lower, '-sbk ') !== false ||
        strpos($filename_lower, '_sbk_') !== false ||
        strpos($filename_lower, 'seaman book') !== false
    ) {
        return 'Seaman Book';
    }

    // Medical Certificate kontrolü
    if (
        strpos($filename_lower, 'medical') !== false ||
        strpos($filename_lower, 'health') !== false ||
        strpos($filename_lower, 'saglik') !== false ||
        strpos($filename_lower, '-med ') !== false ||
        strpos($filename_lower, '_med_') !== false ||
        strpos($filename_lower, 'certificate') !== false
    ) {
        return 'Medical Certificate';
    }

    // SID kontrolü
    if (
        strpos($filename_lower, 'sid') !== false ||
        strpos($filename_lower, '-sid ') !== false ||
        strpos($filename_lower, '_sid_') !== false
    ) {
        return 'SID';
    }

    return 'Other';
}

/**
 * Debug fonksiyonu - İsim eşleştirme sürecini gösterir
 */
function debug_name_matching($crew_data, $processed_attachments)
{
    echo "<h3>Debug: İsim Eşleştirme Süreci</h3>";

    $all_crews = array_merge($crew_data['on_signers'], $crew_data['off_signers']);

    echo "<h4>Crew Members:</h4>";
    foreach ($all_crews as $index => $crew) {
        echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
        echo "<strong>Crew #" . ($index + 1) . ":</strong> " . htmlspecialchars($crew['name']) . "<br>";
        echo "<strong>Firstname:</strong> " . htmlspecialchars($crew['firstname']) . "<br>";
        echo "<strong>Lastname:</strong> " . htmlspecialchars($crew['lastname']) . "<br>";
        echo "<strong>Rank:</strong> " . htmlspecialchars($crew['rank']) . "<br>";

        // Format 2 ek bilgileri
        if (isset($crew['mobile_phone']) && $crew['mobile_phone']) {
            echo "<strong>Mobile:</strong> " . htmlspecialchars($crew['mobile_phone']) . "<br>";
        }
        if (isset($crew['email']) && $crew['email']) {
            echo "<strong>Email:</strong> " . htmlspecialchars($crew['email']) . "<br>";
        }

        $variations = generate_name_variations($crew);
        echo "<strong>Name Variations (" . count($variations) . " total):</strong><br>";
        foreach ($variations as $variation) {
            echo "- " . htmlspecialchars($variation) . "<br>";
        }
        echo "</div>";
    }

    echo "<h4>Attachments:</h4>";
    foreach ($processed_attachments as $index => $attachment) {
        echo "<div style='border: 1px solid #ddd; margin: 10px; padding: 10px; background: #f9f9f9;'>";
        echo "<strong>Attachment #" . ($index + 1) . ":</strong> " . htmlspecialchars($attachment['filename']) . "<br>";
        echo "<strong>Lowercase:</strong> " . htmlspecialchars(strtolower($attachment['filename'])) . "<br>";
        echo "<strong>Document Type:</strong> " . determine_document_type($attachment['filename']) . "<br>";

        echo "<strong>Matching attempts:</strong><br>";
        foreach ($all_crews as $crew_index => $crew) {
            $variations = generate_name_variations($crew);
            $found_match = false;
            foreach ($variations as $variation) {
                if (strpos(strtolower($attachment['filename']), strtolower($variation)) !== false) {
                    echo "✅ MATCH with Crew #" . ($crew_index + 1) . " (" . htmlspecialchars($crew['name']) . ") using variation: " . htmlspecialchars($variation) . "<br>";
                    $found_match = true;
                    break;
                }
            }
            if (!$found_match) {
                echo "❌ No match with Crew #" . ($crew_index + 1) . " (" . htmlspecialchars($crew['name']) . ")<br>";
            }
        }
        echo "</div>";
    }
}

/**
 * Format 2 Parser - ONSIGNER/OFFSIGNER format
 *
 * @param string $text - Ayrıştırılacak metin
 * @param array $processed_attachments - İşlenmiş ek dosyalar dizisi
 * @return array - Yapılandırılmış crew member verileri
 */
function parse_crew_format_2($text, $processed_attachments = [])
{
    $result = [
        'on_signers' => [],
        'off_signers' => [],
        'on_signers_flight_details' => '',
        'off_signers_flight_details' => '',
        'statistics' => [
            'on_signers_count' => 0,
            'off_signers_count' => 0,
            'total_attachments_matched' => 0
        ]
    ];

    // Metni satırlara böl
    $lines = explode("\n", $text);
    $current_section = '';
    $current_crew_data = '';

    foreach ($lines as $line) {
        $line = trim($line);

        // Bölüm başlıklarını tespit et
        if (strtoupper($line) == 'ONSIGNER') {
            // Önceki crew member verisini tamamla
            if ($current_crew_data && $current_section == 'off_signers') {
                $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                $current_crew_data = '';
            }

            $current_section = 'on_signers';
            continue;
        }

        if (strtoupper($line) == 'OFFSIGNER') {
            // Önceki crew member verisini tamamla
            if ($current_crew_data && $current_section == 'on_signers') {
                $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                $current_crew_data = '';
            }

            $current_section = 'off_signers';
            continue;
        }

        // Boş satırları atla
        if (empty($line)) {
            // Eğer crew data varsa ve boş satır gelirse, crew member'ı tamamla
            if ($current_crew_data) {
                if ($current_section == 'on_signers') {
                    $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                } elseif ($current_section == 'off_signers') {
                    $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                }
                $current_crew_data = '';
            }
            continue;
        }

        // Crew member verilerini topla
        if ($current_section == 'on_signers' || $current_section == 'off_signers') {
            // Yeni crew member başlangıcını tespit et (Surname: ile başlayan satır)
            if (preg_match('/^Surname:\s*(.+)/', $line)) {
                // Önceki crew member verisini kaydet
                if ($current_crew_data) {
                    if ($current_section == 'on_signers') {
                        $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                    } else {
                        $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
                    }
                }
                $current_crew_data = $line . "\n";
            } else {
                $current_crew_data .= $line . "\n";
            }
        }
    }

    // Son crew member verisini kaydet
    if ($current_crew_data) {
        if ($current_section == 'on_signers') {
            $result['on_signers'][] = parse_single_crew_member_format_2($current_crew_data);
        } elseif ($current_section == 'off_signers') {
            $result['off_signers'][] = parse_single_crew_member_format_2($current_crew_data);
        }
    }

    // İstatistikleri güncelle
    $result['statistics']['on_signers_count'] = count($result['on_signers']);
    $result['statistics']['off_signers_count'] = count($result['off_signers']);

    // Ek dosyaları crew member'lara eşleştir
    if (!empty($processed_attachments)) {
        $result = match_attachments_to_crew($result, $processed_attachments);
    }

    return $result;
}

/**
 * Format 2 için tek bir crew member verisini ayrıştırır
 *
 * @param string $crew_data - Tek crew member'a ait ham metin verisi
 * @return array - Ayrıştırılmış crew member bilgileri
 */
function parse_single_crew_member_format_2($crew_data)
{
    $crew = [
        'name' => '',
        'firstname' => '',
        'lastname' => '',
        'rank' => '',
        'passport' => [
            'number' => '',
            'issued' => '',
            'expiry' => '',
            'issue_place' => ''
        ],
        'seaman_book' => [
            'number' => '',
            'issued' => '',
            'expiry' => '',
            'issue_place' => ''
        ],
        'nationality' => '',
        'date_of_birth' => '',
        'place_of_birth' => '',
        'mobile_phone' => '',
        'email' => '',
        'attachments' => []
    ];

    $lines = explode("\n", trim($crew_data));

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // Surname
        if (preg_match('/^Surname:\s*(.+)$/i', $line, $matches)) {
            $crew['lastname'] = trim($matches[1]);
        }

        // Name (firstname)
        if (preg_match('/^Name:\s*(.+)$/i', $line, $matches)) {
            $crew['firstname'] = trim($matches[1]);
        }

        // Rank
        if (preg_match('/^Rank:\s*(.+)$/i', $line, $matches)) {
            $crew['rank'] = trim($matches[1]);
        }

        // Nationality
        if (preg_match('/^Nationality:\s*(.+)$/i', $line, $matches)) {
            $crew['nationality'] = trim($matches[1]);
        }

        // DOB (Date of Birth)
        if (preg_match('/^DOB:\s*(.+)$/i', $line, $matches)) {
            $crew['date_of_birth'] = trim($matches[1]);
        }

        // POB (Place of Birth)
        if (preg_match('/^POB:\s*(.+)$/i', $line, $matches)) {
            $crew['place_of_birth'] = trim($matches[1]);
        }

        // Mobile Phone
        if (preg_match('/^Mobile Phone:\s*(.+)$/i', $line, $matches)) {
            $crew['mobile_phone'] = trim($matches[1]);
        }

        // Email
        if (preg_match('/^Email:\s*(.+)$/i', $line, $matches)) {
            $crew['email'] = trim($matches[1]);
        }

        // Passport No
        if (preg_match('/^Passport No\.?:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['number'] = trim($matches[1]);
        }

        // Passport Issue Place
        if (preg_match('/^Passport Issue Place:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['issue_place'] = trim($matches[1]);
        }

        // Passport Issue Date
        if (preg_match('/^Passport Issue Date:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['issued'] = trim($matches[1]);
        }

        // Passport Expiry Date
        if (preg_match('/^Passport Expiry Date:\s*(.+)$/i', $line, $matches)) {
            $crew['passport']['expiry'] = trim($matches[1]);
        }

        // Seaman's Book No
        if (preg_match('/^Seaman\'?s Book No\.?:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['number'] = trim($matches[1]);
        }

        // Seaman's Book Issue Place
        if (preg_match('/^Seaman\'?s Book Issue Place:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['issue_place'] = trim($matches[1]);
        }

        // Seaman's Book Issue Date
        if (preg_match('/^Seaman\'?s Book Issue Date:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['issued'] = trim($matches[1]);
        }

        // Seaman's Book Expiry Date
        if (preg_match('/^Seaman\'?s Book Expiry Date:\s*(.+)$/i', $line, $matches)) {
            $crew['seaman_book']['expiry'] = trim($matches[1]);
        }
    }

    // Tam ismi oluştur
    if ($crew['lastname'] && $crew['firstname']) {
        $crew['name'] = $crew['lastname'] . ',' . $crew['firstname'];
    }

    return $crew;
}


// Gelen verileri al
$selected_text = '';
$target_section = '';
$email_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$folder = isset($_GET['folder']) ? $_GET['folder'] : 'INBOX';
$attachments = [];

// Gerekli kontroller
if (!isset($email_id)) {
    fn_set_notice('Seçili id bulunamadı.', 'E');
    fn_redirect(fn_menu_link('mailbox/inbox', 1));
}
// IMAP bağlantısı aç
$mailbox = mailbox_connect();



// Mail içeriğini al
$email = get_email_content($mailbox, $email_id);
$attachments = $email['attachments'] ?? [];
$selected_text = (htmlspecialchars(strip_tags($email['body'])));

// Ekleri işle
$processed_attachments = [];
if (!empty($attachments) && $email_id > 0) {
    try {



        // tmp_move klasörünün varlığını kontrol et, yoksa oluştur
        $tmp_move_dir = ADDONS_DIR . '/mailbox/tmp_move/' . $email_id;
        if (!file_exists($tmp_move_dir)) {
            mkdir($tmp_move_dir, 0755, true);
        }


        foreach ($attachments as $attachment) {
            try {
                // download_attachment fonksiyonunu kullan (Apple Mail boundary parsing dahil)
                $download_result = download_attachment($mailbox, $email_id, $attachment['part_num']);

                if ($download_result === false || empty($download_result)) {
                    error_log("Failed to download attachment: " . $attachment['filename']);
                    continue;
                }

                // download_attachment array döndürüyor: ['filename' => ..., 'temp_file' => ..., 'mime_type' => ...]
                $temp_file = $download_result['temp_file'];
                $attachment_data = file_get_contents($temp_file);

                if ($attachment_data === false || empty($attachment_data)) {
                    error_log("Failed to read temp file: " . $temp_file);
                    @unlink($temp_file);
                    continue;
                }

                // Dosyayı tmp_move klasörüne kaydet
                $safe_filename = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $attachment['filename']);
                $file_path = $tmp_move_dir . '/' . $safe_filename;

                // Aynı isimde dosya varsa, isim sonuna sayı ekle
                $counter = 1;
                $original_name = pathinfo($file_path, PATHINFO_FILENAME);
                $extension = pathinfo($file_path, PATHINFO_EXTENSION);
                $file_path = $tmp_move_dir . '/' . $original_name . '_' . $counter . '.' . $extension;
                // while (file_exists($file_path)) {
                //     $file_path = $tmp_move_dir . '/' . $original_name . '_' . $counter . '.' . $extension;
                //     $counter++;
                // }

                // Dosya varsa kaydetme! 16 Haziran 2025
                if (!is_file($file_path)) {
                    // Dosyayı kaydet
                    if (file_put_contents($file_path, $attachment_data)) {
                        $processed_attachments[] = [
                            'filename' => $attachment['filename'],
                            'path' => $file_path,
                            'size' => strlen($attachment_data) // Gerçek boyutu kullan
                        ];
                        // error_log("Successfully saved attachment: " . $attachment['filename'] . " (" . strlen($attachment_data) . " bytes)");
                    } else {
                        // error_log("Failed to save attachment: " . $attachment['filename']);
                    }
                } else {
                    $processed_attachments[] = [
                        'filename' => $attachment['filename'],
                        'path' => $file_path,
                        'size' => strlen($attachment_data) // Gerçek boyutu kullan
                    ];
                }


                // Temp dosyayı temizle
                @unlink($temp_file);
            } catch (Exception $e) {
                error_log("Error processing attachment " . $attachment['filename'] . ": " . $e->getMessage());
                // Temp dosyayı temizle (eğer varsa)
                if (isset($temp_file)) {
                    @unlink($temp_file);
                }
                continue;
            }
        }


        // IMAP bağlantısını kapat
    } catch (Exception $e) {
        fn_set_notice('Ekler işlenirken hata oluştu: ' . $e->getMessage(), 'E');
    }
}

imap_close($mailbox);


// Crew member text parsing işlemi
$parsed_crew_data = null;
if (!empty($selected_text)) {
    // Metinde crew member verisi olup olmadığını kontrol et
    if (
        strpos($selected_text, 'ON-SIGNERS:') !== false ||
        strpos($selected_text, 'OFF-SIGNERS:') !== false ||
        strpos($selected_text, 'ONSIGNER') !== false ||
        strpos($selected_text, 'OFFSIGNER') !== false ||
        (strpos($selected_text, 'Surname:') !== false && strpos($selected_text, 'Name:') !== false)
    ) {

        $parsed_crew_data = parse_crew_member_text($selected_text, $processed_attachments);

        // Debug için parsed data'yı session'a kaydet
        // $_SESSION['calisma']['parsed_crew_data'] = $parsed_crew_data;
    }
}

// Sonuçları göster
?>
<form id="crewDataForm" method="post" action="">
    <input type="hidden" name="id" value="<?php echo $email_id; ?>">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Aktarılacak Bilgiler</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($processed_attachments)): ?>
                <div class="mb-4">
                    <h4 class="fw-bold">Aktarılan Ekler</h4>
                    <div class="scroll h-300px ">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>Dosya Adı</th>
                                        <th>Boyut</th>
                                        <th>Geçici Konum</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($processed_attachments as $attachment): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($attachment['filename']); ?></td>
                                            <td><?php echo format_file_size($attachment['size']); ?></td>
                                            <td><code><?php echo htmlspecialchars($attachment['path']); ?></code></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($parsed_crew_data): ?>
                <div class="mb-4">
                    <h4 class="fw-bold">Ayrıştırılmış Crew Member Verileri</h4>

                    <!-- Format bilgisi -->
                    <?php
                    $detected_format = detect_text_format($selected_text);
                    $format_name = ($detected_format == 'format_2') ? 'ONSIGNER/OFFSIGNER Format' : 'ON-SIGNERS/OFF-SIGNERS Format';
                    ?>
                    <div class="alert alert-success">
                        <strong>Tespit Edilen Format:</strong> <?php echo htmlspecialchars($format_name); ?> (<?php echo htmlspecialchars($detected_format); ?>)
                    </div>

                    <!-- Success Message -->
                    <?php if (isset($_SESSION['success_message'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Başarılı!</strong> <?php echo htmlspecialchars($_SESSION['success_message']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php unset($_SESSION['success_message']); ?>
                    <?php endif; ?>

                    <!-- Debug output -->
                    <?php if (1 == 2): ?>
                        <div class="alert alert-info">
                            <h5>Debug: İsim Eşleştirme Süreci</h5>
                            <?php debug_name_matching($parsed_crew_data, $processed_attachments);
                            ?>
                        </div>
                    <?php endif; ?>

                    <!-- İstatistikler -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo $parsed_crew_data['statistics']['on_signers_count']; ?></h5>
                                    <small>ON-SIGNERS</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo $parsed_crew_data['statistics']['off_signers_count']; ?></h5>
                                    <small>OFF-SIGNERS</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo $parsed_crew_data['statistics']['total_attachments_matched']; ?></h5>
                                    <small>Eşleşen Ek Dosya</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ON-SIGNERS Tablosu -->
                    <?php if (!empty($parsed_crew_data['on_signers'])): ?>
                        <div class="mb-4">
                            <h5 class="text-primary">ON-SIGNERS</h5>

                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>Ad Soyad</th>
                                            <th>Rütbe</th>
                                            <th>Pasaport</th>
                                            <th>Seaman Book</th>
                                            <th>Uyruk</th>
                                            <th>Doğum Tarihi</th>
                                            <?php if ($detected_format == 'format_2'): ?>
                                                <th>İletişim</th>
                                            <?php endif; ?>
                                            <th>Ek Dosyalar</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($parsed_crew_data['on_signers'] as $index => $crew): ?>
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][name]"
                                                        value="<?php echo htmlspecialchars(trim($crew['name'])); ?>"
                                                        placeholder="Ad Soyad">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][firstname]"
                                                        value="<?php echo htmlspecialchars(trim($crew['firstname'])); ?>"
                                                        placeholder="Ad">
                                                    <input type="text" class="form-control form-control-sm mt-1"
                                                        name="on_signers[<?php echo $index; ?>][lastname]"
                                                        value="<?php echo htmlspecialchars(trim($crew['lastname'])); ?>"
                                                        placeholder="Soyad">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][rank]"
                                                        value="<?php echo htmlspecialchars(trim($crew['rank'])); ?>"
                                                        placeholder="Rütbe">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][passport][number]"
                                                        value="<?php echo htmlspecialchars(trim($crew['passport']['number'])); ?>"
                                                        placeholder="Pasaport No">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][passport][issued]"
                                                        value="<?php echo htmlspecialchars(trim($crew['passport']['issued'])); ?>"
                                                        placeholder="Veriliş Tarihi">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][passport][expiry]"
                                                        value="<?php echo htmlspecialchars(trim($crew['passport']['expiry'])); ?>"
                                                        placeholder="Bitiş Tarihi">
                                                    <?php if ($detected_format == 'format_2'): ?>
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="on_signers[<?php echo $index; ?>][passport][issue_place]"
                                                            value="<?php echo htmlspecialchars(trim($crew['passport']['issue_place'] ?? '')); ?>"
                                                            placeholder="Veriliş Yeri">
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][seaman_book][number]"
                                                        value="<?php echo htmlspecialchars(trim($crew['seaman_book']['number'])); ?>"
                                                        placeholder="Seaman Book No">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][seaman_book][issued]"
                                                        value="<?php echo htmlspecialchars(trim($crew['seaman_book']['issued'])); ?>"
                                                        placeholder="Veriliş Tarihi">
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][seaman_book][expiry]"
                                                        value="<?php echo htmlspecialchars(trim($crew['seaman_book']['expiry'])); ?>"
                                                        placeholder="Bitiş Tarihi">
                                                    <?php if ($detected_format == 'format_2'): ?>
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="on_signers[<?php echo $index; ?>][seaman_book][issue_place]"
                                                            value="<?php echo htmlspecialchars(trim($crew['seaman_book']['issue_place'] ?? '')); ?>"
                                                            placeholder="Veriliş Yeri">
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][nationality]"
                                                        value="<?php echo htmlspecialchars(trim($crew['nationality'])); ?>"
                                                        placeholder="Uyruk">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm mb-1"
                                                        name="on_signers[<?php echo $index; ?>][date_of_birth]"
                                                        value="<?php echo htmlspecialchars(trim($crew['date_of_birth'])); ?>"
                                                        placeholder="Doğum Tarihi">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="on_signers[<?php echo $index; ?>][place_of_birth]"
                                                        value="<?php echo htmlspecialchars(trim($crew['place_of_birth'])); ?>"
                                                        placeholder="Doğum Yeri">
                                                </td>
                                                <?php if ($detected_format == 'format_2'): ?>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="on_signers[<?php echo $index; ?>][mobile_phone]"
                                                            value="<?php echo htmlspecialchars(trim($crew['mobile_phone'] ?? '')); ?>"
                                                            placeholder="Cep Telefonu">
                                                        <input type="email" class="form-control form-control-sm"
                                                            name="on_signers[<?php echo $index; ?>][email]"
                                                            value="<?php echo htmlspecialchars(trim($crew['email'] ?? '')); ?>"
                                                            placeholder="E-posta">
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <div class="attachment-manager" data-crew-index="<?php echo $index; ?>" data-crew-type="on_signers">
                                                        <label class="form-label small fw-bold">Ek Dosyalar:</label>

                                                        <!-- Tespit edilen dosyalar -->
                                                        <?php if (!empty($crew['attachments'])): ?>
                                                            <div class="detected-files mb-3">
                                                                <small class="text-success fw-bold d-block mb-2">
                                                                    <i class="fas fa-check-circle me-1"></i>Tespit Edilen Dosyalar:
                                                                </small>
                                                                <?php foreach ($crew['attachments'] as $att_index => $attachment): ?>
                                                                    <div class="attachment-group mb-3 p-2 border rounded bg-light">
                                                                        <div class="row">
                                                                            <div class="col-12 mb-2">
                                                                                <label class="form-label small mb-1">Dosya:</label>
                                                                                <select class="form-select form-select-sm"
                                                                                    name="on_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][filename]">
                                                                                    <option value="">-- Dosya Seçin --</option>
                                                                                    <?php foreach ($processed_attachments as $proc_att): ?>
                                                                                        <option value="<?php echo htmlspecialchars(trim($proc_att['filename'])); ?>"
                                                                                            data-path="<?php echo htmlspecialchars(trim($proc_att['path'])); ?>"
                                                                                            <?php echo (trim($proc_att['filename']) == trim($attachment['filename'])) ? 'selected' : ''; ?>>
                                                                                            <?php echo htmlspecialchars(trim($proc_att['filename'])); ?>
                                                                                        </option>
                                                                                    <?php endforeach; ?>
                                                                                </select>
                                                                            </div>
                                                                            <div class="col-12">
                                                                                <label class="form-label small mb-1">Label:</label>
                                                                                <select class="form-select form-select-sm"
                                                                                    name="on_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][type]">
                                                                                    <option value="">-- Dosya Türü Seçin --</option>
                                                                                    <option value="Passport" <?php echo (trim($attachment['type']) == 'Passport') ? 'selected' : ''; ?>>Passport</option>
                                                                                    <option value="Seaman Book" <?php echo (trim($attachment['type']) == 'Seaman Book') ? 'selected' : ''; ?>>Seaman Book</option>
                                                                                    <option value="Medical Certificate" <?php echo (trim($attachment['type']) == 'Medical Certificate') ? 'selected' : ''; ?>>Medical Certificate</option>
                                                                                    <option value="SID" <?php echo (trim($attachment['type']) == 'SID') ? 'selected' : ''; ?>>SID</option>
                                                                                    <option value="Other" <?php echo (trim($attachment['type']) == 'Other') ? 'selected' : ''; ?>>Other</option>
                                                                                </select>
                                                                                <input type="hidden"
                                                                                    name="on_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][path]"
                                                                                    value="<?php echo htmlspecialchars(trim($attachment['path'])); ?>">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        <?php endif; ?>

                                                        <!-- Tüm dosyalar için ek seçim -->
                                                        <div class="all-files">
                                                            <small class="text-info fw-bold d-block mb-2">
                                                                <i class="fas fa-plus-circle me-1"></i>Ek Dosya Ekle:
                                                            </small>
                                                            <div class="attachment-group mb-2 p-2 border rounded">
                                                                <div class="row">
                                                                    <div class="col-12 mb-2">
                                                                        <label class="form-label small mb-1">Dosya:</label>
                                                                        <select class="form-select form-select-sm"
                                                                            name="on_signers[<?php echo $index; ?>][additional_attachments][0][filename]">
                                                                            <option value="">-- Dosya Seçin --</option>
                                                                            <?php foreach ($processed_attachments as $att_index => $attachment): ?>
                                                                                <option value="<?php echo htmlspecialchars(trim($attachment['filename'])); ?>"
                                                                                    data-path="<?php echo htmlspecialchars(trim($attachment['path'])); ?>">
                                                                                    <?php echo htmlspecialchars(trim($attachment['filename'])); ?>
                                                                                </option>
                                                                            <?php endforeach; ?>
                                                                        </select>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label small mb-1">Label:</label>
                                                                        <select class="form-select form-select-sm"
                                                                            name="on_signers[<?php echo $index; ?>][additional_attachments][0][type]">
                                                                            <option value="">-- Dosya Türü Seçin --</option>
                                                                            <option value="Passport">Passport</option>
                                                                            <option value="Seaman Book">Seaman Book</option>
                                                                            <option value="Medical Certificate">Medical Certificate</option>
                                                                            <option value="SID">SID</option>
                                                                            <option value="Other">Other</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>


                        <?php endif; ?>

                        <!-- OFF-SIGNERS Tablosu -->
                        <?php if (!empty($parsed_crew_data['off_signers'])): ?>
                            <div class="mb-4">
                                <h5 class="text-success">OFF-SIGNERS</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead class="table-success">
                                            <tr>
                                                <th>Ad Soyad</th>
                                                <th>Rütbe</th>
                                                <th>Pasaport</th>
                                                <th>Seaman Book</th>
                                                <th>Uyruk</th>
                                                <th>Doğum Tarihi</th>
                                                <?php if ($detected_format == 'format_2'): ?>
                                                    <th>İletişim</th>
                                                <?php endif; ?>
                                                <th>Ek Dosyalar</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($parsed_crew_data['off_signers'] as $index => $crew): ?>
                                                <tr>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="off_signers[<?php echo $index; ?>][name]"
                                                            value="<?php echo htmlspecialchars(trim($crew['name'])); ?>"
                                                            placeholder="Ad Soyad">
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="off_signers[<?php echo $index; ?>][firstname]"
                                                            value="<?php echo htmlspecialchars(trim($crew['firstname'])); ?>"
                                                            placeholder="Ad">
                                                        <input type="text" class="form-control form-control-sm mt-1"
                                                            name="off_signers[<?php echo $index; ?>][lastname]"
                                                            value="<?php echo htmlspecialchars(trim($crew['lastname'])); ?>"
                                                            placeholder="Soyad">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="off_signers[<?php echo $index; ?>][rank]"
                                                            value="<?php echo htmlspecialchars(trim($crew['rank'])); ?>"
                                                            placeholder="Rütbe">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="off_signers[<?php echo $index; ?>][passport][number]"
                                                            value="<?php echo htmlspecialchars(trim($crew['passport']['number'])); ?>"
                                                            placeholder="Pasaport No">
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="off_signers[<?php echo $index; ?>][passport][issued]"
                                                            value="<?php echo htmlspecialchars(trim($crew['passport']['issued'])); ?>"
                                                            placeholder="Veriliş Tarihi">
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="off_signers[<?php echo $index; ?>][passport][expiry]"
                                                            value="<?php echo htmlspecialchars(trim($crew['passport']['expiry'])); ?>"
                                                            placeholder="Bitiş Tarihi">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="off_signers[<?php echo $index; ?>][seaman_book][number]"
                                                            value="<?php echo htmlspecialchars(trim($crew['seaman_book']['number'])); ?>"
                                                            placeholder="Seaman Book No">
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="off_signers[<?php echo $index; ?>][seaman_book][issued]"
                                                            value="<?php echo htmlspecialchars(trim($crew['seaman_book']['issued'])); ?>"
                                                            placeholder="Veriliş Tarihi">
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="off_signers[<?php echo $index; ?>][seaman_book][expiry]"
                                                            value="<?php echo htmlspecialchars(trim($crew['seaman_book']['expiry'])); ?>"
                                                            placeholder="Bitiş Tarihi">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="off_signers[<?php echo $index; ?>][nationality]"
                                                            value="<?php echo htmlspecialchars(trim($crew['nationality'])); ?>"
                                                            placeholder="Uyruk">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm mb-1"
                                                            name="off_signers[<?php echo $index; ?>][date_of_birth]"
                                                            value="<?php echo htmlspecialchars(trim($crew['date_of_birth'])); ?>"
                                                            placeholder="Doğum Tarihi">
                                                        <input type="text" class="form-control form-control-sm"
                                                            name="off_signers[<?php echo $index; ?>][place_of_birth]"
                                                            value="<?php echo htmlspecialchars(trim($crew['place_of_birth'])); ?>"
                                                            placeholder="Doğum Yeri">
                                                    </td>
                                                    <?php if ($detected_format == 'format_2'): ?>
                                                        <td>
                                                            <input type="text" class="form-control form-control-sm mb-1"
                                                                name="off_signers[<?php echo $index; ?>][mobile_phone]"
                                                                value="<?php echo htmlspecialchars(trim($crew['mobile_phone'] ?? '')); ?>"
                                                                placeholder="Cep Telefonu">
                                                            <input type="email" class="form-control form-control-sm"
                                                                name="off_signers[<?php echo $index; ?>][email]"
                                                                value="<?php echo htmlspecialchars(trim($crew['email'] ?? '')); ?>"
                                                                placeholder="E-posta">
                                                        </td>
                                                    <?php endif; ?>
                                                    <td>
                                                        <div class="attachment-manager" data-crew-index="<?php echo $index; ?>" data-crew-type="off_signers">
                                                            <label class="form-label small fw-bold">Ek Dosyalar:</label>

                                                            <!-- Tespit edilen dosyalar -->
                                                            <?php if (!empty($crew['attachments'])): ?>
                                                                <div class="detected-files mb-3">
                                                                    <small class="text-success fw-bold d-block mb-2">
                                                                        <i class="fas fa-check-circle me-1"></i>Tespit Edilen Dosyalar:
                                                                    </small>
                                                                    <?php foreach ($crew['attachments'] as $att_index => $attachment): ?>
                                                                        <div class="attachment-group mb-3 p-2 border rounded bg-light">
                                                                            <div class="row">
                                                                                <div class="col-12 mb-2">
                                                                                    <label class="form-label small mb-1">Dosya:</label>
                                                                                    <select class="form-select form-select-sm"
                                                                                        name="off_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][filename]">
                                                                                        <option value="">-- Dosya Seçin --</option>
                                                                                        <?php foreach ($processed_attachments as $proc_att): ?>
                                                                                            <option value="<?php echo htmlspecialchars(trim($proc_att['filename'])); ?>"
                                                                                                data-path="<?php echo htmlspecialchars(trim($proc_att['path'])); ?>"
                                                                                                <?php echo (trim($proc_att['filename']) == trim($attachment['filename'])) ? 'selected' : ''; ?>>
                                                                                                <?php echo htmlspecialchars(trim($proc_att['filename'])); ?>
                                                                                            </option>
                                                                                        <?php endforeach; ?>
                                                                                    </select>
                                                                                </div>
                                                                                <div class="col-12">
                                                                                    <label class="form-label small mb-1">Label:</label>
                                                                                    <select class="form-select form-select-sm"
                                                                                        name="off_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][type]">
                                                                                        <option value="">-- Dosya Türü Seçin --</option>
                                                                                        <option value="Passport" <?php echo (trim($attachment['type']) == 'Passport') ? 'selected' : ''; ?>>Passport</option>
                                                                                        <option value="Seaman Book" <?php echo (trim($attachment['type']) == 'Seaman Book') ? 'selected' : ''; ?>>Seaman Book</option>
                                                                                        <option value="Medical Certificate" <?php echo (trim($attachment['type']) == 'Medical Certificate') ? 'selected' : ''; ?>>Medical Certificate</option>
                                                                                        <option value="SID" <?php echo (trim($attachment['type']) == 'SID') ? 'selected' : ''; ?>>SID</option>
                                                                                        <option value="Other" <?php echo (trim($attachment['type']) == 'Other') ? 'selected' : ''; ?>>Other</option>
                                                                                    </select>
                                                                                    <input type="hidden"
                                                                                        name="off_signers[<?php echo $index; ?>][attachments][<?php echo $att_index; ?>][path]"
                                                                                        value="<?php echo htmlspecialchars(trim($attachment['path'])); ?>">
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    <?php endforeach; ?>
                                                                </div>
                                                            <?php endif; ?>

                                                            <!-- Tüm dosyalar için ek seçim -->
                                                            <div class="all-files">
                                                                <small class="text-info fw-bold d-block mb-2">
                                                                    <i class="fas fa-plus-circle me-1"></i>Ek Dosya Ekle:
                                                                </small>
                                                                <div class="attachment-group mb-2 p-2 border rounded">
                                                                    <div class="row">
                                                                        <div class="col-12 mb-2">
                                                                            <label class="form-label small mb-1">Dosya:</label>
                                                                            <select class="form-select form-select-sm"
                                                                                name="off_signers[<?php echo $index; ?>][additional_attachments][0][filename]">
                                                                                <option value="">-- Dosya Seçin --</option>
                                                                                <?php foreach ($processed_attachments as $att_index => $attachment): ?>
                                                                                    <option value="<?php echo htmlspecialchars(trim($attachment['filename'])); ?>"
                                                                                        data-path="<?php echo htmlspecialchars(trim($attachment['path'])); ?>">
                                                                                        <?php echo htmlspecialchars(trim($attachment['filename'])); ?>
                                                                                    </option>
                                                                                <?php endforeach; ?>
                                                                            </select>
                                                                        </div>
                                                                        <div class="col-12">
                                                                            <label class="form-label small mb-1">Label:</label>
                                                                            <select class="form-select form-select-sm"
                                                                                name="off_signers[<?php echo $index; ?>][additional_attachments][0][type]">
                                                                                <option value="">-- Dosya Türü Seçin --</option>
                                                                                <option value="Passport">Passport</option>
                                                                                <option value="Seaman Book">Seaman Book</option>
                                                                                <option value="Medical Certificate">Medical Certificate</option>
                                                                                <option value="SID">SID</option>
                                                                                <option value="Other">Other</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Flight Details -->
                        <?php
                        if (1 == 2):
                            if (!empty($parsed_crew_data['on_signers_flight_details']) || !empty($parsed_crew_data['off_signers_flight_details'])): ?>
                                <div class="mb-4">
                                    <h5>Uçuş Detayları</h5>
                                    <?php if (!empty($parsed_crew_data['on_signers_flight_details'])): ?>
                                        <div class="mb-3">
                                            <h6 class="text-primary">ON-SIGNERS Uçuş Detayları</h6>
                                            <div class="border rounded p-3 bg-light">
                                                <pre><?php echo htmlspecialchars(trim($parsed_crew_data['on_signers_flight_details'])); ?></pre>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($parsed_crew_data['off_signers_flight_details'])): ?>
                                        <div class="mb-3">
                                            <h6 class="text-success">OFF-SIGNERS Uçuş Detayları</h6>
                                            <div class="border rounded p-3 bg-light">
                                                <pre><?php echo htmlspecialchars(trim($parsed_crew_data['off_signers_flight_details'])); ?></pre>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                <?php endif;
                    endif; ?>


                </div>
                <div class="card-footer d-flex justify-content-end">
                    <?php if ($parsed_crew_data): ?>
                        <button type="submit" name="export_crew_data" value="1" class="btn btn-success btn-evo-loading">
                            <i class="fas fa-download me-2"></i>Crew Verilerini Aktar
                        </button>
                    <?php endif; ?>
                </div>
        </div>
</form>