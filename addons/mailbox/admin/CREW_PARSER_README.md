# Crew Member Text Parser - Mürette<PERSON> <PERSON><PERSON>ırıcısı

B<PERSON> do<PERSON>ü<PERSON>, crew member veril<PERSON><PERSON> içeren metinleri ayrıştıran ve yapılandırılmış diziler oluşturan fonksiyonları açıklar.

## Ana Fonksiyon: `parse_crew_member_text()`

### Amaç

Bu fonksiyon crew member verilerini içeren metinleri ayrıştırır ve yapılandırılmış diziler oluşturur. **İki farklı text formatını otomatik olarak tespit eder** ve uygun parser'ı çağırır. Her crew member için Name, Passport, Seaman Book gibi bilgileri çıkarır ve ek dosyaları crew member'lara göre kategorize eder.

### Kullanım

```php
$parsed_data = parse_crew_member_text($text, $processed_attachments);
```

### Parametreler

- `$text` (string): Ayr<PERSON>ştırılaca<PERSON> metin
- `$processed_attachments` (array, opsiyonel): İşlenmiş ek dosyalar dizisi

### Dönen <PERSON>

```php
[
    'on_signers' => [],           // ON-SIGNERS listesi
    'off_signers' => [],          // OFF-SIGNERS listesi
    'on_signers_flight_details' => '',   // ON-SIGNERS uçuş detayları
    'off_signers_flight_details' => '',  // OFF-SIGNERS uçuş detayları
    'statistics' => [
        'on_signers_count' => 0,
        'off_signers_count' => 0,
        'total_attachments_matched' => 0
    ]
]
```

## Desteklenen Metin Formatları

Sistem **iki farklı formatı otomatik olarak tespit eder** ve uygun parser'ı çağırır:

### Format 1: ON-SIGNERS/OFF-SIGNERS Format

```
ON-SIGNERS: 7
****************
Name: ABANILLA,JOHN ALBERT ANSELMO     Rank: 2ND OFFICER
Passport:P0424228B   Issued:26/Jan/2019       Expiry: 25/Jan/2029
Seaman Book: C1369525          Issued:11/Feb/2019     Expiry: 11/Feb/2029
Nationality: Filipino      Date of Birth: 15/Feb/1992        Place of Birth: CEBU CITY

ON-SIGNERS FLIGHT DETAILS:
[Uçuş detayları...]

OFF-SIGNERS: 7
****************
Name: YOLA,GERALD XAVIER VILLAFLOR         Rank:2ND OFFICER
[Diğer crew member bilgileri...]
```

### Format 2: ONSIGNER/OFFSIGNER Format

```
ONSIGNER

Surname: MAJICA
Name: VOJISLAV
Rank: MASTER
Nationality: Croatian
DOB: 01.08.1968
POB: RIJEKA
Mobile Phone: +38598443816
Email: <EMAIL>
Passport No.:*********
Passport Issue Place:Rijeka
Passport Issue Date:07.11.2016
Passport Expiry Date:07.11.2026
Seaman's Book No.:00103934
Seaman's Book Issue Date:14.01.2021
Seaman's Book Expiry Date:14.01.2031

OFFSIGNER

Surname: MATEI
Name: RADU
[Diğer crew member bilgileri...]
```

## Crew Member Veri Yapısı

Her crew member için aşağıdaki veri yapısı oluşturulur:

```php
[
    'name' => '',                    // Tam isim
    'firstname' => '',               // Ad
    'lastname' => '',                // Soyad
    'rank' => '',                    // Rütbe
    'passport' => [
        'number' => '',              // Pasaport numarası
        'issued' => '',              // Veriliş tarihi
        'expiry' => ''               // Bitiş tarihi
    ],
    'seaman_book' => [
        'number' => '',              // Seaman book numarası
        'issued' => '',              // Veriliş tarihi
        'expiry' => ''               // Bitiş tarihi
    ],
    'nationality' => '',             // Uyruk
    'date_of_birth' => '',           // Doğum tarihi
    'place_of_birth' => '',          // Doğum yeri
    'mobile_phone' => '',            // Cep telefonu (Format 2)
    'email' => '',                   // E-posta (Format 2)
    'attachments' => []              // Eşleşen ek dosyalar
]

**Not:** Format 2'de passport ve seaman_book dizilerinde ek olarak `issue_place` alanı da bulunur.
```

## Ek Dosya Eşleştirme

Fonksiyon ek dosyaları crew member'lara otomatik olarak eşleştirir:

### Eşleştirme Kriterleri

- İsim varyasyonları (virgüllü, virgülsüz, boşluksuz, alt çizgili)
- Pasaport numarası
- Seaman book numarası

### Dosya Türü Belirleme

Dosya adına göre otomatik olarak kategorize edilir:

- **Passport**: "passport", "pasaport" içeren dosyalar
- **Seaman Book**: "seaman", "book" içeren dosyalar
- **Medical Certificate**: "medical", "health", "saglik" içeren dosyalar
- **SID**: "sid" içeren dosyalar
- **Other**: Diğer dosyalar

## Yardımcı Fonksiyonlar

### `detect_text_format($text)`

Text formatını otomatik olarak tespit eder. `format_1` veya `format_2` döner.

### `parse_crew_format_1($text, $processed_attachments)`

Format 1 (ON-SIGNERS/OFF-SIGNERS) için özel parser.

### `parse_crew_format_2($text, $processed_attachments)`

Format 2 (ONSIGNER/OFFSIGNER) için özel parser.

### `parse_single_crew_member($crew_data)`

Format 1 için tek bir crew member verisini ayrıştırır.

### `parse_single_crew_member_format_2($crew_data)`

Format 2 için tek bir crew member verisini ayrıştırır.

### `match_attachments_to_crew($crew_data, $processed_attachments)`

Ek dosyaları crew member'lara eşleştirir.

### `generate_name_variations($crew)`

İsim varyasyonları oluşturur.

### `determine_document_type($filename)`

Dosya türünü belirler.

## Kullanım Örnekleri

### Temel Kullanım

```php
$parsed_data = parse_crew_member_text($_SESSION['calisma']['selected_text']);
```

### Ek Dosyalarla Kullanım

```php
$parsed_data = parse_crew_member_text(
    $_SESSION['calisma']['selected_text'],
    $processed_attachments
);
```

### Sonuçlara Erişim

```php
// ON-SIGNERS listesi
foreach ($parsed_data['on_signers'] as $crew) {
    echo $crew['name'] . ' - ' . $crew['rank'] . "\n";
    echo 'Passport: ' . $crew['passport']['number'] . "\n";

    // Ek dosyalar
    foreach ($crew['attachments'] as $attachment) {
        echo $attachment['type'] . ': ' . $attachment['filename'] . "\n";
    }
}
```

## Test Dosyası

`test_crew_parser.php` dosyası ile fonksiyonları test edebilirsiniz.

## Entegrasyon

Fonksiyon `tomove.php` dosyasına entegre edilmiştir ve otomatik olarak:

- Crew member metni tespit eder
- Verileri ayrıştırır
- Ek dosyaları eşleştirir
- Sonuçları tablo formatında gösterir

## Özellikler

- ✅ **İki farklı metin formatı desteği** (Otomatik tespit)
  - Format 1: ON-SIGNERS/OFF-SIGNERS (Orijinal format)
  - Format 2: ONSIGNER/OFFSIGNER (Yeni format)
- ✅ Otomatik ek dosya eşleştirme
- ✅ İsim varyasyonu desteği
- ✅ Dosya türü otomatik belirleme
- ✅ Hata toleransı
- ✅ Türkçe karakter desteği
- ✅ Bootstrap tablo görünümü
- ✅ Responsive tasarım
- ✅ **Format 2 ek özellikleri:**
  - Cep telefonu bilgisi
  - E-posta adresi
  - Pasaport/Seaman Book veriliş yeri
  - Gelişmiş veri ayrıştırma

## Format Karşılaştırması

| Özellik        | Format 1                 | Format 2            |
| -------------- | ------------------------ | ------------------- |
| Başlık         | ON-SIGNERS:/OFF-SIGNERS: | ONSIGNER/OFFSIGNER  |
| İsim Formatı   | Name: SURNAME,FIRSTNAME  | Surname:/Name:      |
| Cep Telefonu   | ❌                       | ✅                  |
| E-posta        | ❌                       | ✅                  |
| Veriliş Yeri   | ❌                       | ✅                  |
| Uçuş Detayları | ✅                       | ❌                  |
| Sayı Bilgisi   | ✅ (ON-SIGNERS: 7)       | ❌ (Otomatik sayım) |
