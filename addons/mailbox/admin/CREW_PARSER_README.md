# Crew Member Text Parser - Mürette<PERSON> <PERSON><PERSON>ırıcısı

B<PERSON> dokü<PERSON>, crew member veril<PERSON>ni içeren metinleri ayrıştıran ve yapılandırılmış diziler oluşturan fonksiyonları açıklar.

## Ana Fonksiyon: `parse_crew_member_text()`

### Amaç
Bu fonksiyon crew member verilerini içeren metinleri ayrıştırır ve yapılandırılmış diziler oluşturur. ON-SIGNERS, OFF-SIGNERS ve FLIGHT DETAILS b<PERSON><PERSON><PERSON><PERSON><PERSON> ayırır, her crew member i<PERSON><PERSON> <PERSON>, Passport, Seaman Book gibi bilgileri çıkarır ve ek dosyaları crew member'lara göre kategorize eder.

### Kullanım
```php
$parsed_data = parse_crew_member_text($text, $processed_attachments);
```

### Parametreler
- `$text` (string): Ayrıştırılacak metin
- `$processed_attachments` (array, opsiyonel): İşlenmiş ek dosyalar dizisi

### Dönen <PERSON>er
```php
[
    'on_signers' => [],           // ON-SIGNERS listesi
    'off_signers' => [],          // OFF-SIGNERS listesi
    'on_signers_flight_details' => '',   // ON-SIGNERS uçuş detayları
    'off_signers_flight_details' => '',  // OFF-SIGNERS uçuş detayları
    'statistics' => [
        'on_signers_count' => 0,
        'off_signers_count' => 0,
        'total_attachments_matched' => 0
    ]
]
```

## Desteklenen Metin Formatı

Fonksiyon aşağıdaki format ile çalışır:

```
ON-SIGNERS: 7
****************
Name: ABANILLA,JOHN ALBERT ANSELMO     Rank: 2ND OFFICER
Passport:P0424228B   Issued:26/Jan/2019       Expiry: 25/Jan/2029      
Seaman Book: C1369525          Issued:11/Feb/2019     Expiry: 11/Feb/2029
Nationality: Filipino      Date of Birth: 15/Feb/1992        Place of Birth: CEBU CITY

ON-SIGNERS FLIGHT DETAILS:
[Uçuş detayları...]

OFF-SIGNERS: 7
****************
Name: YOLA,GERALD XAVIER VILLAFLOR         Rank:2ND OFFICER
[Diğer crew member bilgileri...]
```

## Crew Member Veri Yapısı

Her crew member için aşağıdaki veri yapısı oluşturulur:

```php
[
    'name' => '',                    // Tam isim
    'firstname' => '',               // Ad
    'lastname' => '',                // Soyad
    'rank' => '',                    // Rütbe
    'passport' => [
        'number' => '',              // Pasaport numarası
        'issued' => '',              // Veriliş tarihi
        'expiry' => ''               // Bitiş tarihi
    ],
    'seaman_book' => [
        'number' => '',              // Seaman book numarası
        'issued' => '',              // Veriliş tarihi
        'expiry' => ''               // Bitiş tarihi
    ],
    'nationality' => '',             // Uyruk
    'date_of_birth' => '',           // Doğum tarihi
    'place_of_birth' => '',          // Doğum yeri
    'attachments' => []              // Eşleşen ek dosyalar
]
```

## Ek Dosya Eşleştirme

Fonksiyon ek dosyaları crew member'lara otomatik olarak eşleştirir:

### Eşleştirme Kriterleri
- İsim varyasyonları (virgüllü, virgülsüz, boşluksuz, alt çizgili)
- Pasaport numarası
- Seaman book numarası

### Dosya Türü Belirleme
Dosya adına göre otomatik olarak kategorize edilir:
- **Passport**: "passport", "pasaport" içeren dosyalar
- **Seaman Book**: "seaman", "book" içeren dosyalar
- **Medical Certificate**: "medical", "health", "saglik" içeren dosyalar
- **SID**: "sid" içeren dosyalar
- **Other**: Diğer dosyalar

## Yardımcı Fonksiyonlar

### `parse_single_crew_member($crew_data)`
Tek bir crew member verisini ayrıştırır.

### `match_attachments_to_crew($crew_data, $processed_attachments)`
Ek dosyaları crew member'lara eşleştirir.

### `generate_name_variations($crew)`
İsim varyasyonları oluşturur.

### `determine_document_type($filename)`
Dosya türünü belirler.

## Kullanım Örnekleri

### Temel Kullanım
```php
$parsed_data = parse_crew_member_text($_SESSION['calisma']['selected_text']);
```

### Ek Dosyalarla Kullanım
```php
$parsed_data = parse_crew_member_text(
    $_SESSION['calisma']['selected_text'], 
    $processed_attachments
);
```

### Sonuçlara Erişim
```php
// ON-SIGNERS listesi
foreach ($parsed_data['on_signers'] as $crew) {
    echo $crew['name'] . ' - ' . $crew['rank'] . "\n";
    echo 'Passport: ' . $crew['passport']['number'] . "\n";
    
    // Ek dosyalar
    foreach ($crew['attachments'] as $attachment) {
        echo $attachment['type'] . ': ' . $attachment['filename'] . "\n";
    }
}
```

## Test Dosyası

`test_crew_parser.php` dosyası ile fonksiyonları test edebilirsiniz.

## Entegrasyon

Fonksiyon `tomove.php` dosyasına entegre edilmiştir ve otomatik olarak:
- Crew member metni tespit eder
- Verileri ayrıştırır
- Ek dosyaları eşleştirir
- Sonuçları tablo formatında gösterir

## Özellikler

- ✅ Çoklu metin formatı desteği
- ✅ Otomatik ek dosya eşleştirme
- ✅ İsim varyasyonu desteği
- ✅ Dosya türü otomatik belirleme
- ✅ Hata toleransı
- ✅ Türkçe karakter desteği
- ✅ Bootstrap tablo görünümü
- ✅ Responsive tasarım
