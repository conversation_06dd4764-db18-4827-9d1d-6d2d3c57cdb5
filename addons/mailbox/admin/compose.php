<?php
// Taslak düzenleme kontrolü
$draft = null;
if (isset($_GET['draft_id'])) {
    $draft = get_draft((int) $_GET['draft_id']);
    if (!$draft) {
        fn_set_notice('Taslak bulunamadı.', 'E');
        fn_redirect(fn_menu_link('mailbox/drafts', 1));
    }
}

// Kullanıcının imzalarını al
$signatures = get_signatures();
$default_signature = get_default_signature();

// Hazır mesajları al
$templates = get_templates();

// Mail hesaplarını al
global $mailbox_config;

// Mail gönderme işlemi
if ($_POST) {
    try {
        // Form verilerini kontrol et
        if (empty($_POST['to']) || empty($_POST['subject']) || empty($_POST['body'])) {
            throw new Exception('Lütfen tüm alanları doldurun.');
        }

        // Email formatını kontrol et
        if (!filter_var($_POST['to'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Geçersiz email adresi.');
        }

        // CC ve BCC adreslerini kontrol et
        $cc_array = array();
        if (!empty($_POST['cc'])) {
            $cc_addresses = array_map('trim', explode(',', $_POST['cc']));
            foreach ($cc_addresses as $cc_address) {
                if (!empty($cc_address) && !filter_var($cc_address, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Geçersiz CC email adresi: ' . $cc_address);
                }
                if (!empty($cc_address)) {
                    $cc_array[] = $cc_address;
                }
            }
        }

        $bcc_array = array();
        if (!empty($_POST['bcc'])) {
            $bcc_addresses = array_map('trim', explode(',', $_POST['bcc']));
            foreach ($bcc_addresses as $bcc_address) {
                if (!empty($bcc_address) && !filter_var($bcc_address, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Geçersiz BCC email adresi: ' . $bcc_address);
                }
                if (!empty($bcc_address)) {
                    $bcc_array[] = $bcc_address;
                }
            }
        }

        // Ekleri hazırla
        $attachments = array();
        if (!empty($_FILES['attachments']['name'][0])) {
            foreach ($_FILES['attachments']['name'] as $key => $name) {
                if ($_FILES['attachments']['error'][$key] === UPLOAD_ERR_OK) {
                    $attachments[] = array(
                        'name' => $name,
                        'tmp_name' => $_FILES['attachments']['tmp_name'][$key]
                    );
                }
            }
        }

        if (isset($_POST['draft_id']) && $draft && !empty($draft->attachments)) {
            $draft_attachments = json_decode($draft->attachments);
            foreach ($draft_attachments as $attachment) {
                if (file_exists($attachment->path)) {
                    $attachments[] = array(
                        'name' => $attachment->filename,
                        'tmp_name' => $attachment->path
                    );
                }
            }
        }

        // İmza ID'sini al
        $signature_id = isset($_POST['signature_id']) ? (int) $_POST['signature_id'] : null;

        // Hesap ID'sini al
        $account_id = isset($_POST['account_id']) && !empty($_POST['account_id']) ? (int) $_POST['account_id'] : null;

        // Maili gönder
        if (send_email($_POST['to'], $_POST['subject'], $_POST['body'], $attachments, $signature_id, $cc_array, $bcc_array, $account_id)) {
            // Eğer taslaktan gönderildiyse taslağı sil
            if (isset($_POST['draft_id'])) {
                delete_draft((int) $_POST['draft_id']);
            }
            fn_set_notice('Mail başarıyla gönderildi.', 'S');
            fn_redirect(fn_menu_link('mailbox/outbox', 1));
        } else {
            throw new Exception('Mail gönderilirken bir hata oluştu.');
        }
    } catch (Exception $e) {
        fn_set_notice($e->getMessage(), 'E');
    }
}
?>
<div class="d-flex flex-column flex-lg-row">
    <?php
    // Aside menüyü dahil et
    include(dirname(__FILE__) . '/includes/mailbox_aside.php');
    ?>
    <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
        <form action="" class="form valid_form" method="post" enctype="multipart/form-data" id="compose-form">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Yeni Mail</h3>
                    <div class="card-toolbar">
                        <div class="d-flex align-items-center">
                            <label class="form-text w-150px text-end">Gönderen Hesap</label>
                            <div class="w-200px ms-2">
                                <select class="form-select form-select-sm form-select-solid" data-control="select2" data-placeholder="Gönderen Hesap" data-hide-search="true" name="account_id">
                                    <option></option>
                                    <option value="default" <?php echo ($_SESSION['active_mail_account'] === 'default') ? 'selected' : ''; ?>><?php echo $mailbox_config['default']['display_name']; ?> (<?php echo $mailbox_config['default']['username']; ?>)</option>
                                    <?php foreach ($mailbox_config['accounts'] as $account): ?>
                                        <option value="<?php echo $account['id']; ?>" <?php echo ($_SESSION['active_mail_account'] == $account['id']) ? 'selected' : ''; ?>>
                                            <?php echo isset($account['display_name']) ? $account['display_name'] . ' (' . $account['username'] . ')' : $account['username']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($draft): ?>
                    <input type="hidden" name="draft_id" value="<?php echo $draft->id; ?>">
                <?php endif; ?>
                <div class="card-body">
                    <div class="mb-5">
                        <label class="form-label">Alıcı</label>
                        <input type="email" placeholder="Örnek: <EMAIL>, <EMAIL>" class="form-control" name="to" required
                            value="<?php echo $draft ? $draft->to_email : ''; ?>">
                    </div>
                    <div class="mb-5">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">CC:</label>
                            <div class="d-flex">
                                <a href="#" class="text-muted text-hover-primary me-4" id="toggleCc">CC Gizle</a>
                                <a href="#" class="text-muted text-hover-primary" id="toggleBcc">
                                    <?php if ($draft && !empty($draft->bcc)): ?>BCC Gizle<?php else: ?>BCC Ekle<?php endif; ?>
                                </a>
                            </div>
                        </div>
                        <div id="ccField" class="">
                            <input type="text" class="form-control" name="cc" value="<?php echo $draft ? $draft->cc : ''; ?>" placeholder="">
                        </div>
                    </div>
                    <div class="mb-5 <?php echo ($draft && !empty($draft->bcc)) ? '' : 'd-none'; ?>" id="bccField">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">BCC:</label>
                        </div>
                        <div>
                            <input type="text" class="form-control" name="bcc" value="<?php echo $draft ? $draft->bcc : ''; ?>" placeholder="Örnek: <EMAIL>, <EMAIL>">
                        </div>
                    </div>

                    <div class="mb-5">
                        <label class="form-label">Konu</label>
                        <input type="text" class="form-control" name="subject" required
                            value="<?php echo $draft ? $draft->subject : ''; ?>">
                    </div>

                    <div class="mb-5">
                        <label class="form-label">İçerik</label>
                        <textarea class="form-control ckeditor_mailbox border-1" id="body" name="body" rows="10"><?php echo $draft ? $draft->body : ''; ?></textarea>
                    </div>

                    <div class="row ">

                        <div class="col-md-4">
                            <label class="form-label">İmza Seç</label>
                            <select class="form-select " data-control="select2" data-placeholder="İmzalar" data-hide-search="true" name="signature_id">
                                <option value="">Seçiniz (Varsayılan kullanılacak)</option>
                                <?php foreach ($signatures as $signature): ?>
                                    <option value="<?php echo $signature->id; ?>" <?php echo ($default_signature && $default_signature->id == $signature->id) ? 'selected' : ''; ?>>
                                        <?php echo $signature->name; ?> <?php echo $signature->is_default ? '(Varsayılan)' : ''; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (empty($signatures)): ?>
                                <div class="form-text text-muted">
                                    Henüz hiç imza eklenmemiş. <a href="<?php fn_menu_link('mailbox/signatures'); ?>">Buradan</a> yeni imza ekleyebilirsiniz.
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Hazır Mesaj Seç</label>
                            <select class="form-select " data-control="select2" data-placeholder="İmzalar" data-hide-search="true" id="template_select">
                                <option value="">Seçiniz</option>
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?php echo $template->id; ?>"><?php echo $template->name; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (empty($templates)): ?>
                                <div class="form-text text-muted">
                                    Henüz hiç hazır mesaj eklenmemiş. <a href="<?php fn_menu_link('mailbox/templates'); ?>">Buradan</a> yeni hazır mesaj ekleyebilirsiniz.
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Ekler</label>
                            <input type="file" class="form-control " name="attachments[]" multiple>
                            <?php if ($draft && !empty($draft->attachments)): ?>
                                <div class="mt-2">
                                    <p class="text-muted">Mevcut Ekler:</p>
                                    <?php foreach (json_decode($draft->attachments) as $key => $attachment): ?>
                                        <div class="d-flex align-items-center gap-2 mb-2">
                                            <i class="ki-duotone ki-file fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                            <span><?php echo $attachment->filename; ?></span>
                                            <button type="button" class="btn btn-sm btn-icon btn-light-danger"
                                                onclick="deleteAttachment(<?php echo $draft->id; ?>, <?php echo $key; ?>)">
                                                <i class="ki-duotone ki-trash fs-2">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                    <span class="path4"></span>
                                                    <span class="path5"></span>
                                                </i>
                                            </button>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>


                </div>

                <div class="card-footer d-flex justify-content-between">
                    <button type="button" class="btn btn-light" onclick="saveDraft()">Taslak Kaydet</button>
                    <button type="submit" class="btn btn-primary btn-evo-loading" data-defaul-text="Gönder!">Gönder</button>
                </div>

            </div>
        </form>
    </div>
</div>