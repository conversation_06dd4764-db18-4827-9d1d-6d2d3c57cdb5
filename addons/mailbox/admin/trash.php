<?php
try {
    // Mail kalıcı olarak silme işlemi
    if ($_POST && isset($_POST['delete_email'])) {
        try {
            // Aktif hesap ID'sini al
            $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

            $mailbox = mailbox_connect($account_id);
            $email_id = (int) $_POST['email_id'];

            // Çöp kutusuna bağlan
            $result = get_trash_emails($mailbox, 1, 1, $account_id);

            if (isset($result['mailbox'])) {
                $trash_mailbox = $result['mailbox'];
            } else {
                $trash_mailbox = $mailbox;
            }

            if (delete_email($trash_mailbox, $email_id, true)) {
                fn_set_notice('Mail kalıcı olarak silindi.', 'S');
            }

            // Bağlantıyı kapat
            if (is_resource($trash_mailbox) || (is_object($trash_mailbox) && $trash_mailbox instanceof \IMAP\Connection)) {
                @imap_close($trash_mailbox);
            }

            // Eğer $mailbox ve $trash_mailbox farklı nesnelerse ve $mailbox hala açıksa kapat
            if ($mailbox !== $trash_mailbox) {
                // Bağlantıyı güvenli bir şekilde kapatmaya çalış
                try {
                    @imap_close($mailbox);
                } catch (\Throwable $e) {
                    // Bağlantı zaten kapalı olabilir, hatayı yok say
                    error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
                }
            }
        } catch (Exception $e) {
            fn_set_notice($e->getMessage(), 'E');
        }
        fn_redirect(fn_menu_link('mailbox/trash', 1));
    }

    // Çöp kutusunu boşaltma işlemi
    if ($_POST && isset($_POST['delete_all_emails'])) {
        try {
            // Aktif hesap ID'sini al
            $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

            $mailbox = mailbox_connect($account_id);
            if (empty_trash($mailbox, $account_id)) {
                fn_set_notice('Çöp kutusu başarıyla boşaltıldı.', 'S');
            }
        } catch (Exception $e) {
            fn_set_notice($e->getMessage(), 'E');
        }
        fn_redirect(fn_menu_link('mailbox/trash', 1));
    }

    // Aktif hesap ID'sini al
    $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

    // Mail listesini al (fonksiyon kendi içinde bağlantı açıp kapatacak)
    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
    $result = get_trash_emails(null, $page, 20, $account_id);

    // mailbox anahtarını kaldır (artık kullanmıyoruz)
    if (isset($result['mailbox'])) {
        unset($result['mailbox']);
    }
} catch (Exception $e) {
    fn_set_notice($e->getMessage(), 'E');
}
?>

<!--begin::Content-->
<div class="d-flex flex-column flex-lg-row">
    <?php
    // Aktif sayfa bilgisini ayarla
    $current_page = 'trash';

    // Aside menüyü dahil et
    include(dirname(__FILE__) . '/includes/mailbox_aside.php');
    ?>

    <!--begin::Content-->
    <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
        <!--begin::Card-->
        <div class="card">
            <div class="card-header align-items-center ps-5 py-5 gap-5">
                <!--begin::Actions-->
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative">
                        <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        <input type="text" id="mailbox-search" class="form-control form-control-sm form-control-solid mw-100 min-w-125px min-w-lg-300px ps-11" placeholder="Mail Ara...">
                    </div>
                    <!--end::Search-->

                    <?php if (!empty($result['emails'])): ?>
                        <!--begin::Empty Trash-->
                        <form method="post" class="d-inline" id="delete-all-form" onsubmit="return confirm('Çöp kutusundaki tüm mailleri kalıcı olarak silmek istediğinize emin misiniz? Bu işlem geri alınamaz!')">
                            <input type="hidden" name="delete_all_emails" value="1">
                            <button type="submit" class="btn btn-sm btn-danger me-2">
                                <i class="ki-duotone ki-trash fs-2 me-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                    <span class="path4"></span>
                                    <span class="path5"></span>
                                </i>
                                Çöp Kutusunu Boşalt
                            </button>
                        </form>
                        <!--end::Empty Trash-->
                        <?php if (1 == 2): ?>
                            <!--begin::Update Trash Labels-->
                            <a href="<?php echo fn_menu_link('mailbox/update_trash_labels'); ?>" class="btn btn-sm btn-light-primary me-2">
                                <i class="ki-duotone ki-tag fs-2 me-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                Etiketleri Güncelle
                            </a>
                        <?php endif; ?>
                        <!--end::Update Trash Labels-->
                    <?php endif; ?>
                </div>
                <!--end::Actions-->

                <!--begin::Pagination-->
                <?php if ($result['pages'] > 1): ?>
                    <div class="d-flex align-items-center">
                        <span class="fs-7 fw-semibold text-muted me-2"><?php echo ($page - 1) * 20 + 1; ?>-<?php echo min($page * 20, $result['total']); ?> / <?php echo $result['total']; ?></span>
                        <a href="<?php echo fn_menu_link('mailbox/trash&page=' . max(1, $page - 1)); ?>" class="btn btn-sm btn-icon btn-light me-2 <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <i class="ki-duotone ki-left fs-2 text-gray-500">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </a>
                        <a href="<?php echo fn_menu_link('mailbox/trash&page=' . min($result['pages'], $page + 1)); ?>" class="btn btn-sm btn-icon btn-light <?php echo $page >= $result['pages'] ? 'disabled' : ''; ?>">
                            <i class="ki-duotone ki-right fs-2 text-gray-500">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </a>
                    </div>
                <?php endif; ?>
                <!--end::Pagination-->
            </div>

            <div class="card-body p-0">
                <!--begin::Table-->
                <?php if (empty($result['emails'])): ?>
                    <div class="text-center py-10">
                        <i class="ki-duotone ki-trash fs-3x text-gray-400 mb-5">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                            <span class="path4"></span>
                            <span class="path5"></span>
                        </i>
                        <div class="text-gray-600 fw-semibold fs-4 mb-3">Çöp kutunuzda hiç mail bulunmuyor.</div>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table align-middle table-hover table-row-dashed table-sm">
                            <thead>
                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-35px ps-9">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" id="select-all-emails">
                                        </div>
                                    </th>
                                    <th>Gönderen</th>
                                    <th>Konu</th>
                                    <th class="text-end">Tarih</th>
                                    <th class="text-end pe-2">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($result['emails'] as $email):
                                ?>
                                    <tr <?php echo $email['seen'] ? '' : 'class="fw-bold"'; ?>>
                                        <!--begin::Checkbox-->
                                        <td class="ps-9">
                                            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                <input class="form-check-input email-checkbox" type="checkbox" data-email-id="<?php echo $email['id']; ?>">
                                            </div>
                                        </td>
                                        <!--end::Checkbox-->

                                        <!--begin::Sender-->
                                        <td class="w-150px">
                                            <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=trash'); ?>" class="d-flex align-items-center text-dark">
                                                <div class="symbol symbol-35px me-3">
                                                    <span class="symbol-label bg-light-danger text-danger fw-bold"><?php echo strtoupper(substr($email['from'], 0, 1)); ?></span>
                                                </div>
                                                <span class="fw-semibold text-gray-600"><?php echo $email['from']; ?></span>
                                            </a>
                                        </td>
                                        <!--end::Sender-->

                                        <!--begin::Title-->
                                        <td>
                                            <div class="text-dark mb-1">
                                                <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=trash'); ?>" class="text-dark">
                                                    <span class="fw-bold"><?php echo $email['subject']; ?></span>
                                                </a>
                                            </div>
                                            <?php
                                            // Mail etiketlerini getir
                                            $email_labels = get_email_labels($email['id'] ?? 0, $account_id, 'trash');
                                            if (is_array($email_labels) && !empty($email_labels)):
                                            ?>
                                                <div class="d-flex flex-wrap gap-1 mt-1">
                                                    <?php foreach ($email_labels as $label): ?>
                                                        <span class="badge" style="background-color: <?php echo $label->color; ?>; color: <?php echo get_contrast_color($label->color); ?>">
                                                            <?php echo $label->name; ?>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <!--end::Title-->

                                        <!--begin::Date-->
                                        <td class="text-end">
                                            <span class="fw-semibold"><?php echo $email['date']; ?></span>
                                        </td>
                                        <!--end::Date-->

                                        <!--begin::Actions-->
                                        <td class="w-100px text-end pe-2">
                                            <div class="d-flex justify-content-end">
                                                <a href="<?php echo fn_menu_link('mailbox/view&id=' . $email['id'] . '&folder=trash'); ?>" class="btn btn-sm btn-icon btn-light btn-active-light-primary me-2" data-bs-toggle="tooltip" title="Görüntüle">
                                                    <i class="ki-duotone ki-eye fs-2">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                        <span class="path3"></span>
                                                        <span class="path4"></span>
                                                    </i>
                                                </a>

                                                <form method="post" class="d-inline delete-form" onsubmit="return confirm('Bu maili kalıcı olarak silmek istediğinize emin misiniz?');">
                                                    <input type="hidden" name="email_id" value="<?php echo $email['id']; ?>">
                                                    <input type="hidden" name="delete_email" value="1">
                                                    <button type="submit" class="btn btn-sm btn-icon btn-light btn-active-danger" data-bs-toggle="tooltip" title="Kalıcı Olarak Sil">
                                                        <i class="ki-duotone ki-trash fs-2">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                            <span class="path3"></span>
                                                            <span class="path4"></span>
                                                            <span class="path5"></span>
                                                        </i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                        <!--end::Actions-->
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
                <!--end::Table-->
            </div>
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content-->
</div>
<!--end::Content-->

<script>
    // Mail arama fonksiyonu
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('mailbox-search');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const subject = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                    const sender = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

                    if (subject.includes(searchText) || sender.includes(searchText)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // Checkbox işlemleri
        const selectAllCheckbox = document.getElementById('select-all-emails');
        const emailCheckboxes = document.querySelectorAll('.email-checkbox');

        // Tümünü seç checkbox'u
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;

                emailCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });
        }

        // Tek tek checkbox'lar
        emailCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                let allSelected = true;

                emailCheckboxes.forEach(cb => {
                    if (!cb.checked) allSelected = false;
                });

                // Tümünü seç checkbox'unu güncelle
                if (selectAllCheckbox) selectAllCheckbox.checked = allSelected && emailCheckboxes.length > 0;
            });
        });

        // Bootstrap tooltip'lerini etkinleştir
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    });
</script>