<?php
// AJAX isteklerini işleyen dosya
header('Content-Type: application/json');

// Debug için gelen istekleri logla
error_log("AJAX İsteği: " . print_r($_POST, true));

// AJAX isteği kontrolü
if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
    echo json_encode(['error' => 'Geçersiz istek']);
    exit;
}

// Sistem otomatik olarak func.php ve init.php dosyalarını yüklüyor

// Site dizini ve URL'sini tanımla
if (!defined('SITE_DIR')) {
    define('SITE_DIR', realpath(dirname(__FILE__) . '/../../../'));
}

if (!defined('SITE_URL')) {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $domainName = $_SERVER['HTTP_HOST'];
    define('SITE_URL', $protocol . $domainName);
}

// Kullanıcı kontrolü
if (!USER_LOGIN) {
    echo json_encode(['error' => 'Oturum açmanız gerekiyor']);
    exit;
}

// İşlem türünü al
$action = isset($_POST['action']) ? $_POST['action'] : '';

// İşleme göre yönlendir
switch ($action) {
    case 'get_template':
        // Hazır mesaj ID'sini al
        $template_id = isset($_POST['template_id']) ? (int) $_POST['template_id'] : 0;

        // Hazır mesajı getir
        $template = get_template($template_id);

        if ($template) {
            // HTML içeriğini düz metne çevir
            $content = strip_tags($template->content);
            echo json_encode([
                'success' => true,
                'content' => nl2br($content)
            ]);
        } else {
            echo json_encode([
                'error' => 'Hazır mesaj bulunamadı'
            ]);
        }
        break;

    case 'upload_image':
        // Resim yükleme işlemi
        if (!isset($_FILES['upload']) || $_FILES['upload']['error'] != 0) {
            echo json_encode([
                'success' => false,
                'error' => 'Resim yüklenirken hata oluştu: ' . ($_FILES['upload']['error'] ?? 'Bilinmeyen hata')
            ]);
            break;
        }

        // Dosya bilgilerini al
        $file = $_FILES['upload'];
        $filename = $file['name'];
        // Dosya adının UTF-8 kodlamasını kontrol et ve düzelt
        if (!mb_check_encoding($filename, 'UTF-8')) {
            $filename = mb_convert_encoding($filename, 'UTF-8');
        }
        $tmp_name = $file['tmp_name'];
        $file_size = $file['size'];
        $file_type = $file['type'];

        // Dosya türü kontrolü
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
        if (!in_array($file_type, $allowed_types)) {
            echo json_encode([
                'success' => false,
                'error' => 'Sadece JPEG, PNG ve GIF dosyaları yüklenebilir.'
            ]);
            break;
        }

        // Dosya boyutu kontrolü (5MB)
        if ($file_size > 5 * 1024 * 1024) {
            echo json_encode([
                'success' => false,
                'error' => 'Dosya boyutu 5MB\'dan büyük olamaz.'
            ]);
            break;
        }

        // Dosya adını benzersiz ve güvenli hale getir
        // Sadece dosya sisteminde sorun yaratabilecek karakterleri değiştir
        // Türkçe karakterler, boşluklar ve diğer özel karakterler korunacak
        $invalid_chars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|', '&', '+', ',', ';', '='];
        $safe_filename = str_replace($invalid_chars, '_', $filename);

        // Dosya adının UTF-8 kodlamasını koruyarak benzersiz hale getir
        $new_filename = uniqid() . '_' . $safe_filename;

        // Seçili hesabı belirle
        $account_id = isset($_POST['account_id']) ? preg_replace('/[^a-zA-Z0-9_-]/', '', $_POST['account_id']) : 'default';

        // Yükleme klasörü - seçili hesaba göre özelleştir
        $upload_dir = SITE_DIR . '/uploads/mailbox/' . $account_id . '/gonderilen_mail_dosyalari/';

        // Klasör yoksa oluştur
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Dosyayı taşı
        $upload_path = $upload_dir . $new_filename;
        if (move_uploaded_file($tmp_name, $upload_path)) {
            // Başarılı yükleme
            $url = SITE_URL . '/gp/uploads/mailbox/' . $account_id . '/gonderilen_mail_dosyalari/' . $new_filename;
            echo json_encode([
                'success' => true,
                'url' => $url
            ]);
        } else {
            // Yükleme hatası
            echo json_encode([
                'success' => false,
                'error' => 'Dosya kaydedilirken hata oluştu.'
            ]);
        }
        break;

    case 'update_email_labels':
        // Mail etiketlerini güncelleme işlemi
        $email_id = (int) $_POST['email_id'];
        $folder = $_POST['folder'];
        $label_ids = isset($_POST['label_ids']) ? $_POST['label_ids'] : [];

        // Hesap ID'sini belirle
        $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : 'default';

        // Önce bu mail için tüm etiket ilişkilerini sil
        $db->query("DELETE FROM mail_label_relations WHERE user_id = " . (int) $user->id . " AND email_id = " . $email_id . " AND account_id = '" . $db->escape($account_id) . "' AND folder = '" . $db->escape($folder) . "'");

        // Seçilen etiketleri ekle
        foreach ($label_ids as $label_id) {
            add_label_to_email((int) $label_id, $email_id, $account_id, $folder);
        }

        echo json_encode([
            'success' => true,
            'message' => 'Mail etiketleri başarıyla güncellendi.'
        ]);
        break;

    case 'search_emails':
        // Mail arama işlemi
        $search_text = isset($_POST['search_text']) ? $_POST['search_text'] : '';
        $folder = isset($_POST['folder']) ? $_POST['folder'] : 'INBOX';

        // Hesap ID'sini belirle
        $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : 'default';

        // Arama yap
        $result = search_emails($search_text, $folder, $account_id);

        echo json_encode([
            'success' => true,
            'result' => $result
        ]);
        break;

    default:
        echo json_encode(['error' => 'Geçersiz işlem']);
        break;
}
