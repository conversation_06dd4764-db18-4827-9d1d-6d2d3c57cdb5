<?php
// Aktif hesap ID'sini al
$account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

try {
    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
    $result = get_drafts($page, 20, $account_id);
} catch (Exception $e) {
    fn_set_notice($e->getMessage(), 'E');
}

// Taslak silme işlemi
if ($_POST && isset($_POST['delete_draft'])) {
    $draft_id = (int) $_POST['draft_id'];
    if (delete_draft($draft_id)) {
        fn_set_notice('Taslak başarıyla silindi.', 'S');
    } else {
        fn_set_notice('Taslak silinirken bir hata oluştu.', 'E');
    }
    fn_redirect(fn_menu_link('mailbox/drafts', 1));
}
?>

<!--begin::Content-->
<div class="d-flex flex-column flex-lg-row">
    <?php
    // Aktif sayfa bilgisini ayarla
    $current_page = 'drafts';

    // Aktif hesap ID'sini al
    $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : null;

    // Aside menüyü dahil et
    include(dirname(__FILE__) . '/includes/mailbox_aside.php');
    ?>

    <!--begin::Content-->
    <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
        <!--begin::Card-->
        <div class="card">
            <div class="card-header align-items-center ps-5 py-5 gap-5">
                <!--begin::Actions-->
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative">
                        <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        <input type="text" id="mailbox-search" class="form-control form-control-sm form-control-solid mw-100 min-w-125px min-w-lg-300px ps-11" placeholder="Mail Ara...">
                    </div>
                    <!--end::Search-->
                </div>
                <!--end::Actions-->
            </div>

            <div class="card-body p-0">
                <!--begin::Table-->
                <?php if (empty($result['drafts'])): ?>
                    <div class="text-center py-10">
                        <i class="ki-duotone ki-document fs-3x text-gray-400 mb-5">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        <div class="text-gray-600 fw-semibold fs-4 mb-3">Henüz kaydedilmiş taslak mail bulunmuyor.</div>
                        <a href="<?php echo fn_menu_link('mailbox/compose'); ?>" class="btn btn-sm btn-primary">Yeni Mail Oluştur</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table align-middle table-hover table-row-dashed table-sm">
                            <thead>
                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-35px ps-9">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" id="select-all-drafts">
                                        </div>
                                    </th>
                                    <th>Alıcı</th>
                                    <th>Konu</th>
                                    <th class="text-end">Tarih</th>
                                    <th class="text-end pe-2">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($result['drafts'] as $draft): ?>
                                    <tr>
                                        <!--begin::Checkbox-->
                                        <td class="ps-9">
                                            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                <input class="form-check-input draft-checkbox" type="checkbox" data-draft-id="<?php echo $draft->id; ?>">
                                            </div>
                                        </td>
                                        <!--end::Checkbox-->

                                        <!--begin::Recipient-->
                                        <td class="w-150px">
                                            <a href="<?php echo fn_menu_link('mailbox/compose&draft_id=' . $draft->id); ?>" class="d-flex align-items-center text-dark">
                                                <div class="symbol symbol-35px me-3">
                                                    <span class="symbol-label bg-light-primary text-primary fw-bold"><?php echo strtoupper(substr($draft->to_email, 0, 1)); ?></span>
                                                </div>
                                                <span class="fw-semibold"><?php echo $draft->to_email; ?></span>
                                            </a>
                                        </td>
                                        <!--end::Recipient-->

                                        <!--begin::Title-->
                                        <td>
                                            <div class="text-dark mb-1">
                                                <a href="<?php echo fn_menu_link('mailbox/compose&draft_id=' . $draft->id); ?>" class="text-dark">
                                                    <span class="fw-bold"><?php echo $draft->subject; ?></span>
                                                </a>
                                            </div>
                                        </td>
                                        <!--end::Title-->

                                        <!--begin::Date-->
                                        <td class="text-end ">
                                            <span class="fw-semibold"><?php echo date('d.m.Y H:i', strtotime($draft->created_at)); ?></span>
                                        </td>
                                        <!--end::Date-->

                                        <!--begin::Actions-->
                                        <td class="w-100px text-end pe-2">
                                            <div class="d-flex justify-content-end">
                                                <a href="<?php echo fn_menu_link('mailbox/compose&draft_id=' . $draft->id); ?>" class="btn btn-sm btn-icon btn-light btn-active-light-primary me-2" data-bs-toggle="tooltip" title="Düzenle">
                                                    <i class="ki-duotone ki-pencil fs-2">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                    </i>
                                                </a>

                                                <form method="post" class="d-inline delete-form" onsubmit="return confirm('Bu taslağı silmek istediğinize emin misiniz?');">
                                                    <input type="hidden" name="draft_id" value="<?php echo $draft->id; ?>">
                                                    <input type="hidden" name="delete_draft" value="1">
                                                    <button type="submit" class="btn btn-sm btn-icon btn-light btn-active-danger" data-bs-toggle="tooltip" title="Sil">
                                                        <i class="ki-duotone ki-trash fs-2">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                            <span class="path3"></span>
                                                            <span class="path4"></span>
                                                            <span class="path5"></span>
                                                        </i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                        <!--end::Actions-->
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($result['pages'] > 1): ?>
                        <div class="d-flex justify-content-end p-5">
                            <ul class="pagination">
                                <?php for ($i = 1; $i <= $result['pages']; $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="<?php echo fn_menu_link('mailbox/drafts&page=' . $i); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                <!--end::Table-->
            </div>
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content-->
</div>
<!--end::Content-->

<script>
    // Mail arama fonksiyonu
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('mailbox-search');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const subject = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                    const recipient = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

                    if (subject.includes(searchText) || recipient.includes(searchText)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // Checkbox işlemleri
        const selectAllCheckbox = document.getElementById('select-all-drafts');
        const draftCheckboxes = document.querySelectorAll('.draft-checkbox');

        // Tümünü seç checkbox'u
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;

                draftCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });
        }

        // Tek tek checkbox'lar
        draftCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                let allSelected = true;

                draftCheckboxes.forEach(cb => {
                    if (!cb.checked) allSelected = false;
                });

                // Tümünü seç checkbox'unu güncelle
                if (selectAllCheckbox) selectAllCheckbox.checked = allSelected && draftCheckboxes.length > 0;
            });
        });

        // Bootstrap tooltip'lerini etkinleştir
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    });

    function deleteAttachment(draftId, attachmentIndex) {
        if (confirm('Bu eki silmek istediğinizden emin misiniz?')) {
            $.ajax({
                url: '<?php echo fn_menu_link("mailbox/delete_attachment"); ?>',
                type: 'POST',
                data: {
                    draft_id: draftId,
                    attachment_index: attachmentIndex,
                    ajax: 1
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success('Ek başarıyla silindi');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.error || 'Ek silinirken bir hata oluştu');
                    }
                }
            });
        }
    }
</script>

<script>
    $(document).ready(function() {
        // Bootstrap tooltip'lerini etkinleştir
        $('[data-bs-toggle="tooltip"]').tooltip();
    });

    function deleteAttachment(draftId, attachmentIndex) {
        if (confirm('Bu eki silmek istediğinizden emin misiniz?')) {
            $.ajax({
                url: '<?php fn_menu_link("mailbox/delete_attachment"); ?>',
                type: 'POST',
                data: {
                    draft_id: draftId,
                    attachment_index: attachmentIndex,
                    ajax: 1
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success('Ek başarıyla silindi');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.error || 'Ek silinirken bir hata oluştu');
                    }
                }
            });
        }
    }
</script>