/**
 * Mailbox modülü için JavaScript kodları
 */

$(document).ready(function() {
    // ClassicEditor'ı başlat
    if (typeof ClassicEditor !== 'undefined') {
        // Editor instances'ları saklamak için global bir nesne oluştur
        window.editors = {};
        // ClassicEditor ayarları
        var editorConfig = {
            toolbar: {
                items: [
                    'heading',
                    '|',
                    'bold',
                    'italic',
                    'link',
                    'bulletedList',
                    'numberedList',
                    '|',
                    'imageUpload',
                    'insertTable',
                    '|',
                    'fontColor',
                    'fontBackgroundColor',
                    '|',
                    'alignment',
                    'undo',
                    'redo'
                ]
            },
            language: 'tr',
            image: {
                toolbar: [
                    'imageTextAlternative',
                    'imageStyle:inline',
                    'imageStyle:block',
                    'imageStyle:side'
                ],
                // Base64 adapter kullan
                upload: {
                    types: ['jpeg', 'png', 'gif', 'jpg']
                }
            },
            table: {
                contentToolbar: [
                    'tableColumn',
                    'tableRow',
                    'mergeTableCells'
                ],
                // Basitleştirilmiş tablo ayarları
                defaultHeadings: true,
                defaultBorder: true
            },
            htmlSupport: {
                allow: [
                    {
                        name: /.*/,
                        attributes: true,
                        classes: true,
                        styles: true
                    }
                ]
            }
        };

        // SimpleUploadAdapter - Sunucuya resim yükleme
        class SimpleUploadAdapter {
            constructor(loader) {
                this.loader = loader;
            }

            upload() {
                return this.loader.file.then(file => {
                    const formData = new FormData();
                    formData.append('upload', file);
                    formData.append('ajax', '1');
                    formData.append('action', 'upload_image');

                    // Seçili hesap bilgisini gönder
                    const accountSelect = document.querySelector('select[name="account_id"]');
                    if (accountSelect) {
                        formData.append('account_id', accountSelect.value || 'default');
                    } else {
                        formData.append('account_id', 'default');
                    }

                    return new Promise((resolve, reject) => {
                        $.ajax({
                            url: WEB_DIR + '/index.php?do=mailbox/mailbox_admin',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                if (response.success && response.url) {
                                    resolve({
                                        default: response.url
                                    });
                                } else {
                                    reject(response.error || 'Resim yüklenirken hata oluştu.');
                                }
                            },
                            error: function() {
                                reject('Sunucu hatası: Resim yüklenemedi.');
                            }
                        });
                    });
                });
            }

            abort() {
                // Yükleme iptal edildi
            }
        }

        // Adapter fabrikası
        function SimpleUploadAdapterPlugin(editor) {
            editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
                return new SimpleUploadAdapter(loader);
            };
        }

        // ClassicEditor'ı textarea'lara uygula
        document.querySelectorAll('.ckeditor_mailbox').forEach(function(element) {
            ClassicEditor
                .create(element, {
                    ...editorConfig,
                    extraPlugins: [SimpleUploadAdapterPlugin],
                    // Performans iyileştirmeleri
                    wordCount: {
                        onUpdate: () => {}
                    },
                    typing: {
                        transformations: {
                            include: ['quotes']
                        }
                    }
                })
                .then(function(editor) {
                    // Editor instance'ını sakla
                    window.editors[element.id] = editor;

                    // Tablo ekleme işlemini basitleştir
                    editor.plugins.get('Table').on('afterInsert', tableElement => {
                        // Tablo eklendikten sonra basit bir sınıf ekle
                        tableElement.table.setAttribute('class', 'table table-bordered');
                    });
                })
                .catch(function(error) {
                    console.error('ClassicEditor yüklenirken hata oluştu:', error);
                });
        });
    }

    // CC ve BCC alanlarını göster/gizle
    $('#toggleCc').on('click', function(e) {
        e.preventDefault();
        $('#ccField').toggleClass('d-none');
        $(this).text($('#ccField').hasClass('d-none') ? 'CC Ekle' : 'CC Gizle');
    });

    $('#toggleBcc').on('click', function(e) {
        e.preventDefault();
        $('#bccField').toggleClass('d-none');
        $(this).text($('#bccField').hasClass('d-none') ? 'BCC Ekle' : 'BCC Gizle');
    });

    // Yanıt formu gönderimi
    $('#replyForm').on('submit', function(e) {
        e.preventDefault();

        // ClassicEditor içeriğini güncelle
        if (window.editors && window.editors.reply_content) {
            var editor = window.editors.reply_content;
            var content = editor.getData();

            // Boş içerik kontrolü
            if (!content || content.trim() === '' || content.trim() === '<p>&nbsp;</p>') {
                toastr.error('Lütfen bir yanıt yazın.');
                return false;
            }

            $('#reply_content').val(content);
        }

        var form = $(this);
        var url = form.attr('action') || window.location.href;

        // URL'ye ajax=1 parametresini ekle
        if (url.indexOf('?') !== -1) {
            url += '&ajax=1';
        } else {
            url += '?ajax=1';
        }

        // Form verilerini al
        var formData = form.serialize();

        // CC ve BCC alanlarını kontrol et ve boş değerleri temizle
        var ccField = form.find('input[name="cc"]');
        var bccField = form.find('input[name="bcc"]');

        if (ccField.length && ccField.val().trim() === '') {
            ccField.prop('disabled', true);
        }

        if (bccField.length && bccField.val().trim() === '') {
            bccField.prop('disabled', true);
        }

        // Yeni form verilerini al (boş alanlar devre dışı bırakıldı)
        formData = form.serialize();

        // AJAX parametresini ekle
        formData += '&ajax=1';

        // Alanları tekrar etkinleştir
        ccField.prop('disabled', false);
        bccField.prop('disabled', false);

        // Hata ayıklama için
        console.log('Form URL:', url);
        console.log('Form Data:', formData);

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('Response:', response);
                if (response.success) {
                    toastr.success('Yanıt başarıyla gönderildi.');
                    // Yönlendirme URL'sini data-redirect özelliğinden al
                    var redirectUrl = form.data('redirect') || 'mailbox.php';
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 1000);
                } else {
                    toastr.error('Hata: ' + (response.error || 'Yanıt gönderilemedi.'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.log('Response Text:', xhr.responseText);
                try {
                    var jsonResponse = JSON.parse(xhr.responseText);
                    console.log('Parsed JSON Response:', jsonResponse);
                    if (jsonResponse.success) {
                        toastr.success('Mail başarıyla gönderildi.');

                        // Yönlendirme yap
                        var redirectUrl = form.data('redirect') || 'mailbox.php';
                        setTimeout(function() {
                            window.location.href = redirectUrl;
                        }, 1000);
                        return;
                    }
                } catch (e) {
                    console.error('JSON Parse Error:', e);
                }
                toastr.error('Bir hata oluştu. Lütfen tekrar deneyin.');
            }
        });
    });

    // Yönlendirme formu gönderimi
    $('#forwardForm').on('submit', function(e) {
        e.preventDefault();

        // ClassicEditor içeriğini güncelle
        if (window.editors && window.editors.forward_content) {
            var editor = window.editors.forward_content;
            var content = editor.getData();

            // Boş içerik kontrolü
            if (!content || content.trim() === '' || content.trim() === '<p>&nbsp;</p>') {
                toastr.error('Lütfen bir not yazın.');
                return false;
            }

            $('#forward_content').val(content);
        }

        var form = $(this);
        var url = form.attr('action') || window.location.href;

        // URL'ye ajax=1 parametresini ekle
        if (url.indexOf('?') !== -1) {
            url += '&ajax=1';
        } else {
            url += '?ajax=1';
        }

        // Form verilerini al
        var formData = form.serialize();

        // AJAX parametresini ekle
        formData += '&ajax=1';

        // Hata ayıklama için
        console.log('Forward Form URL:', url);
        console.log('Forward Form Data:', formData);

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('Forward Response:', response);
                if (response.success) {
                    toastr.success('Mail başarıyla yönlendirildi.');
                    // Yönlendirme URL'sini data-redirect özelliğinden al
                    var redirectUrl = form.data('redirect') || 'mailbox.php';
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 1000);
                } else {
                    toastr.error('Hata: ' + (response.error || 'Mail yönlendirilemedi.'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Forward AJAX Error:', status, error);
                console.log('Forward Response Text:', xhr.responseText);
                try {
                    var jsonResponse = JSON.parse(xhr.responseText);
                    console.log('Parsed JSON Response:', jsonResponse);
                    if (jsonResponse.success) {
                        toastr.success('Mail başarıyla yönlendirildi.');

                        // Yönlendirme yap
                        var redirectUrl = form.data('redirect') || 'mailbox.php';
                        setTimeout(function() {
                            window.location.href = redirectUrl;
                        }, 1000);
                        return;
                    }
                } catch (e) {
                    console.error('JSON Parse Error:', e);
                }
                toastr.error('Bir hata oluştu. Lütfen tekrar deneyin.');
            }
        });
    });

    // Compose formu gönderimi
    $('#compose-form').on('submit', function() {
        // ClassicEditor içeriğini güncelle
        if (window.editors && window.editors.body) {
            var editor = window.editors.body;
            var content = editor.getData();

            // Boş içerik kontrolü
            if (!content || content.trim() === '' || content.trim() === '<p>&nbsp;</p>') {
                toastr.error('Lütfen mail içeriği yazın.');
                return false;
            }

            $('#body').val(content);
        }
    });

    // Taslak kaydetme
    if (typeof saveDraft === 'function') {
        // Taslak kaydetme fonksiyonu zaten tanımlı
    } else {
        window.saveDraft = function() {
            // ClassicEditor içeriğini güncelle
            if (window.editors && window.editors.body) {
                var editor = window.editors.body;
                var content = editor.getData();
                $('#body').val(content);
            }

            var formData = new FormData(document.getElementById('compose-form'));
            formData.append('save_draft', '1');
            formData.append('ajax', '1');

            $.ajax({
                url: WEB_DIR + '/index.php?do=mailbox/save_draft',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        if (response.message) {
                            toastr.info(response.message);
                        } else {
                            toastr.success('Taslak kaydedildi.');
                            setTimeout(() => {
                                location.href = WEB_DIR + '/index.php?do=mailbox/drafts';
                            }, 1000);
                        }
                    } else {
                        toastr.error(response.error || 'Taslak kaydedilemedi.');
                    }
                },
                error: function() {
                    toastr.error('Bir hata oluştu. Lütfen tekrar deneyin.');
                }
            });
        }
    }

    // Bootstrap tooltip'lerini etkinleştir
    if (typeof $.fn.tooltip !== 'undefined') {
        $('[data-bs-toggle="tooltip"]').tooltip();
    }



    // Hazır mesaj seçme işlemi
    $('#template_select').on('change', function() {
        var templateId = $(this).val();
        if (templateId) {
            $.ajax({
                url: WEB_DIR + '/index.php?do=mailbox/mailbox_admin',
                type: 'POST',
                data: {
                    ajax: 1,
                    action: 'get_template',
                    template_id: templateId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.content) {
                        // ClassicEditor'a içeriği ekle
                        if ($('#reply_content').length) {
                            // Yanıt sayfasında
                            if (window.editors && window.editors.reply_content) {
                                var editor = window.editors.reply_content;
                                var currentContent = editor.getData();
                                editor.setData(response.content + '<br><br>' + currentContent);
                            } else {
                                // Normal textarea'ya içeriği ekle
                                var textarea = document.getElementById('reply_content');
                                textarea.value = response.content + '\n\n' + textarea.value;
                            }
                        } else if ($('#body').length) {
                            // Yeni mail sayfasında
                            if (window.editors && window.editors.body) {
                                var editor = window.editors.body;
                                var currentContent = editor.getData();
                                editor.setData(response.content + '<br><br>' + currentContent);
                            } else {
                                // Normal textarea'ya içeriği ekle
                                var textarea = document.getElementById('body');
                                textarea.value = response.content + '\n\n' + textarea.value;
                            }
                        }
                    }
                },
                error: function() {
                    alert('Hazır mesaj yüklenirken bir hata oluştu.');
                }
            });
        }
    });

    // Ek silme fonksiyonu
    if (typeof deleteAttachment !== 'function') {
        window.deleteAttachment = function(draftId, attachmentIndex) {
            if (confirm('Bu eki silmek istediğinizden emin misiniz?')) {
                $.ajax({
                    url: WEB_DIR + '/index.php?do=mailbox/delete_attachment',
                    type: 'POST',
                    data: {
                        draft_id: draftId,
                        attachment_index: attachmentIndex,
                        ajax: 1
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success('Ek başarıyla silindi');
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.error || 'Ek silinirken bir hata oluştu');
                        }
                    }
                });
            }
        }
    }
});