<?php
function fn_menu_items()
{
    // Okunmamış mail sayısını al
    $unread_count = 0;
    if (function_exists('get_unread_count')) {
        $unread_count = get_unread_count();
    }

    return array(
        'dashboard' => array(
            'title' => 'Gösterge Paneli',
            'icon' => 'ki-element-11'
        ),
        'mailbox' => array(
            'title' => 'Mail Kutusu',
            'icon' => 'ki-mailbox',
            'items' => array(
                'compose' => array(
                    'title' => 'Yeni Mail',
                    'icon' => 'ki-add-files'
                ),
                'inbox' => array(
                    'title' => 'Gelen Kutusu',
                    'icon' => 'ki-inbox',
                    'badge' => $unread_count > 0 ? array(
                        'text' => $unread_count,
                        'class' => 'badge-primary'
                    ) : null
                ),
                'outbox' => array(
                    'title' => 'Giden Kutusu',
                    'icon' => 'ki-send'
                ),
                'drafts' => array(
                    'title' => 'Taslaklar',
                    'icon' => 'ki-notepad'
                ),
                'signatures' => array(
                    'title' => 'İmzalar',
                    'icon' => 'ki-signature'
                )
            )
        ),
        // ... diğer menü öğeleri ...
    );
}

function mailbox_connect($account_id = null)
{
    global $mailbox_config;


    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // Hesap bilgilerini al
    $account = null;
    if ($account_id === 'default' || $account_id === null) {
        $account = $mailbox_config['default'];
    } else {
        // Belirtilen hesabı bul
        foreach ($mailbox_config['accounts'] as $acc) {
            if ($acc['id'] == $account_id) {
                $account = $acc;
                break;
            }
        }

        // Hesap bulunamadıysa varsayılanı kullan
        if ($account === null) {
            $account = $mailbox_config['default'];
        }
    }
    // echo 'account_id : ' . $account_id;

    // IMAP bağlantı dizesini oluştur
    $host = $account['imap_host'];

    // INBOX kısmını temizle
    $host = preg_replace('/}INBOX$/', '}INBOX', $host);

    // Eğer host zaten {} içeriyorsa, doğrudan kullan
    if (strpos($host, '{') === 0 && strpos($host, '}') !== false) {
        $imap_string = $host;
    } else {
        // Aksi takdirde, host adresini al ve IMAP dizesini oluştur
        $host_parts = parse_url($host);
        $server = isset($host_parts['host']) ? $host_parts['host'] : $host;
        $imap_string = '{' . $server . ':993/imap/ssl/novalidate-cert}';
    }
    $imap_string;
    $inbox = imap_open(
        $imap_string,
        $account['username'],
        $account['password']
    );

    if (!$inbox) {
        throw new Exception('IMAP bağlantısı kurulamadı: ' . imap_last_error());
    }

    return $inbox;
}

function get_email_list($mailbox = null, $folder = 'INBOX', $page = 1, $per_page = 20, $account_id = null)
{
    try {
        global $mailbox_config;

        // Hesap ID belirtilmemişse, aktif hesabı kullan
        if ($account_id === null && isset($_SESSION['active_mail_account'])) {
            $account_id = $_SESSION['active_mail_account'];
        }

        // Hesap bilgilerini al
        $account = null;
        if ($account_id === 'default' || $account_id === null) {
            $account = $mailbox_config['default'];
        } else {
            // Belirtilen hesabı bul
            foreach ($mailbox_config['accounts'] as $acc) {
                if ($acc['id'] == $account_id) {
                    $account = $acc;
                    break;
                }
            }

            // Hesap bulunamadıysa varsayılanı kullan
            if ($account === null) {
                $account = $mailbox_config['default'];
            }
        }

        $close_connection = false;

        // Eğer mailbox parametresi verilmemişse, yeni bir bağlantı aç
        if (!$mailbox) {
            $mailbox = mailbox_connect($account_id);
            $close_connection = true;
        }

        $start = ($page - 1) * $per_page;
        $emails = array();

        $total_emails = imap_num_msg($mailbox);

        // Mail yoksa boş dizi döndür
        if ($total_emails <= 0) {
            // Bağlantıyı kapat (eğer biz açtıysak)
            if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                @imap_close($mailbox);
            }

            return array(
                'emails' => array(),
                'total' => 0,
                'pages' => 0
            );
        }

        $end = max(1, $total_emails - $start);
        $start = max(1, $total_emails - ($start + $per_page));

        for ($i = $end; $i >= $start; $i--) {
            $header = imap_headerinfo($mailbox, $i);
            // Okunma durumunu kontrol et
            $seen = 1; // Varsayılan olarak okunmuş

            // Unseen bayrağını kontrol et
            if (isset($header->Unseen) && trim($header->Unseen) == 'U') {
                $seen = 0;
            }

            // Recent bayrağını kontrol et
            if (isset($header->Recent) && trim($header->Recent) == 'N') {
                $seen = 0;
            }

            // Flagged durumunu kontrol et
            if (isset($header->Flagged) && trim($header->Flagged) == 'F') {
                // Flagged mailler genellikle okunmuş olur
                $seen = 1;
            }

            // Mail bayraklarını kontrol et
            $flags = imap_fetch_overview($mailbox, $i);
            if (!empty($flags) && isset($flags[0])) {
                if (!$flags[0]->seen) {
                    $seen = 0;
                }
            }

            $emails[] = array(
                'id' => $i,
                'subject' => decode_mime_string($header->subject),
                'from' => decode_mime_string($header->from[0]->mailbox . '@' . $header->from[0]->host),
                'date' => date('Y-m-d H:i', strtotime($header->date)),
                'seen' => $seen
            );
        }

        // Sonuçları hazırla
        $result = array(
            'emails' => $emails,
            'total' => $total_emails,
            'pages' => ceil($total_emails / $per_page)
        );

        // Bağlantıyı kapat (eğer biz açtıysak)
        if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        return $result;
    } catch (Exception $e) {
        // Hata durumunda bağlantıyı kapat (eğer biz açtıysak)
        if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        error_log("Mail listesi alınamadı: " . $e->getMessage());
        return array(
            'emails' => array(),
            'total' => 0,
            'pages' => 0
        );
    }
}

function get_email_content($mailbox, $email_id)
{
    $structure = imap_fetchstructure($mailbox, $email_id);
    $header = imap_headerinfo($mailbox, $email_id);

    // Mail yapısını debug için göster

    // Mail gövdesini al
    $body = '';
    $attachments = array();


    // Ekleri almak için özyinelemeli fonksiyon
    $get_attachments = function ($structure, $parent_part = '') use (&$get_attachments, $mailbox, $email_id, &$attachments) {
        if (isset($structure->parts)) {
            foreach ($structure->parts as $part_num => $part) {
                $part_id = $parent_part ? $parent_part . '.' . ($part_num + 1) : ($part_num + 1);

                // Alt parçaları recursive olarak kontrol et
                if (isset($part->parts) && count($part->parts) > 0) {
                    $get_attachments($part, $part_id);
                }

                // Dosya adını bulmak için tüm olası yöntemleri dene
                $filename = '';

                // 1. dparameters kontrolü - Dosya adı genellikle burada
                if ($part->ifdparameters) {
                    foreach ($part->dparameters as $param) {
                        if (strtolower($param->attribute) == 'filename' || strtolower($param->attribute) == 'name') {
                            $filename = $param->value;
                            break;
                        }
                    }
                }

                // 2. parameters kontrolü - Dosya adı bazen burada
                if (!$filename && $part->ifparameters) {
                    foreach ($part->parameters as $param) {
                        if (strtolower($param->attribute) == 'name') {
                            $filename = $param->value;
                            break;
                        }
                    }
                }

                // 3. Content-Disposition başlığından dosya adını çıkarmayı dene
                if (!$filename) {
                    $headers = imap_fetchheader($mailbox, $email_id);
                    if (preg_match('/Content-Disposition:.*?filename="([^"]+)"/i', $headers, $matches)) {
                        $filename = $matches[1];
                    } elseif (preg_match('/Content-Disposition:.*?filename=([^;\r\n]+)/i', $headers, $matches)) {
                        $filename = $matches[1];
                    }
                }

                // 4. Content-Type başlığından dosya adını çıkarmayı dene
                if (!$filename) {
                    $headers = imap_fetchheader($mailbox, $email_id);
                    if (preg_match('/Content-Type:.*?name="([^"]+)"/i', $headers, $matches)) {
                        $filename = $matches[1];
                    } elseif (preg_match('/Content-Type:.*?name=([^;\r\n]+)/i', $headers, $matches)) {
                        $filename = $matches[1];
                    }
                }

                // 5. Dosya adını decode et (MIME encoding)
                if ($filename) {
                    // MIME encoded dosya adlarını çöz
                    if (preg_match('/=\?UTF-8\?Q\?(.*?)\?=/i', $filename, $matches)) {
                        $filename = quoted_printable_decode($matches[1]);
                    } elseif (preg_match('/=\?UTF-8\?B\?(.*?)\?=/i', $filename, $matches)) {
                        $filename = base64_decode($matches[1]);
                    }

                    // URL encoding'i çöz
                    $filename = urldecode($filename);

                    // Türkçe karakterleri düzeltme işlemini kaldırıyoruz
                    // Dosya adları orijinal halinde kalacak

                    // Özel karakterleri temizle
                    $filename = preg_replace('/[^\x20-\x7E\x{00A0}-\x{00FF}\x{0100}-\x{017F}\x{0180}-\x{024F}\x{0300}-\x{036F}\x{1E00}-\x{1EFF}\x{2C60}-\x{2C7F}\x{A720}-\x{A7FF}]/u', '', $filename);

                    $attachments[] = array(
                        'filename' => $filename,
                        'part_num' => $part_id,
                        'size' => $part->bytes
                    );
                }

                // İçerik tipine göre ek olabilecek parçaları kontrol et
                if ($part->type != 0 && $part->type != 1) { // 0:TEXT, 1:MULTIPART dışındakiler genelde ek
                    // Eğer dosya adı daha önce bulunmadıysa, tip ve alt-tip ile bir dosya adı oluştur
                    $found = false;
                    foreach ($attachments as $att) {
                        if ($att['part_num'] == $part_id) {
                            $found = true;
                            break;
                        }
                    }

                    if (!$found) {
                        $default_name = '';
                        switch ($part->type) {
                            case 3: // APPLICATION
                                $default_name = 'attachment.' . strtolower($part->subtype);
                                break;
                            case 4: // AUDIO
                                $default_name = 'audio.' . strtolower($part->subtype);
                                break;
                            case 5: // IMAGE
                                $default_name = 'image.' . strtolower($part->subtype);
                                break;
                            case 6: // VIDEO
                                $default_name = 'video.' . strtolower($part->subtype);
                                break;
                            default:
                                $default_name = 'attachment.bin';
                        }

                        $attachments[] = array(
                            'filename' => $default_name,
                            'part_num' => $part_id
                        );
                    }
                }
            }
        }
    };

    // Mail parçalarını al
    if ($structure->type == 1) { // MULTIPART
        // Önce ekleri tespit et
        $get_attachments($structure);

        for ($i = 1; $i <= count($structure->parts); $i++) {
            $part_body = imap_fetchbody($mailbox, $email_id, $i);
            $part = $structure->parts[$i - 1];

            // Part body'yi encoding'e göre decode et
            if ($part->encoding == 3) { // BASE64
                $part_body = base64_decode($part_body);
            } elseif ($part->encoding == 4) { // QUOTED-PRINTABLE
                $part_body = quoted_printable_decode($part_body);
            }

            // Part'ın charset'ini kontrol et ve dönüştür
            if (!empty($part->parameters)) {
                foreach ($part->parameters as $param) {
                    if (strtolower($param->attribute) == 'charset') {
                        $part_body = mb_convert_encoding($part_body, 'UTF-8', $param->value);
                        break;
                    }
                }
            }

            // İçerik tipine göre işle
            switch ($part->type) {
                case 0: // TEXT
                    if (strtolower($part->subtype) == 'html') {
                        $body = $part_body;
                        break 2; // Ana döngüden çık
                    } elseif (strtolower($part->subtype) == 'plain' && empty($body)) {
                        $body = '<div style="white-space: pre-wrap;">' . htmlspecialchars($part_body, ENT_QUOTES, 'UTF-8') . '</div>';
                    }
                    break;

                case 1: // TYPEMULTIPART
                    // MULTIPART içeriği için alt parçaları işle
                    if (!empty($part->parts)) {
                        foreach ($part->parts as $subpart_index => $subpart) {
                            $subpart_body = imap_fetchbody($mailbox, $email_id, ($i) . '.' . ($subpart_index + 1));

                            // Alt parçanın encoding'ini decode et
                            if ($subpart->encoding == 3) { // BASE64
                                $subpart_body = base64_decode($subpart_body);
                            } elseif ($subpart->encoding == 4) { // QUOTED-PRINTABLE
                                $subpart_body = quoted_printable_decode($subpart_body);
                            }

                            // HTML içeriği varsa onu kullan
                            if ($subpart->type == 0 && strtolower($subpart->subtype) == 'html') {
                                $body = $subpart_body;
                                break;
                            }
                            // Text içeriği varsa ve body boşsa onu kullan
                            elseif ($subpart->type == 0 && strtolower($subpart->subtype) == 'plain' && empty($body)) {
                                $body = '<div style="white-space: pre-wrap;">' . htmlspecialchars($subpart_body, ENT_QUOTES, 'UTF-8') . '</div>';
                            }
                        }
                    }
                    // Alt parça yoksa ve body boşsa raw içeriği göster
                    elseif (empty($body)) {
                        $body = '<div class="embedded-message">' . htmlspecialchars($part_body, ENT_QUOTES, 'UTF-8') . '</div>';
                    }
                    break;
                case 2: // MESSAGE
                    if (empty($body)) {
                        $body = '<div class="embedded-message">' . $part_body . '</div>';
                    }
                    break;

                case 3: // APPLICATION
                    // Uygulama içeriği - ek olarak işleniyor
                    break;

                case 4: // AUDIO
                    // Ses içeriği - ek olarak işleniyor
                    break;

                case 5: // IMAGE
                    if (empty($body)) {
                        $body = '<div><img src="data:image/' . strtolower($part->subtype) . ';base64,' . base64_encode($part_body) . '" /></div>';
                    }
                    break;

                case 6: // VIDEO
                    // Video içeriği - ek olarak işleniyor
                    break;

                case 7: // OTHER
                    // Diğer içerikler - ek olarak işleniyor
                    break;
            }
        }
    } else { // Tek parça
        $body = imap_body($mailbox, $email_id);

        // Encoding'e göre decode et
        if ($structure->encoding == 3) { // BASE64
            $body = base64_decode($body);
        } elseif ($structure->encoding == 4) { // QUOTED-PRINTABLE
            $body = quoted_printable_decode($body);
        }

        // Charset'e göre dönüştür
        if (!empty($structure->parameters)) {
            foreach ($structure->parameters as $param) {
                if (strtolower($param->attribute) == 'charset') {
                    $body = mb_convert_encoding($body, 'UTF-8', $param->value);
                    break;
                }
            }
        }

        // Plain text ise HTML'e çevir
        if (strtolower($structure->subtype) != 'html') {
            $body = '<div style="white-space: pre-wrap;">' . htmlspecialchars($body, ENT_QUOTES, 'UTF-8') . '</div>';
        }
    }

    // Alternatif yaklaşım: Tüm mail içeriğini al
    if (empty($body)) {
        $full_body = imap_body($mailbox, $email_id);

        // Encoding'e göre decode et
        if ($structure->encoding == 3) { // BASE64
            $full_body = base64_decode($full_body);
        } elseif ($structure->encoding == 4) { // QUOTED-PRINTABLE
            $full_body = quoted_printable_decode($full_body);
        }

        // MIME sınırlarını ve başlıkları temizle
        $full_body = preg_replace('/--=_.*?--/s', '', $full_body);
        $full_body = preg_replace('/--Apple-Mail=_.*?--/s', '', $full_body);
        $full_body = preg_replace('/--Apple-Mail=_[A-Z0-9-]+/s', '', $full_body);
        $full_body = preg_replace('/Content-Disposition:.*?filename.*?\n/is', '', $full_body);
        $full_body = preg_replace('/Content-Type:.*?name.*?\n/is', '', $full_body);
        $full_body = preg_replace('/Content-Transfer-Encoding:.*?\n/is', '', $full_body);

        // Base64 kodlanmış içeriği temizle
        $full_body = preg_replace('/[A-Za-z0-9+\/=]{60,}\s*/s', '[Dosya içeriği]', $full_body);

        // HTML içeriği varsa kullan
        if (strpos($full_body, '<html') !== false || strpos($full_body, '<body') !== false) {
            $body = $full_body;
        } else {
            $body = '<div style="white-space: pre-wrap;">' . htmlspecialchars($full_body, ENT_QUOTES, 'UTF-8') . '</div>';
        }
    }

    // Tüm mail içeriğini al (RFC822 formatında)
    $raw_email = imap_fetchbody($mailbox, $email_id, "");

    // Base64 kodlanmış içeriği temizle
    $body = preg_replace('/[A-Za-z0-9+\/=]{60,}\s*/s', '[Dosya içeriği]', $body);

    // MIME başlıklarını temizle
    $body = preg_replace('/Content-Disposition:\s*inline;\s*filename.*?\n/is', '', $body);
    $body = preg_replace('/Content-Type:\s*application\/.*?\n/is', '', $body);
    $body = preg_replace('/Content-Transfer-Encoding:\s*base64\s*\n/is', '', $body);
    $body = preg_replace('/--Apple-Mail=_[A-Z0-9-]+/s', '', $body);

    // Geçmiş yazışmaları ekle
    $raw_parts = explode("\r\n\r\n", $raw_email, 2);
    if (count($raw_parts) > 1) {
        $raw_body = $raw_parts[1];

        // Geçmiş yazışma işaretlerini ara
        $markers = array(
            '-----Original Message-----',
            '-----Orijinal Mesaj-----',
            'From:',
            'Kimden:',
            'On ',
            'Tarih:'
        );

        foreach ($markers as $marker) {
            $pos = strpos($raw_body, $marker);
            if ($pos !== false) {
                $previous_email = substr($raw_body, $pos);

                // Quoted-printable kodlamasını çöz
                $previous_email = quoted_printable_decode($previous_email);

                // MIME sınırlarını temizle
                $previous_email = preg_replace('/--=_.*?--/s', '', $previous_email);
                $previous_email = preg_replace('/--Apple-Mail=_.*?--/s', '', $previous_email);
                $previous_email = preg_replace('/--Apple-Mail=_[A-Z0-9-]+/s', '', $previous_email);
                $previous_email = preg_replace('/Content-Disposition:.*?filename.*?\n/is', '', $previous_email);
                $previous_email = preg_replace('/Content-Type:.*?name.*?\n/is', '', $previous_email);
                $previous_email = preg_replace('/Content-Transfer-Encoding:.*?\n/is', '', $previous_email);

                // Base64 kodlanmış içeriği temizle
                $previous_email = preg_replace('/[A-Za-z0-9+\/=]{60,}\s*/s', '[Dosya içeriği]', $previous_email);

                // "=20" gibi quoted-printable kalıntılarını temizle
                $previous_email = preg_replace('/=\d\d(\s|$)/', ' ', $previous_email);

                // "=\r\n" gibi satır sonlarını temizle
                $previous_email = preg_replace('/=\r\n/', '', $previous_email);

                // HTML içeriğinde bu yazışma yoksa ekle
                if (strpos($body, $marker) === false) {
                    $previous_html = '<div class="previous-email" style="margin-top: 20px; padding: 10px; border-left: 3px solid #ccc; background-color: #f9f9f9; color: #666; white-space: pre-wrap;">' .
                        htmlspecialchars($previous_email, ENT_QUOTES, 'UTF-8') .
                        '</div>';

                    // HTML içeriğine ekle
                    if (strpos($body, '</body>') !== false) {
                        $body = str_replace('</body>', $previous_html . '</body>', $body);
                    } else {
                        $body .= $previous_html;
                    }
                }

                break;
            }
        }
    }

    return array(
        'subject' => decode_mime_string($header->subject),
        'from' => decode_mime_string($header->from[0]->mailbox . '@' . $header->from[0]->host),
        'to' => decode_mime_string($header->to[0]->mailbox . '@' . $header->to[0]->host),
        'date' => date('Y-m-d H:i', strtotime($header->date)),
        'body' => $body,
        'attachments' => $attachments
    );
}

/**
 * Belirli ID'lere sahip mailleri getirir
 */
function get_email_list_by_ids($mailbox = null, $folder = 'INBOX', $email_ids = array(), $page = 1, $per_page = 20, $account_id = null)
{
    try {
        global $mailbox_config;

        // Boş ID listesi kontrolü
        if (empty($email_ids)) {
            return array(
                'emails' => array(),
                'pages' => 0,
                'total' => 0
            );
        }

        // Hesap ID belirtilmemişse, aktif hesabı kullan
        if ($account_id === null && isset($_SESSION['active_mail_account'])) {
            $account_id = $_SESSION['active_mail_account'];
        }

        $close_connection = false;

        // Eğer mailbox parametresi verilmemişse, yeni bir bağlantı aç
        if (!$mailbox) {
            $mailbox = mailbox_connect($account_id);
            $close_connection = true;
        }

        // Toplam mail sayısı
        $total_emails = count($email_ids);

        // Sayfalama hesapla
        $total_pages = ceil($total_emails / $per_page);
        $page = max(1, min($page, $total_pages));
        $start = ($page - 1) * $per_page;

        // Sayfa için ID'leri al
        $page_email_ids = array_slice($email_ids, $start, $per_page);

        // Mail listesini al
        $emails = array();

        foreach ($page_email_ids as $email_id) {
            $header = imap_headerinfo($mailbox, $email_id);
            if ($header) {
                // Okunma durumunu kontrol et
                $seen = 1; // Varsayılan olarak okunmuş

                // Unseen bayrağını kontrol et
                if (isset($header->Unseen) && trim($header->Unseen) == 'U') {
                    $seen = 0;
                }

                // Recent bayrağını kontrol et
                if (isset($header->Recent) && trim($header->Recent) == 'N') {
                    $seen = 0;
                }

                // Flagged durumunu kontrol et
                if (isset($header->Flagged) && trim($header->Flagged) == 'F') {
                    // Flagged mailler genellikle okunmuş olur
                    $seen = 1;
                }

                // Mail bayraklarını kontrol et
                $flags = imap_fetch_overview($mailbox, $email_id);
                if (!empty($flags) && isset($flags[0])) {
                    if (!$flags[0]->seen) {
                        $seen = 0;
                    }
                }

                $emails[] = array(
                    'id' => $email_id,
                    'subject' => decode_mime_string($header->subject),
                    'from' => decode_mime_string($header->from[0]->mailbox . '@' . $header->from[0]->host),
                    'date' => date('Y-m-d H:i', strtotime($header->date)),
                    'seen' => $seen
                );
            }
        }

        // Bağlantıyı kapat (eğer biz açtıysak)
        if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        return array(
            'emails' => $emails,
            'pages' => $total_pages,
            'total' => $total_emails
        );
    } catch (Exception $e) {
        // Hata durumunda bağlantıyı kapat (eğer biz açtıysak)
        if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        error_log("Mail listesi alınamadı: " . $e->getMessage());
        return array(
            'emails' => array(),
            'pages' => 0,
            'total' => 0
        );
    }
}

/**
 * Geçmiş mail içeriğini temizler
 */
function clean_previous_email($content)
{
    // Sadece <html> ve <body> etiketlerini koru, diğer tüm özellikleri temizle
    $content = preg_replace('/<html[^>]*>/i', '<html>', $content);
    $content = preg_replace('/<body[^>]*>/i', '<body>', $content);

    // DOCTYPE, head ve diğer teknik bölümleri temizle
    $content = preg_replace('/<!DOCTYPE[^>]*>/i', '', $content);
    $content = preg_replace('/<head>.*?<\/head>/is', '', $content);
    $content = preg_replace('/<!--.*?-->/s', '', $content);

    // Tüm XML namespace ve özel etiketleri temizle
    $content = preg_replace('/xmlns:[^=]*="[^"]*"/i', '', $content);
    $content = preg_replace('/<\/?[ov]:[^>]*>/i', '', $content);

    // Tüm stil ve özel özellikleri temizle
    $content = preg_replace('/\s(style|class|id|width|height|align|valign|border|cellspacing|cellpadding|bgcolor|background|marginwidth|marginheight|topmargin|leftmargin|offset)="[^"]*"/i', '', $content);

    // Fazla boşlukları temizle
    $content = preg_replace('/\s+/', ' ', $content);

    // Boş etiketleri temizle
    $content = preg_replace('/<(p|div|span|table|tr|td)[^>]*>\s*<\/\1>/i', '', $content);

    // Satır sonlarını düzenle
    $content = str_replace('> <', ">\n<", $content);

    return trim($content);
}

function decode_mime_string($string)
{
    return imap_utf8(imap_mime_header_decode($string)[0]->text);
}

function get_part_content($mailbox, $email_id, $part, $part_num)
{
    if ($part->type == 0) {
        $content = imap_fetchbody($mailbox, $email_id, $part_num + 1);

        if ($part->encoding == 3) {
            $content = base64_decode($content);
        } elseif ($part->encoding == 4) {
            $content = quoted_printable_decode($content);
        }

        return $content;
    }
    return '';
}

function send_email($to, $subject, $body, $attachments = array(), $signature_id = null, $cc = array(), $bcc = array(), $account_id = null)
{
    global $mailbox_config, $MAILBOX_HEADER, $MAILBOX_BODY_BEFORE, $MAILBOX_BODY_AFTER;

    // Hangi hesaptan gönderileceğini belirle
    $account = null;
    if ($account_id !== null) {
        // Belirtilen hesabı bul
        foreach ($mailbox_config['accounts'] as $acc) {
            if ($acc['id'] == $account_id) {
                $account = $acc;
                break;
            }
        }
    }

    // Hesap bulunamadıysa varsayılan hesabı kullan
    if ($account === null) {
        $account = $mailbox_config['default'];
    }

    $mail = new PHPMailer\PHPMailer\PHPMailer();
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = 'error_log';

    try {
        $mail->isSMTP();
        $mail->Host = $account['smtp_host'];
        $mail->SMTPAuth = $account['smtp_auth'];
        $mail->Username = $account['username'];
        $mail->Password = $account['password'];
        $mail->SMTPSecure = $account['smtp_secure'];
        $mail->Port = $account['smtp_port'];
        $mail->CharSet = 'UTF-8';

        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // error_log("SMTP Config: " . print_r([
        //     'host' => $account['smtp_host'],
        //     'port' => $account['smtp_port'],
        //     'secure' => $account['smtp_secure'],
        //     'username' => $account['username']
        // ], true));

        // Gönderen adını ayarla
        if (isset($account['display_name']) && !empty($account['display_name'])) {
            $mail->setFrom($account['username'], $account['display_name']);
        } else {
            $mail->setFrom($account['username']);
        }
        $mail->addAddress($to);

        // CC alıcıları ekle
        if (!empty($cc)) {
            if (is_array($cc)) {
                foreach ($cc as $cc_address) {
                    if (!empty($cc_address)) {
                        $mail->addCC($cc_address);
                    }
                }
            } else {
                $mail->addCC($cc);
            }
        }

        // BCC alıcıları ekle
        if (!empty($bcc)) {
            if (is_array($bcc)) {
                foreach ($bcc as $bcc_address) {
                    if (!empty($bcc_address)) {
                        $mail->addBCC($bcc_address);
                    }
                }
            } else {
                $mail->addBCC($bcc);
            }
        }

        foreach ($attachments as $attachment) {
            $mail->addAttachment($attachment['tmp_name'], $attachment['name']);
        }

        // İmza ekle
        if ($signature_id) {
            $signature = get_signature($signature_id);
            if ($signature && !empty($signature->content)) {
                $body .= '<br><div class="signature">' . $signature->content . '</div>';
            }
        } else {
            // Varsayılan imzayı ekle
            $default_signature = get_default_signature();
            if ($default_signature && !empty($default_signature->content)) {
                $body .= '<div class="signature" style="width:766px; height:auto;">' . $default_signature->content . '</div>';
            }
        }

        // HTML mail için gerekli ayarlar
        $mail->isHTML(true);
        $mail->Subject = $subject;

        // HTML mail için gerekli başlıklar
        $render_body = $MAILBOX_HEADER . $MAILBOX_BODY_BEFORE  . $body . $MAILBOX_BODY_AFTER;
        $mail->Body = $render_body;
        //         $mail->Body = '<!DOCTYPE html>
        // <html>
        // <head>
        // <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        // <style>
        //     table { border-collapse: collapse; width: 100%; }
        //     th, td { border: 1px solid #dddddd; padding: 8px; text-align: left; }
        //     th { background-color: #f2f2f2; }
        //     .table { width: auto; margin-bottom: 1rem; color: #212529; }
        //     .table-bordered { border: 1px solid #dee2e6; }
        //     .table-striped tbody tr:nth-of-type(odd) { background-color: rgba(0, 0, 0, 0.05); }
        // </style>
        // </head>
        // <body>
        // ' . $body . '
        // </body>
        // </html>';
        $mail->AltBody = strip_tags($body);

        $result = $mail->send();
        if (!$result) {
            error_log("Mail Error: " . $mail->ErrorInfo);
            throw new Exception($mail->ErrorInfo);
        }

        // Gönderilen maili "Sent" klasörüne kopyala
        try {
            // IMAP bağlantısı aç
            $imap_string = parse_url($account['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';
            $mailbox = imap_open($imap_string, $account['username'], $account['password']);

            // Sent klasörünü bul
            $folders = imap_list($mailbox, $imap_string, '*');
            $sent_folder = null;
            foreach ($folders as $folder) {
                if (
                    stripos($folder, 'Sent') !== false ||
                    stripos($folder, 'Gönderilmiş') !== false ||
                    stripos($folder, 'Giden') !== false
                ) {
                    $sent_folder = $folder;
                    break;
                }
            }

            if ($sent_folder) {
                // Mail içeriğini oluştur
                $boundary = "----=_NextPart_" . md5(time());
                $header = "From: ";
                if (isset($account['display_name']) && !empty($account['display_name'])) {
                    $header .= $account['display_name'] . " <" . $account['username'] . ">";
                } else {
                    $header .= $account['username'];
                }
                $header .= "\r\n";
                $header .= "To: " . $to . "\r\n";
                $header .= "Subject: " . $subject . "\r\n";
                $header .= "Date: " . date("r") . "\r\n";
                $header .= "MIME-Version: 1.0\r\n";
                $header .= "Content-Type: multipart/mixed; boundary=\"" . $boundary . "\"\r\n";

                // Mail gövdesi oluştur
                $message = "--" . $boundary . "\r\n";
                $message .= "Content-Type: text/html; charset=UTF-8\r\n";
                $message .= "Content-Transfer-Encoding: base64\r\n\r\n";
                $message .= chunk_split(base64_encode($render_body)) . "\r\n";

                // Ekleri ekle
                foreach ($attachments as $attachment) {
                    $content = file_get_contents($attachment['tmp_name']);
                    $filename = $attachment['name'];
                    $message .= "--" . $boundary . "\r\n";
                    $message .= "Content-Type: application/octet-stream; name=\"" . $filename . "\"\r\n";
                    $message .= "Content-Transfer-Encoding: base64\r\n";
                    $message .= "Content-Disposition: attachment; filename=\"" . $filename . "\"\r\n\r\n";
                    $message .= chunk_split(base64_encode($content)) . "\r\n";
                }

                $message .= "--" . $boundary . "--\r\n";

                // Maili Sent klasörüne kopyala
                imap_append($mailbox, $sent_folder, $header . "\r\n" . $message);
            }

            imap_close($mailbox);
        } catch (Exception $e) {
            error_log("Sent folder copy error: " . $e->getMessage());
            // Sent klasörüne kopyalama başarısız olsa bile mail gönderildi sayılır
        }

        return true;
    } catch (Exception $e) {
        error_log("Mail Exception: " . $e->getMessage());
        throw new Exception('Mail gönderilemedi: ' . $e->getMessage());
    }
}

/**
 * Taslak maili kaydeder
 */
function save_draft($data)
{
    global $db, $user, $mailbox_images;

    // Kullanıcı ID'sini al
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // USER_LOGIN değişkeni tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata fırlat
    if ($user_id == 0) {
        throw new Exception('Kullanıcı ID bulunamadı.');
    }

    // Gerekli alanların kontrolü
    if (empty($data['to']) && empty($data['subject']) && empty($data['body']) && empty($data['cc']) && empty($data['bcc'])) {
        // Hiçbir alan doldurulmamışsa kaydetmeye gerek yok
        return true;
    }

    $attachments = array();

    // Dosya eklerini işle ve geçici dizine kaydet
    if (!empty($_FILES['attachments'])) {
        foreach ($_FILES['attachments']['tmp_name'] as $key => $tmp_name) {
            if ($_FILES['attachments']['error'][$key] === UPLOAD_ERR_OK) {
                $filename = $_FILES['attachments']['name'][$key];
                $temp_dir = $mailbox_images . '/drafts/' . date('Y/m/d');

                if (!is_dir($temp_dir)) {
                    mkdir($temp_dir, 0777, true);
                }

                $new_path = $temp_dir . '/' . uniqid() . '_' . $filename;

                if (move_uploaded_file($tmp_name, $new_path)) {
                    $attachments[] = array(
                        'filename' => $filename,
                        'path' => $new_path
                    );
                }
            }
        }
    }

    // Mevcut taslağın eklerini koru
    if (!empty($data['draft_id'])) {
        $draft = get_draft((int) $data['draft_id']);
        if ($draft && !empty($draft->attachments)) {
            $existing_attachments = json_decode($draft->attachments, true);
            $attachments = array_merge($existing_attachments, $attachments);
        }
    }

    // Aktif hesap ID'sini al
    $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : 'default';

    // SQL sorgusunu hazırla
    if (!empty($data['draft_id'])) {
        // Güncelleme sorgusu
        $sql = "UPDATE mail_drafts
                SET to_email = '" . $db->escape($data['to']) . "',
                    cc = '" . $db->escape(isset($data['cc']) ? $data['cc'] : '') . "',
                    bcc = '" . $db->escape(isset($data['bcc']) ? $data['bcc'] : '') . "',
                    subject = '" . $db->escape($data['subject']) . "',
                    body = '" . $db->escape($data['body']) . "',
                    account_id = '" . $db->escape($account_id) . "',
                    attachments = " . (!empty($attachments) ? "'" . $db->escape(json_encode($attachments)) . "'" : "NULL") . "
                WHERE id = " . (int) $data['draft_id'] . "
                AND user_id = " . (int) $user_id;
    } else {
        // Yeni kayıt sorgusu
        $sql = "INSERT INTO mail_drafts (user_id, account_id, to_email, cc, bcc, subject, body, attachments, created_at)
                VALUES (" . (int) $user_id . ",
                        '" . $db->escape($account_id) . "',
                        '" . $db->escape($data['to']) . "',
                        '" . $db->escape(isset($data['cc']) ? $data['cc'] : '') . "',
                        '" . $db->escape(isset($data['bcc']) ? $data['bcc'] : '') . "',
                        '" . $db->escape($data['subject']) . "',
                        '" . $db->escape($data['body']) . "',
                        " . (!empty($attachments) ? "'" . $db->escape(json_encode($attachments)) . "'" : "NULL") . ",
                        NOW())";
    }

    // Sorguyu çalıştır

    return $db->query($sql);
}

/**
 * Taslak maili getirir
 */
function get_draft($id)
{
    global $db, $user;

    // Kullanıcı ID'sini al
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // USER_LOGIN değişkeni tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata fırlat
    if ($user_id == 0) {
        throw new Exception('Kullanıcı ID bulunamadı.');
    }

    return $db->get_row("SELECT * FROM mail_drafts WHERE id = " . (int) $id . " AND user_id = " . (int) $user_id);
}

/**
 * Kullanıcının tüm taslaklarını getirir
 */
function get_drafts($page = 1, $per_page = 20, $account_id = null)
{
    global $db, $user;

    // Kullanıcı ID'sini al
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // USER_LOGIN değişkeni tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata fırlat
    if ($user_id == 0) {
        throw new Exception('Kullanıcı ID bulunamadı.');
    }

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    $start = ($page - 1) * $per_page;

    // Hesaba göre filtreleme yap
    $account_filter = "";
    if ($account_id) {
        $account_filter = " AND account_id = '" . $db->escape($account_id) . "'";
    }

    $drafts = $db->get_results(
        "
        SELECT *
        FROM mail_drafts
        WHERE user_id = " . (int) $user_id . "
        " . $account_filter . "
        ORDER BY created_at DESC
        LIMIT " . (int) $start . ", " . (int) $per_page
    );

    $total = $db->get_var(
        "
        SELECT COUNT(*)
        FROM mail_drafts
        WHERE user_id = " . (int) $user_id . "
        " . $account_filter
    );

    return array(
        'drafts' => $drafts,
        'total' => $total,
        'pages' => ceil($total / $per_page)
    );
}

/**
 * Taslak maili siler
 */
function delete_draft($id)
{
    global $db, $user;

    // Kullanıcı ID'sini al
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // USER_LOGIN değişkeni tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata fırlat
    if ($user_id == 0) {
        throw new Exception('Kullanıcı ID bulunamadı.');
    }

    $draft = get_draft($id);

    if ($draft) {
        // Ekleri sil
        if (!empty($draft->attachments)) {
            $attachments = json_decode($draft->attachments);
            foreach ($attachments as $attachment) {
                if (file_exists($attachment->path)) {
                    unlink($attachment->path);
                }
            }
        }

        // SQL sorgusu ile silme işlemi
        $sql = "DELETE FROM mail_drafts
                WHERE id = " . (int) $id . "
                AND user_id = " . (int) $user_id;

        return $db->query($sql);
    }

    return false;
}

/**
 * Binary içerikten MIME tipini tespit eder
 */
function detect_mime_from_binary_content($content)
{
    if (empty($content)) {
        return false;
    }

    // İlk birkaç byte'ı al
    $header = substr($content, 0, 20);
    $header_hex = bin2hex($header);

    error_log("Detecting MIME from header: " . $header_hex);

    // JPEG - FF D8 FF
    if (substr($header, 0, 3) === "\xFF\xD8\xFF") {
        error_log("Detected JPEG from header");
        return 'image/jpeg';
    }

    // PNG - 89 50 4E 47 0D 0A 1A 0A
    if (substr($header, 0, 8) === "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A") {
        error_log("Detected PNG from header");
        return 'image/png';
    }

    // GIF
    if (substr($header, 0, 6) === "GIF87a" || substr($header, 0, 6) === "GIF89a") {
        error_log("Detected GIF from header");
        return 'image/gif';
    }

    // PDF - 25 50 44 46
    if (substr($header, 0, 4) === "%PDF") {
        error_log("Detected PDF from header");
        return 'application/pdf';
    }

    // ZIP - 50 4B 03 04
    if (substr($header, 0, 4) === "PK\x03\x04" || substr($header, 0, 4) === "PK\x05\x06" || substr($header, 0, 4) === "PK\x07\x08") {
        error_log("Detected ZIP from header");
        return 'application/zip';
    }

    // Microsoft Office (DOCX, XLSX, PPTX)
    if (substr($header, 0, 4) === "PK\x03\x04") {
        // ZIP tabanlı Office dosyaları için daha detaylı kontrol
        if (strpos($content, 'word/') !== false) {
            error_log("Detected DOCX from content");
            return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        } elseif (strpos($content, 'xl/') !== false) {
            error_log("Detected XLSX from content");
            return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        } elseif (strpos($content, 'ppt/') !== false) {
            error_log("Detected PPTX from content");
            return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
        }
    }

    // Old Microsoft Office - D0 CF 11 E0 A1 B1 1A E1
    if (substr($header, 0, 8) === "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1") {
        error_log("Detected old Office format from header");
        return 'application/msword'; // veya excel, powerpoint - daha detaylı analiz gerekir
    }

    // Binary olmayan içerik kontrolü - sadece son çare olarak
    $is_binary = false;
    for ($i = 0; $i < min(1000, strlen($content)); $i++) {
        $byte = ord($content[$i]);
        if ($byte < 32 && $byte !== 9 && $byte !== 10 && $byte !== 13) {
            $is_binary = true;
            break;
        }
    }

    if (!$is_binary) {
        error_log("Content appears to be text");
        return 'text/plain';
    }

    error_log("Could not detect MIME type from binary content");
    return false;
}

/**
 * MIME tipinden dosya uzantısını döndürür
 */
function get_extension_from_mime_type($mime_type)
{
    $mime_map = [
        'image/jpeg' => 'jpg',
        'image/jpg' => 'jpg',
        'image/png' => 'png',
        'image/gif' => 'gif',
        'image/bmp' => 'bmp',
        'image/webp' => 'webp',
        'application/pdf' => 'pdf',
        'application/zip' => 'zip',
        'application/x-rar-compressed' => 'rar',
        'application/x-7z-compressed' => '7z',
        'application/msword' => 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
        'application/vnd.ms-excel' => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
        'application/vnd.ms-powerpoint' => 'ppt',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
        'text/plain' => 'txt',
        'text/html' => 'html',
        'text/css' => 'css',
        'text/javascript' => 'js',
        'application/json' => 'json',
        'application/xml' => 'xml',
        'audio/mpeg' => 'mp3',
        'audio/wav' => 'wav',
        'video/mp4' => 'mp4',
        'video/avi' => 'avi',
        'video/quicktime' => 'mov',
    ];

    return isset($mime_map[$mime_type]) ? $mime_map[$mime_type] : false;
}

/**
 * Dosya uzantısından MIME tipini döndürür
 */
function get_mime_from_extension($extension)
{
    $extension_map = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'bmp' => 'image/bmp',
        'webp' => 'image/webp',
        'pdf' => 'application/pdf',
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
        '7z' => 'application/x-7z-compressed',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt' => 'application/vnd.ms-powerpoint',
        'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'txt' => 'text/plain',
        'html' => 'text/html',
        'htm' => 'text/html',
        'css' => 'text/css',
        'js' => 'text/javascript',
        'json' => 'application/json',
        'xml' => 'application/xml',
        'mp3' => 'audio/mpeg',
        'wav' => 'audio/wav',
        'mp4' => 'video/mp4',
        'avi' => 'video/avi',
        'mov' => 'video/quicktime',
    ];

    return isset($extension_map[$extension]) ? $extension_map[$extension] : false;
}

/**
 * Apple Mail boundary'sinden gerçek dosya içeriğini çıkarır
 */
function parse_apple_mail_boundary($content, $target_filename)
{
    try {
        // Boundary'yi bul
        $boundary = null;
        if (preg_match('/--Apple-Mail=([A-F0-9\-]+)/', $content, $matches)) {
            $boundary = '--Apple-Mail=' . $matches[1];
        } else {
            error_log("Could not find Apple Mail boundary");
            return false;
        }

        error_log("Found boundary: $boundary");

        // Content'i boundary'lere göre böl
        $parts = explode($boundary, $content);

        foreach ($parts as $part_index => $part) {
            $part = trim($part);
            if (empty($part) || $part === '--') {
                continue;
            }

            error_log("Processing part $part_index, length: " . strlen($part));

            // Header ve body'yi ayır
            $header_end = strpos($part, "\r\n\r\n");
            if ($header_end === false) {
                $header_end = strpos($part, "\n\n");
            }

            if ($header_end === false) {
                continue;
            }

            $headers = substr($part, 0, $header_end);
            $body = substr($part, $header_end + 4);

            error_log("Part headers: " . substr($headers, 0, 200));

            // Content-Disposition header'ını kontrol et
            if (preg_match('/Content-Disposition:\s*attachment[^;]*;\s*filename[*]?=([^;\r\n]+)/i', $headers, $filename_matches)) {
                $found_filename = trim($filename_matches[1], '"\'');

                // RFC 2231 encoding kontrolü
                if (strpos($found_filename, '*=') !== false) {
                    $found_filename = urldecode(preg_replace('/^[^\']*\'[^\']*\'/', '', $found_filename));
                }

                error_log("Found attachment filename: $found_filename");

                // Filename eşleşmesi kontrol et (case insensitive ve daha esnek)
                $target_clean = strtolower(str_replace([' ', '+', '%20', '.jpeg', '.jpg', '.png', '.pdf'], '', $target_filename));
                $found_clean = strtolower(str_replace([' ', '+', '%20', '.jpeg', '.jpg', '.png', '.pdf'], '', $found_filename));

                error_log("Comparing filenames - Target: '$target_clean', Found: '$found_clean'");

                if (
                    strpos($found_clean, $target_clean) !== false ||
                    strpos($target_clean, $found_clean) !== false ||
                    similar_text($target_clean, $found_clean) > (strlen($target_clean) * 0.7)
                ) {
                    error_log("Filename match found, extracting content...");

                    // Content-Transfer-Encoding'i kontrol et
                    $encoding = 'binary';
                    if (preg_match('/Content-Transfer-Encoding:\s*([^\r\n]+)/i', $headers, $encoding_matches)) {
                        $encoding = strtolower(trim($encoding_matches[1]));
                    }

                    error_log("Content encoding: $encoding");

                    // Encoding'e göre decode et
                    switch ($encoding) {
                        case 'base64':
                            $decoded = base64_decode($body, true);
                            if ($decoded !== false) {
                                error_log("Base64 decode successful, length: " . strlen($decoded));
                                return $decoded;
                            }
                            break;

                        case 'quoted-printable':
                            $decoded = quoted_printable_decode($body);
                            error_log("Quoted-printable decode, length: " . strlen($decoded));
                            return $decoded;

                        case 'binary':
                        case '8bit':
                        case '7bit':
                        default:
                            // Binary olarak döndür ama önce base64 olup olmadığını kontrol et
                            if (preg_match('/^[a-zA-Z0-9\/\r\n\s+]*={0,2}$/', trim($body))) {
                                $decoded = base64_decode(preg_replace('/\s+/', '', $body), true);
                                if ($decoded !== false && strlen($decoded) > 0) {
                                    error_log("Hidden base64 detected and decoded, length: " . strlen($decoded));
                                    return $decoded;
                                }
                            }
                            error_log("Returning raw body, length: " . strlen($body));
                            return $body;
                    }
                }
            }
        }

        error_log("No matching attachment found in multipart content");
        return false;
    } catch (Exception $e) {
        error_log("Error parsing Apple Mail boundary: " . $e->getMessage());
        return false;
    }
}

/**
 * Mail ekini indirir
 */
function download_attachment($mailbox, $email_id, $part_num)
{
    try {
        // Mail yapısını al
        $structure = imap_fetchstructure($mailbox, $email_id);

        // Dosya adını ve MIME tipini bul
        $filename = '';
        $mime_type = 'application/octet-stream';
        $encoding = 0;

        // Part numarasına göre yapıyı analiz et
        $part = null;
        if (strpos($part_num, '.') === false) {
            // Ana parça (1, 2, 3...)
            if (isset($structure->parts) && isset($structure->parts[$part_num - 1])) {
                $part = $structure->parts[$part_num - 1];
            }
        } else {
            // Alt parça (1.1, 1.2, 2.1...)
            $part_path = explode('.', $part_num);
            $current_part = $structure;

            foreach ($part_path as $path_index) {
                if (isset($current_part->parts) && isset($current_part->parts[$path_index - 1])) {
                    $current_part = $current_part->parts[$path_index - 1];
                } else {
                    $current_part = null;
                    break;
                }
            }
            $part = $current_part;
        }

        // Part bilgilerini al
        if ($part) {
            $encoding = $part->encoding;

            // MIME tipini oluştur
            $primary_types = ['text', 'multipart', 'message', 'application', 'audio', 'image', 'video', 'other'];
            $primary_type = isset($primary_types[$part->type]) ? $primary_types[$part->type] : 'application';
            $mime_type = $primary_type . '/' . strtolower($part->subtype);

            // Dosya adını parametrelerden al
            $parameters_to_check = [];
            if (isset($part->dparameters)) {
                $parameters_to_check = array_merge($parameters_to_check, $part->dparameters);
            }
            if (isset($part->parameters)) {
                $parameters_to_check = array_merge($parameters_to_check, $part->parameters);
            }

            foreach ($parameters_to_check as $param) {
                $attr = strtolower($param->attribute);
                if ($attr == 'filename' || $attr == 'name') {
                    $filename = $param->value;

                    // RFC 2047 encoded filename'i decode et
                    if (strpos($filename, '=?') !== false) {
                        $decoded_headers = imap_mime_header_decode($filename);
                        if (!empty($decoded_headers)) {
                            $filename = $decoded_headers[0]->text;
                        }
                    }

                    // RFC 2231 encoded filename'i decode et
                    if (strpos($filename, '*=') !== false) {
                        $filename = urldecode(preg_replace('/^[^\']*\'[^\']*\'/', '', $filename));
                    }
                    break;
                }
            }
        }

        // URL'den gelen filename parametresini kontrol et (öncelik ver)
        if (isset($_GET['filename']) && !empty($_GET['filename'])) {
            $filename = urldecode($_GET['filename']);
        }

        // Hala filename yoksa varsayılan isim ver
        if (empty($filename)) {
            $extension = '';
            if ($part && isset($part->subtype)) {
                $extension = '.' . strtolower($part->subtype);
            }
            $filename = 'attachment_' . $part_num . $extension;
        }

        // Dosya adını güvenli hale getir
        $filename = str_replace(['\\', '/', ':', '*', '?', '"', '<', '>', '|'], '_', $filename);

        // İçeriği al
        $content = imap_fetchbody($mailbox, $email_id, $part_num);

        // Debug bilgisi
        error_log("Download attachment - Part: $part_num, Encoding: $encoding, MIME: $mime_type, Filename: $filename");
        error_log("Content length before decode: " . strlen($content));
        error_log("Content sample (first 100 chars): " . substr($content, 0, 100));

        // Apple Mail boundary kontrolü (forward edilen mailler için)
        if (strpos($content, '--Apple-Mail=') !== false || strpos($content, 'Content-Transfer-Encoding:') !== false) {
            error_log("Detected Apple Mail boundary, parsing multipart content...");
            $parsed_content = parse_apple_mail_boundary($content, $filename);
            if ($parsed_content !== false && strlen($parsed_content) > 0) {
                error_log("Successfully extracted content from Apple Mail boundary, new length: " . strlen($parsed_content));
                $content = $parsed_content;
                // Encoding'i sıfırla çünkü artık decode edilmiş content var
                $encoding = 0;
            } else {
                error_log("Failed to extract content from Apple Mail boundary, using original content");
                // Original content'i kullanmaya devam et
            }
        }

        // Encoding'e göre decode et - Forward edilen mailler için agresif yaklaşım
        $original_content = $content;
        $decode_attempts = [];

        switch ($encoding) {
            case 0: // 7BIT
            case 1: // 8BIT
            case 2: // BINARY
                // Forward edilen maillerde encoding yanlış olabiliyor, base64 kontrolü yap
                $trimmed = trim($content);
                error_log("7BIT/8BIT content check - length: " . strlen($trimmed));

                // Base64 pattern kontrolü - daha esnek
                if (strlen($trimmed) > 50 && preg_match('/^[a-zA-Z0-9\/\r\n\s+]*={0,2}$/', $trimmed)) {
                    error_log("Content looks like base64, attempting decode...");

                    // Çeşitli temizleme yöntemleri dene
                    $clean_attempts = [
                        'direct' => $trimmed,
                        'no_whitespace' => preg_replace('/\s+/', '', $trimmed),
                        'no_newlines' => str_replace(["\r", "\n"], '', $trimmed),
                        'no_spaces' => str_replace(' ', '', $trimmed)
                    ];

                    foreach ($clean_attempts as $method => $clean_content) {
                        $decoded = base64_decode($clean_content, true);
                        if ($decoded !== false && strlen($decoded) > 0) {
                            $content = $decoded;
                            error_log("Found hidden BASE64 in 7BIT/8BIT content using method: $method");
                            break 2;
                        }
                    }
                }
                break;

            case 3: // BASE64
                // Birden fazla decode yöntemi dene
                $decode_attempts = [
                    'direct' => base64_decode($content, true),
                    'trimmed' => base64_decode(trim($content), true),
                    'cleaned' => base64_decode(preg_replace('/\s+/', '', $content), true),
                    'line_cleaned' => base64_decode(str_replace(["\r", "\n", " ", "\t"], '', $content), true)
                ];

                foreach ($decode_attempts as $method => $decoded) {
                    if ($decoded !== false && strlen($decoded) > 0) {
                        $content = $decoded;
                        error_log("BASE64 decode successful with method: $method");
                        break;
                    }
                }

                // Hala başarısızsa, satır satır decode etmeyi dene
                if ($content === $original_content) {
                    $lines = explode("\n", $content);
                    $decoded_parts = [];
                    foreach ($lines as $line) {
                        $line = trim($line);
                        if (!empty($line)) {
                            $decoded_line = base64_decode($line, true);
                            if ($decoded_line !== false) {
                                $decoded_parts[] = $decoded_line;
                            }
                        }
                    }
                    if (!empty($decoded_parts)) {
                        $content = implode('', $decoded_parts);
                        error_log("BASE64 decode successful with line-by-line method");
                    }
                }
                break;

            case 4: // QUOTED-PRINTABLE
                $content = quoted_printable_decode($content);
                break;

            case 5: // OTHER
            default:
                // Agresif otomatik tespit
                $trimmed_content = trim($content);

                // 1. Base64 kontrolü - çeşitli yöntemlerle
                if (preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $trimmed_content)) {
                    $decode_attempts = [
                        'direct' => base64_decode($trimmed_content, true),
                        'cleaned' => base64_decode(preg_replace('/\s+/', '', $trimmed_content), true),
                        'line_cleaned' => base64_decode(str_replace(["\r", "\n", " ", "\t"], '', $trimmed_content), true)
                    ];

                    foreach ($decode_attempts as $method => $decoded) {
                        if ($decoded !== false && strlen($decoded) > 0) {
                            $content = $decoded;
                            error_log("Auto-detected BASE64 encoding with method: $method");
                            break 2;
                        }
                    }
                }

                // 2. Quoted-printable kontrolü
                if (strpos($content, '=') !== false && preg_match('/=[0-9A-F]{2}/', $content)) {
                    $content = quoted_printable_decode($content);
                    error_log("Auto-detected QUOTED-PRINTABLE encoding");
                    break;
                }

                // 3. UUencoding kontrolü
                if (strpos($content, 'begin ') === 0) {
                    $decoded = convert_uudecode($content);
                    if ($decoded !== false) {
                        $content = $decoded;
                        error_log("Auto-detected UU encoding");
                        break;
                    }
                }

                error_log("Unknown encoding: $encoding, content length: " . strlen($content));
        }

        // Son kontrol - eğer içerik hala base64 gibi görünüyorsa bir kez daha dene
        if ($content === $original_content && preg_match('/^[a-zA-Z0-9\/+=\s]+$/', $content)) {
            $final_attempt = base64_decode(preg_replace('/\s+/', '', $content), true);
            if ($final_attempt !== false && strlen($final_attempt) > 0) {
                $content = $final_attempt;
                error_log("Final BASE64 decode attempt successful");
            }
        }

        error_log("Content length after decode: " . strlen($content));

        // İçerik boşsa hata
        if (empty($content)) {
            throw new Exception("Decoded content is empty for part $part_num");
        }

        // Eğer content hala Apple Mail boundary içeriyorsa ve çok büyükse,
        // muhtemelen parsing başarısız olmuş, raw olarak kullanmayı dene
        if (strlen($content) > 1000000 && strpos($content, '--Apple-Mail=') !== false) {
            error_log("Content still contains Apple Mail boundary and is very large, might be parsing issue");
            // Bu durumda dosyayı raw olarak indirmeye çalış ama MIME tipini extension'dan belirle
            $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $mime_from_extension = get_mime_from_extension($file_extension);
            if ($mime_from_extension) {
                $mime_type = $mime_from_extension;
                error_log("Using MIME from extension for large content: $file_extension -> $mime_from_extension");
            }
        }

        // Dosya header'ını kontrol et ve MIME tipini doğrula
        $file_header = substr($content, 0, 20);
        $detected_mime = detect_mime_from_binary_content($content);

        error_log("Original MIME: $mime_type, Detected MIME: " . ($detected_mime ?: 'none'));
        error_log("File header (first 10 bytes hex): " . bin2hex(substr($content, 0, 10)));

        // Eğer detected MIME varsa ve farklıysa güncelle
        if ($detected_mime && $detected_mime !== $mime_type) {
            error_log("MIME type mismatch - Original: $mime_type, Detected: $detected_mime");
            $mime_type = $detected_mime;

            // Filename extension'ını da güncelle
            $detected_extension = get_extension_from_mime_type($detected_mime);
            if ($detected_extension) {
                $filename_without_ext = pathinfo($filename, PATHINFO_FILENAME);
                $filename = $filename_without_ext . '.' . $detected_extension;
                error_log("Updated filename to: $filename");
            }
        }

        // Eğer hala text/plain ise ve dosya extension'ı farklıysa, extension'dan MIME belirle
        if ($mime_type === 'text/plain' || $mime_type === 'application/octet-stream') {
            $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $mime_from_extension = get_mime_from_extension($file_extension);
            if ($mime_from_extension) {
                error_log("Using MIME from extension: $file_extension -> $mime_from_extension");
                $mime_type = $mime_from_extension;
            }
        }

        // JPEG dosyaları için özel kontrol
        if (strpos($mime_type, 'image/jpeg') !== false || strpos(strtolower($filename), '.jpg') !== false || strpos(strtolower($filename), '.jpeg') !== false) {
            // JPEG magic number kontrolü (FF D8 FF)
            $jpeg_header = substr($content, 0, 3);
            if ($jpeg_header !== "\xFF\xD8\xFF") {
                error_log("Invalid JPEG header detected, trying to fix...");

                // JPEG header'ını ara
                $jpeg_start = strpos($content, "\xFF\xD8\xFF");
                if ($jpeg_start !== false && $jpeg_start > 0) {
                    $content = substr($content, $jpeg_start);
                    error_log("Fixed JPEG by removing " . $jpeg_start . " bytes from start");
                }
            }
        }

        // Geçici dosya oluştur
        $temp_file = tempnam(sys_get_temp_dir(), 'mail_attachment_');
        if (!$temp_file) {
            throw new Exception("Could not create temporary file");
        }

        $bytes_written = file_put_contents($temp_file, $content);
        if ($bytes_written === false) {
            throw new Exception("Could not write to temporary file");
        }

        error_log("Written $bytes_written bytes to temp file: $temp_file");

        return [
            'filename' => $filename,
            'temp_file' => $temp_file,
            'mime_type' => $mime_type
        ];
    } catch (Exception $e) {
        error_log('Ek indirme hatası: ' . $e->getMessage());
        error_log('Stack trace: ' . $e->getTraceAsString());
        return false;
    }
}

/**
 * Giden mailleri listeler
 */
function get_sent_emails($mailbox = null, $page = 1, $per_page = 20, $account_id = null)
{
    global $mailbox_config;

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // Hesap bilgilerini al
    $account = null;
    if ($account_id === 'default' || $account_id === null) {
        $account = $mailbox_config['default'];
    } else {
        // Belirtilen hesabı bul
        foreach ($mailbox_config['accounts'] as $acc) {
            if ($acc['id'] == $account_id) {
                $account = $acc;
                break;
            }
        }

        // Hesap bulunamadıysa varsayılanı kullan
        if ($account === null) {
            $account = $mailbox_config['default'];
        }
    }

    // Sent klasörüne geç
    try {
        $close_connection = false;

        // Eğer mailbox parametresi verilmemişse, yeni bir bağlantı aç
        if (!$mailbox) {
            $mailbox = mailbox_connect($account_id);
            $close_connection = true;
        }

        // IMAP bağlantı dizesini oluştur
        $imap_string = parse_url($account['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';

        // Mevcut klasörleri listele
        $folders = imap_list($mailbox, $imap_string, '*');

        // Debug için klasörleri logla
        error_log('Available folders: ' . print_r($folders, true));

        // Sent klasörünü bul
        $sent_folder = null;
        foreach ($folders as $folder) {
            if (
                stripos($folder, 'Sent') !== false ||
                stripos($folder, 'Gönderilmiş') !== false ||
                stripos($folder, 'Giden') !== false
            ) {
                $sent_folder = $folder;
                break;
            }
        }

        if (!$sent_folder) {
            throw new Exception('Giden kutusu klasörü bulunamadı.');
        }

        // Giden kutusuna bağlan
        if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        $mailbox = imap_open($sent_folder, $account['username'], $account['password']);

        if (!$mailbox) {
            throw new Exception('Giden kutusu klasörüne erişilemiyor: ' . imap_last_error());
        }

        $start = ($page - 1) * $per_page;
        $emails = array();

        $total_emails = imap_num_msg($mailbox);

        // Mail yoksa boş dizi döndür
        if ($total_emails <= 0) {
            // Bağlantıyı kapat (eğer biz açtıysak)
            if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                @imap_close($mailbox);
            }

            return array(
                'emails' => array(),
                'total' => 0,
                'pages' => 0
            );
        }

        $end = max(1, $total_emails - $start);
        $start = max(1, $total_emails - ($start + $per_page));

        for ($i = $end; $i >= $start; $i--) {
            $header = imap_headerinfo($mailbox, $i);
            $emails[] = array(
                'id' => $i,
                'subject' => decode_mime_string($header->subject),
                'to' => isset($header->to[0]) ? decode_mime_string($header->to[0]->mailbox . '@' . $header->to[0]->host) : '',
                'date' => date('Y-m-d H:i', strtotime($header->date))
            );
        }

        // Sonuçları hazırla
        $result = array(
            'emails' => $emails,
            'total' => $total_emails,
            'pages' => ceil($total_emails / $per_page)
        );

        // Bağlantıyı kapat (eğer biz açtıysak)
        if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        return $result;
    } catch (Exception $e) {
        // Hata durumunda bağlantıyı kapat (eğer biz açtıysak)
        if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        throw new Exception('IMAP bağlantı hatası: ' . $e->getMessage());
    }
}

/**
 * Taslaktaki bir eki siler
 */
function delete_draft_attachment($draft_id, $attachment_index)
{
    global $db, $user;

    // Kullanıcı ID'sini al
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // USER_LOGIN değişkeni tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata fırlat
    if ($user_id == 0) {
        throw new Exception('Kullanıcı ID bulunamadı.');
    }

    $draft = get_draft($draft_id);
    if (!$draft || empty($draft->attachments)) {
        return false;
    }

    $attachments = json_decode($draft->attachments);
    if (!isset($attachments[$attachment_index])) {
        return false;
    }

    // Dosyayı fiziksel olarak sil
    $attachment = $attachments[$attachment_index];
    if (file_exists($attachment->path)) {
        unlink($attachment->path);
    }

    // Eki diziden kaldır
    array_splice($attachments, $attachment_index, 1);

    // Veritabanını güncelle
    $sql = "UPDATE mail_drafts
            SET attachments = " . (!empty($attachments) ? "'" . $db->escape(json_encode($attachments)) . "'" : "NULL") . "
            WHERE id = " . (int) $draft_id . "
            AND user_id = " . (int) $user_id;

    return $db->query($sql);
}

/**
 * Okunmamış mail sayısını getirir
 */
function get_unread_count($mailbox = null): int
{
    try {
        if (!$mailbox)
            $mailbox = mailbox_connect();
        $total = imap_num_msg($mailbox);
        $unread = 0;

        for ($i = 1; $i <= $total; $i++) {
            $header = imap_headerinfo($mailbox, $i);
            if (trim($header->Unseen) == 'U') {
                $unread++;
            }
        }

        imap_close($mailbox);
        return $unread;
    } catch (Exception $e) {
        error_log("Unread count error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Mail siler
 * @param resource $mailbox IMAP bağlantısı
 * @param int $email_id Mail ID
 * @param bool $permanent Kalıcı silme işlemi mi?
 * @param string $source_folder Kaynak klasör (INBOX, SENT, TRASH)
 * @return bool İşlem başarılı mı?
 */
function delete_email($mailbox, $email_id, $permanent = false, $source_folder = '')
{
    try {
        global $mailbox_config, $db, $user;

        // Aktif hesap ID'sini al
        $account_id = isset($_SESSION['active_mail_account']) ? $_SESSION['active_mail_account'] : 'default';

        // Kaynak klasörü belirle
        if (empty($source_folder)) {
            $source_folder = 'INBOX';
            if (isset($_GET['folder'])) {
                $source_folder = $_GET['folder'];
            } elseif (isset($_POST['folder'])) {
                $source_folder = $_POST['folder'];
            }
        }

        // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
        $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

        // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
        if ($user_id == 0 && isset($user->id)) {
            $user_id = $user->id;
        }

        // Eğer kalıcı silme ise, doğrudan sil
        if ($permanent) {
            // Maili kalıcı olarak sil
            if (!imap_delete($mailbox, $email_id)) {
                throw new Exception('Mail silinemedi.');
            }

            // Değişiklikleri uygula
            if (!imap_expunge($mailbox)) {
                throw new Exception('Değişiklikler uygulanamadı.');
            }

            // Etiket ilişkilerini sil
            $db->query("DELETE FROM mail_label_relations
                       WHERE user_id = " . (int) $user_id . "
                       AND email_id = " . (int) $email_id . "
                       AND account_id = '" . $db->escape($account_id) . "'");

            return true;
        }

        // IMAP bağlantı dizesini oluştur
        $imap_string = parse_url($mailbox_config['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';

        // Trash klasörünü bul
        $folders = imap_list($mailbox, $imap_string, '*');
        $trash_folder = null;
        foreach ($folders as $folder) {
            if (
                stripos($folder, 'Trash') !== false ||
                stripos($folder, 'Çöp') !== false ||
                stripos($folder, 'Deleted') !== false
            ) {
                $trash_folder = $folder;
                break;
            }
        }

        if (!$trash_folder) {
            throw new Exception('Çöp kutusu klasörü bulunamadı.');
        }

        // Mail içeriğini al
        $header = imap_fetchheader($mailbox, $email_id);
        $body = imap_body($mailbox, $email_id);

        // Maili Trash klasörüne kopyala
        if (!imap_append($mailbox, $trash_folder, $header . "\r\n" . $body)) {
            throw new Exception('Mail çöp kutusuna taşınamadı.');
        }

        // Etiket ilişkilerini güncelle - folder alanını 'trash' olarak işaretle
        // source_folder parametresi zaten yukarıda belirlendi

        // Etiket ilişkilerini geçici olarak sakla
        $email_labels = $db->get_results("SELECT * FROM mail_label_relations
                                        WHERE user_id = " . (int) $user_id . "
                                        AND email_id = " . (int) $email_id . "
                                        AND account_id = '" . $db->escape($account_id) . "'
                                        AND folder = '" . $db->escape($source_folder) . "'");

        // Önce eski etiket ilişkilerini sil
        $db->query("DELETE FROM mail_label_relations
                   WHERE user_id = " . (int) $user_id . "
                   AND email_id = " . (int) $email_id . "
                   AND account_id = '" . $db->escape($account_id) . "'
                   AND folder = '" . $db->escape($source_folder) . "'");

        // Orijinal maili sil
        if (!imap_delete($mailbox, $email_id)) {
            throw new Exception('Orijinal mail silinemedi.');
        }

        // Değişiklikleri uygula
        if (!imap_expunge($mailbox)) {
            throw new Exception('Değişiklikler uygulanamadı.');
        }

        // Eğer etiket ilişkileri varsa, çöp kutusundaki yeni mail ID'sini bul ve etiketleri yeniden ekle
        if (!empty($email_labels)) {
            // Hesap bilgilerini al
            $account = null;
            if ($account_id === 'default' || $account_id === null) {
                $account = $mailbox_config['default'];
            } else {
                // Belirtilen hesabı bul
                foreach ($mailbox_config['accounts'] as $acc) {
                    if ($acc['id'] == $account_id) {
                        $account = $acc;
                        break;
                    }
                }

                // Hesap bulunamadıysa varsayılanı kullan
                if ($account === null) {
                    $account = $mailbox_config['default'];
                }
            }

            // Çöp kutusuna bağlan
            $trash_mailbox = imap_open($trash_folder, $account['username'], $account['password']);

            if ($trash_mailbox) {
                // Çöp kutusundaki mail sayısını al
                $total_emails = imap_num_msg($trash_mailbox);

                // En son eklenen mail, en son silinen mail olacaktır
                if ($total_emails > 0) {
                    $new_email_id = $total_emails; // Çöp kutusundaki son mail ID'si

                    // Etiketleri yeni ID ile ekle
                    foreach ($email_labels as $label) {
                        $sql = "INSERT INTO mail_label_relations (user_id, label_id, email_id, account_id, folder, created_at)
                                VALUES (" . (int) $user_id . ",
                                        " . (int) $label->label_id . ",
                                        " . (int) $new_email_id . ",
                                        '" . $db->escape($account_id) . "',
                                        'trash',
                                        NOW())";
                        $db->query($sql);
                    }
                }

                // Çöp kutusu bağlantısını kapat
                imap_close($trash_mailbox);
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("Mail silme hatası: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Çöp kutusundaki mailleri listeler
 */
function get_trash_emails($mailbox = null, $page = 1, $per_page = 20, $account_id = null)
{
    global $mailbox_config;

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // Hesap bilgilerini al
    $account = null;
    if ($account_id === 'default' || $account_id === null) {
        $account = $mailbox_config['default'];
    } else {
        // Belirtilen hesabı bul
        foreach ($mailbox_config['accounts'] as $acc) {
            if ($acc['id'] == $account_id) {
                $account = $acc;
                break;
            }
        }

        // Hesap bulunamadıysa varsayılanı kullan
        if ($account === null) {
            $account = $mailbox_config['default'];
        }
    }

    try {
        $close_connection = false;

        // Eğer mailbox parametresi verilmemişse, yeni bir bağlantı aç
        if (!$mailbox) {
            $mailbox = mailbox_connect($account_id);
            $close_connection = true;
        }

        // Çöp kutusu klasörünü bul
        $imap_string = parse_url($account['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';
        $folders = imap_list($mailbox, $imap_string, '*');
        $trash_folder = null;

        foreach ($folders as $folder) {
            if (
                stripos($folder, 'Trash') !== false ||
                stripos($folder, 'Çöp') !== false ||
                stripos($folder, 'Deleted') !== false
            ) {
                $trash_folder = $folder;
                break;
            }
        }

        if (!$trash_folder) {
            // Bağlantıyı kapat (eğer biz açtıysak)
            if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                @imap_close($mailbox);
            }

            return array(
                'emails' => array(),
                'total' => 0,
                'pages' => 0
            );
        }

        // Mevcut bağlantıyı kapat
        if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            try {
                @imap_close($mailbox);
            } catch (\Throwable $e) {
                // Bağlantı zaten kapalı olabilir, hatayı yok say
                error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
            }
        }

        // Çöp kutusuna bağlan
        $mailbox = imap_open($trash_folder, $account['username'], $account['password']);

        if (!$mailbox) {
            error_log("Çöp kutusuna bağlanma hatası: " . imap_last_error());
            return array(
                'emails' => array(),
                'total' => 0,
                'pages' => 0
            );
        }

        $start = ($page - 1) * $per_page;
        $emails = array();

        $total_emails = imap_num_msg($mailbox);

        // Mail yoksa boş dizi döndür
        if ($total_emails <= 0) {
            return array(
                'emails' => array(),
                'total' => 0,
                'pages' => 0,
                'mailbox' => $mailbox
            );
        }

        $end = max(1, $total_emails - $start);
        $start = max(1, $total_emails - ($start + $per_page));

        for ($i = $end; $i >= $start; $i--) {
            $header = imap_headerinfo($mailbox, $i);
            // Okunma durumunu kontrol et
            $seen = 1; // Varsayılan olarak okunmuş

            // Unseen bayrağını kontrol et
            if (isset($header->Unseen) && trim($header->Unseen) == 'U') {
                $seen = 0;
            }

            // Mail bayraklarını kontrol et
            $flags = imap_fetch_overview($mailbox, $i);
            if (!empty($flags) && isset($flags[0])) {
                if (!$flags[0]->seen) {
                    $seen = 0;
                }
            }

            $emails[] = array(
                'id' => $i,
                'subject' => decode_mime_string($header->subject),
                'from' => decode_mime_string($header->from[0]->mailbox . '@' . $header->from[0]->host),
                'date' => date('Y-m-d H:i', strtotime($header->date)),
                'seen' => $seen
            );
        }

        return array(
            'emails' => $emails,
            'total' => $total_emails,
            'pages' => ceil($total_emails / $per_page),
            'mailbox' => $mailbox
        );
    } catch (Exception $e) {
        // Hata durumunda bağlantıyı kapat (eğer biz açtıysak)
        if ($close_connection && is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            @imap_close($mailbox);
        }

        error_log("Çöp kutusu mailleri alınamadı: " . $e->getMessage());
        return array(
            'emails' => array(),
            'total' => 0,
            'pages' => 0
        );
    }
}

/**
 * Çöp kutusundaki tüm mailleri kalıcı olarak siler
 */
function empty_trash($mailbox, $account_id = null)
{
    try {
        global $mailbox_config, $db, $user;

        // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
        $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

        // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
        if ($user_id == 0 && isset($user->id)) {
            $user_id = $user->id;
        }

        // Hesap ID belirtilmemişse, aktif hesabı kullan
        if ($account_id === null && isset($_SESSION['active_mail_account'])) {
            $account_id = $_SESSION['active_mail_account'];
        }

        // Hesap bilgilerini al
        $account = null;
        if ($account_id === 'default' || $account_id === null) {
            $account = $mailbox_config['default'];
        } else {
            // Belirtilen hesabı bul
            foreach ($mailbox_config['accounts'] as $acc) {
                if ($acc['id'] == $account_id) {
                    $account = $acc;
                    break;
                }
            }

            // Hesap bulunamadıysa varsayılanı kullan
            if ($account === null) {
                $account = $mailbox_config['default'];
            }
        }

        // IMAP bağlantı dizesini oluştur
        $imap_string = parse_url($account['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';

        // Çöp kutusu klasörünü bul
        $folders = imap_list($mailbox, $imap_string, '*');
        $trash_folder = null;

        foreach ($folders as $folder) {
            if (
                stripos($folder, 'Trash') !== false ||
                stripos($folder, 'Çöp') !== false ||
                stripos($folder, 'Deleted') !== false
            ) {
                $trash_folder = $folder;
                break;
            }
        }

        if (!$trash_folder) {
            throw new Exception('Çöp kutusu klasörü bulunamadı.');
        }

        // Mevcut bağlantıyı kapat
        if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
            try {
                @imap_close($mailbox);
            } catch (\Throwable $e) {
                // Bağlantı zaten kapalı olabilir, hatayı yok say
                error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
            }
        }

        // Çöp kutusuna bağlan
        $mailbox = imap_open($trash_folder, $account['username'], $account['password']);

        if (!$mailbox) {
            throw new Exception('Çöp kutusu klasörüne erişilemiyor: ' . imap_last_error());
        }

        // Mail sayısını al
        $total_emails = imap_num_msg($mailbox);

        if ($total_emails == 0) {
            return true; // Silinecek mail yok
        }

        // Tüm mailleri kalıcı olarak sil
        for ($i = 1; $i <= $total_emails; $i++) {
            try {
                // Maili kalıcı olarak sil
                imap_delete($mailbox, $i);
            } catch (Exception $e) {
                error_log("Çöp kutusundaki mail silme hatası (ID: $i): " . $e->getMessage());
                // Hata olsa bile devam et
                continue;
            }
        }

        // Değişiklikleri uygula
        if (!imap_expunge($mailbox)) {
            throw new Exception('Değişiklikler uygulanamadı.');
        }

        // Çöp kutusundaki tüm etiket ilişkilerini sil
        $db->query("DELETE FROM mail_label_relations
                   WHERE user_id = " . (int) $user_id . "
                   AND account_id = '" . $db->escape($account_id) . "'
                   AND folder = 'trash'");

        return true;
    } catch (Exception $e) {
        error_log("Çöp kutusunu boşaltma hatası: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Tüm mailleri siler
 */
function delete_all_emails($mailbox, $folder = 'INBOX', $account_id = null)
{
    try {
        global $mailbox_config, $db, $user;

        // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
        $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

        // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
        if ($user_id == 0 && isset($user->id)) {
            $user_id = $user->id;
        }

        // Etiket ilişkilerini saklamak için dizi
        $email_labels_to_restore = array();

        // Hesap ID belirtilmemişse, aktif hesabı kullan
        if ($account_id === null && isset($_SESSION['active_mail_account'])) {
            $account_id = $_SESSION['active_mail_account'];
        }

        // Hesap bilgilerini al
        $account = null;
        if ($account_id === 'default' || $account_id === null) {
            $account = $mailbox_config['default'];
        } else {
            // Belirtilen hesabı bul
            foreach ($mailbox_config['accounts'] as $acc) {
                if ($acc['id'] == $account_id) {
                    $account = $acc;
                    break;
                }
            }

            // Hesap bulunamadıysa varsayılanı kullan
            if ($account === null) {
                $account = $mailbox_config['default'];
            }
        }

        // IMAP bağlantı dizesini oluştur
        $imap_string = parse_url($account['imap_host'], PHP_URL_HOST) . ':993/imap/ssl/novalidate-cert}';

        // Trash klasörünü bul
        $folders = imap_list($mailbox, $imap_string, '*');
        $trash_folder = null;
        foreach ($folders as $folder_name) {
            if (
                stripos($folder_name, 'Trash') !== false ||
                stripos($folder_name, 'Çöp') !== false ||
                stripos($folder_name, 'Deleted') !== false
            ) {
                $trash_folder = $folder_name;
                break;
            }
        }

        if (!$trash_folder) {
            throw new Exception('Çöp kutusu klasörü bulunamadı.');
        }

        // Kaynak klasörü belirle
        $source_folder = null;

        // Eğer folder parametresi 'INBOX' değilse, ilgili klasörü bul
        if ($folder != 'INBOX') {
            foreach ($folders as $folder_path) {
                if (
                    ($folder == 'Sent' && (stripos($folder_path, 'Sent') !== false || stripos($folder_path, 'Gönderilmiş') !== false)) ||
                    ($folder == 'Trash' && (stripos($folder_path, 'Trash') !== false || stripos($folder_path, 'Çöp') !== false || stripos($folder_path, 'Deleted') !== false))
                ) {
                    $source_folder = $folder_path;
                    break;
                }
            }

            if (!$source_folder) {
                throw new Exception('Kaynak klasör bulunamadı: ' . $folder);
            }

            // Mevcut bağlantıyı kapat
            if (is_resource($mailbox) || (is_object($mailbox) && $mailbox instanceof \IMAP\Connection)) {
                try {
                    @imap_close($mailbox);
                } catch (\Throwable $e) {
                    // Bağlantı zaten kapalı olabilir, hatayı yok say
                    error_log('IMAP bağlantısı kapatma hatası: ' . $e->getMessage());
                }
            }

            // Kaynak klasöre bağlan
            $mailbox = imap_open($source_folder, $account['username'], $account['password']);

            if (!$mailbox) {
                throw new Exception('Kaynak klasöre bağlanma hatası: ' . imap_last_error());
            }
        }

        // Mail sayısını al
        $total_emails = imap_num_msg($mailbox);

        if ($total_emails == 0) {
            return true; // Silinecek mail yok
        }

        // Tüm mailleri sil
        for ($i = 1; $i <= $total_emails; $i++) {
            try {
                // Mail içeriğini al
                $header = imap_fetchheader($mailbox, $i);
                $body = imap_body($mailbox, $i);

                // Maili Trash klasörüne kopyala
                imap_append($mailbox, $trash_folder, $header . "\r\n" . $body);

                // Etiket ilişkilerini geçici olarak sakla
                $email_labels = $db->get_results("SELECT * FROM mail_label_relations
                                                WHERE user_id = " . (int) $user_id . "
                                                AND email_id = " . (int) $i . "
                                                AND account_id = '" . $db->escape($account_id) . "'
                                                AND folder = '" . $db->escape($folder) . "'");

                // Önce eski etiket ilişkilerini sil
                $db->query("DELETE FROM mail_label_relations
                           WHERE user_id = " . (int) $user_id . "
                           AND email_id = " . (int) $i . "
                           AND account_id = '" . $db->escape($account_id) . "'
                           AND folder = '" . $db->escape($folder) . "'");

                // Etiket ilişkilerini daha sonra yeniden eklemek için sakla
                if (!empty($email_labels)) {
                    $email_labels_to_restore[$i] = $email_labels;
                }

                // Orijinal maili sil
                imap_delete($mailbox, $i);
            } catch (Exception $e) {
                error_log("Mail silme hatası (ID: $i): " . $e->getMessage());
                // Hata olsa bile devam et
                continue;
            }
        }

        // Değişiklikleri uygula
        if (!imap_expunge($mailbox)) {
            throw new Exception('Değişiklikler uygulanamadı.');
        }

        // Eğer etiket ilişkileri varsa, çöp kutusundaki yeni mail ID'leri ile etiketleri yeniden ekle
        if (!empty($email_labels_to_restore)) {
            // Hesap bilgilerini al
            $account = null;
            if ($account_id === 'default' || $account_id === null) {
                $account = $mailbox_config['default'];
            } else {
                // Belirtilen hesabı bul
                foreach ($mailbox_config['accounts'] as $acc) {
                    if ($acc['id'] == $account_id) {
                        $account = $acc;
                        break;
                    }
                }

                // Hesap bulunamadıysa varsayılanı kullan
                if ($account === null) {
                    $account = $mailbox_config['default'];
                }
            }

            // Çöp kutusuna bağlan
            $trash_mailbox = imap_open($trash_folder, $account['username'], $account['password']);

            if ($trash_mailbox) {
                // Çöp kutusundaki mail sayısını al
                $total_emails = imap_num_msg($trash_mailbox);

                if ($total_emails > 0) {
                    // Silinen mail sayısı
                    $deleted_count = count($email_labels_to_restore);

                    // Çöp kutusundaki son $deleted_count mail, silinen maillere karşılık gelir
                    $start_index = $total_emails - $deleted_count + 1;

                    // Eski mail ID'lerini yeni ID'lerle eşleştir
                    $id_mapping = array();
                    $index = 0;
                    foreach ($email_labels_to_restore as $old_id => $labels) {
                        $new_id = $start_index + $index;
                        $id_mapping[$old_id] = $new_id;
                        $index++;
                    }

                    // Etiketleri yeni ID'ler ile ekle
                    foreach ($email_labels_to_restore as $old_id => $labels) {
                        $new_id = $id_mapping[$old_id];

                        foreach ($labels as $label) {
                            $sql = "INSERT INTO mail_label_relations (user_id, label_id, email_id, account_id, folder, created_at)
                                    VALUES (" . (int) $user_id . ",
                                            " . (int) $label->label_id . ",
                                            " . (int) $new_id . ",
                                            '" . $db->escape($account_id) . "',
                                            'trash',
                                            NOW())";
                            $db->query($sql);
                        }
                    }
                }

                // Çöp kutusu bağlantısını kapat
                imap_close($trash_mailbox);
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("Tüm mailleri silme hatası: " . $e->getMessage());
        throw $e;
    }
}

/**
 * E-posta adresini MIME formatından ayıklar
 */
function extract_email($mime_string)
{
    if (preg_match('/<([^>]+)>/', $mime_string, $matches)) {
        return $matches[1];
    }
    return trim($mime_string);
}

/**
 * Mail yanıtlama fonksiyonu
 */
function mailbox_reply($mailbox, $email_id, $to, $subject, $body, $attachments = array(), $signature_id = null, $cc = array(), $bcc = array(), $reply_all = false, $account_id = null)
{
    global $mailbox_config;

    // Orijinal maili al
    $original = get_email_content($mailbox, $email_id);

    // E-posta adresini ayıkla
    $to = extract_email($to);

    // Yanıt başlığını oluştur
    if (strpos(strtolower($subject), 're:') === false) {
        $subject = 'Re: ' . $subject;
    }

    // Tümünü yanıtla seçeneği için CC'leri ekle
    if ($reply_all) {
        // Orijinal maildeki CC'leri al
        if (!empty($original['cc'])) {
            if (is_array($original['cc'])) {
                foreach ($original['cc'] as $cc_address) {
                    // Kendimizi CC'ye eklemeyelim
                    if ($cc_address != $mailbox_config['username']) {
                        $cc[] = $cc_address;
                    }
                }
            } elseif ($original['cc'] != $mailbox_config['username']) {
                $cc[] = $original['cc'];
            }
        }

        // Orijinal maildeki alıcıları CC'ye ekle (gönderen hariç)
        if (!empty($original['to'])) {
            if (is_array($original['to'])) {
                foreach ($original['to'] as $to_address) {
                    // Gönderen ve kendimizi CC'ye eklemeyelim
                    if ($to_address != $to && $to_address != $mailbox_config['username']) {
                        $cc[] = $to_address;
                    }
                }
            } elseif ($original['to'] != $to && $original['to'] != $mailbox_config['username']) {
                $cc[] = $original['to'];
            }
        }
    }

    // Yanıt gövdesini oluştur
    $reply_body = $body . "\n\n";
    $reply_body .= "<br><br>";
    $reply_body .= "<div style='border-left: 2px solid #ccc; padding-left: 10px; margin: 10px 0;'>";
    $reply_body .= "<p>-------- Orijinal Mesaj --------</p>";
    $reply_body .= "<p><strong>Kimden:</strong> " . $original['from'] . "</p>";
    $reply_body .= "<p><strong>Tarih:</strong> " . $original['date'] . "</p>";
    $reply_body .= "<p><strong>Konu:</strong> " . $original['subject'] . "</p>";
    if (!empty($original['to'])) {
        $reply_body .= "<p><strong>Alıcı:</strong> " . (is_array($original['to']) ? implode(', ', $original['to']) : $original['to']) . "</p>";
    }
    if (!empty($original['cc'])) {
        $reply_body .= "<p><strong>CC:</strong> " . (is_array($original['cc']) ? implode(', ', $original['cc']) : $original['cc']) . "</p>";
    }
    $reply_body .= "<br>";
    $reply_body .= $original['body'];
    $reply_body .= "</div>";

    // Maili gönder
    return send_email($to, $subject, $reply_body, $attachments, $signature_id, $cc, $bcc, $account_id);
}

/**
 * Dosya boyutunu formatlar
 */
function format_file_size($bytes)
{
    if ($bytes >= **********) {
        return number_format($bytes / **********, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

/**
 * Mail yönlendirme (forward) fonksiyonu
 */
function mailbox_forward($mailbox, $email_id, $to, $subject, $body, $attachments = array(), $signature_id = null, $account_id = null)
{
    // Orijinal maili al
    $original = get_email_content($mailbox, $email_id);

    // E-posta adresini ayıkla
    $to = extract_email($to);

    // Yönlendirme başlığını oluştur
    if (strpos(strtolower($subject), 'fwd:') === false && strpos(strtolower($subject), 'ilet:') === false) {
        $subject = 'Fwd: ' . $subject;
    }

    // Yönlendirme gövdesini oluştur
    $forward_body = $body . "\n\n";
    $forward_body .= "<br><br>";
    $forward_body .= "<div style='border-left: 2px solid #ccc; padding-left: 10px; margin: 10px 0;'>";
    $forward_body .= "<p>-------- Yönlendirilen Mesaj --------</p>";
    $forward_body .= "<p><strong>Kimden:</strong> " . $original['from'] . "</p>";
    $forward_body .= "<p><strong>Tarih:</strong> " . $original['date'] . "</p>";
    $forward_body .= "<p><strong>Konu:</strong> " . $original['subject'] . "</p>";
    $forward_body .= "<p><strong>Alıcı:</strong> " . $original['to'] . "</p>";
    $forward_body .= "<br>";
    $forward_body .= $original['body'];
    $forward_body .= "</div>";

    // Orijinal maildeki ekleri ekle
    if (!empty($original['attachments'])) {
        foreach ($original['attachments'] as $attachment) {
            $attachment_content = download_attachment($mailbox, $email_id, $attachment['part_num']);
            if ($attachment_content) {
                // Geçici dosya oluştur
                $temp_dir = images_url('mailbox') . '/mailbox/temp/';
                if (!is_dir($temp_dir)) {
                    mkdir($temp_dir, 0777, true);
                }

                $temp_file = $temp_dir . uniqid() . '_' . $attachment_content['filename'];
                file_put_contents($temp_file, $attachment_content['content']);

                // Ekleri diziye ekle
                $attachments[] = array(
                    'name' => $attachment_content['filename'],
                    'tmp_name' => $temp_file
                );
            }
        }
    }

    // Maili gönder
    return send_email($to, $subject, $forward_body, $attachments, $signature_id, array(), array(), $account_id);
}

/**
 * İmza oluşturur
 */
function create_signature($data)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata döndür
    if ($user_id == 0) {
        error_log('İmza oluşturma hatası: Kullanıcı ID bulunamadı');
        return false;
    }

    // Gerekli alanların kontrolü
    if (empty($data['name']) || empty($data['content'])) {
        throw new Exception('İmza adı ve içeriği zorunludur.');
    }

    // Varsayılan imza kontrolü
    $is_default = isset($data['is_default']) && $data['is_default'] ? 1 : 0;

    // Eğer bu imza varsayılan olarak işaretlendiyse, diğer imzaları varsayılan olmaktan çıkar
    if ($is_default) {
        $db->query("UPDATE mail_signatures SET is_default = 0 WHERE user_id = " . (int) $user_id);
    }

    // SQL sorgusunu hazırla
    $sql = "INSERT INTO mail_signatures (user_id, name, content, is_default, created_at)
            VALUES (" . (int) $user_id . ",
                    '" . $db->escape($data['name']) . "',
                    '" . $db->escape($data['content']) . "',
                    " . $is_default . ",
                    NOW())";

    // Sorguyu çalıştır
    return $db->query($sql);
}

/**
 * İmzayı günceller
 */
function update_signature($id, $data)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Gerekli alanların kontrolü
    if (empty($data['name']) || empty($data['content'])) {
        throw new Exception('İmza adı ve içeriği zorunludur.');
    }

    // İmzanın varlığını kontrol et
    $signature = get_signature($id);
    if (!$signature) {
        throw new Exception('İmza bulunamadı.');
    }

    // Varsayılan imza kontrolü
    $is_default = isset($data['is_default']) && $data['is_default'] ? 1 : 0;

    // Eğer bu imza varsayılan olarak işaretlendiyse, diğer imzaları varsayılan olmaktan çıkar
    if ($is_default) {
        $db->query("UPDATE mail_signatures SET is_default = 0 WHERE user_id = " . (int) $user_id);
    }

    // SQL sorgusunu hazırla
    $sql = "UPDATE mail_signatures
            SET name = '" . $db->escape($data['name']) . "',
                content = '" . $db->escape($data['content']) . "',
                is_default = " . $is_default . "
            WHERE id = " . (int) $id . "
            AND user_id = " . (int) $user_id;

    // Sorguyu çalıştır
    return $db->query($sql);
}

/**
 * İmzayı siler
 */
function delete_signature($id)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // İmzanın varlığını kontrol et
    $signature = get_signature($id);
    if (!$signature) {
        return false;
    }

    // SQL sorgusu ile silme işlemi
    $sql = "DELETE FROM mail_signatures
            WHERE id = " . (int) $id . "
            AND user_id = " . (int) $user_id;

    return $db->query($sql);
}

/**
 * İmzayı getirir
 */
function get_signature($id)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->get_row("SELECT * FROM mail_signatures WHERE id = " . (int) $id . " AND user_id = " . (int) $user_id);
}

/**
 * Varsayılan imzayı getirir
 */
function get_default_signature()
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->get_row("SELECT * FROM mail_signatures WHERE user_id = " . (int) $user_id . " AND is_default = 1");
}

/**
 * Kullanıcının tüm imzalarını getirir
 */
function get_signatures()
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->get_results("SELECT * FROM mail_signatures WHERE user_id = " . (int) $user_id . " ORDER BY is_default DESC, name ASC");
}

/**
 * Hazır mesaj oluşturur
 */
function create_template($data)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata döndür
    if ($user_id == 0) {
        error_log('Hazır mesaj oluşturma hatası: Kullanıcı ID bulunamadı');
        return false;
    }

    // SQL sorgusunu hazırla
    $sql = "INSERT INTO mail_templates (user_id, name, content, created_at)
            VALUES (" . (int) $user_id . ",
                    '" . $db->escape($data['name']) . "',
                    '" . $db->escape($data['content']) . "',
                    NOW())";

    // Sorguyu çalıştır
    return $db->query($sql);
}

/**
 * Hazır mesajı günceller
 */
function update_template($id, $data)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // SQL sorgusunu hazırla
    $sql = "UPDATE mail_templates
            SET name = '" . $db->escape($data['name']) . "',
                content = '" . $db->escape($data['content']) . "'
            WHERE id = " . (int) $id . "
            AND user_id = " . (int) $user_id;

    // Sorguyu çalıştır
    return $db->query($sql);
}

/**
 * Hazır mesajı siler
 */
function delete_template($id)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->query("DELETE FROM mail_templates WHERE id = " . (int) $id . " AND user_id = " . (int) $user_id);
}

/**
 * Hazır mesajı getirir
 */
function get_template($id)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->get_row("SELECT * FROM mail_templates WHERE id = " . (int) $id . " AND user_id = " . (int) $user_id);
}

/**
 * Mail arama fonksiyonu
 * @param string $search_text Aranacak metin
 * @param string $folder Klasör (INBOX, SENT, TRASH)
 * @param int|string $account_id Hesap ID
 * @return array Arama sonuçları
 */
function search_emails($search_text, $folder = 'INBOX', $account_id = null)
{
    try {
        global $mailbox_config;

        // Arama metni boşsa tüm mailleri getir
        if (empty($search_text)) {
            return get_email_list(null, $folder, 1, 20, $account_id);
        }

        // Hesap ID belirtilmemişse, aktif hesabı kullan
        if ($account_id === null && isset($_SESSION['active_mail_account'])) {
            $account_id = $_SESSION['active_mail_account'];
        }

        // Hesap bilgilerini al
        $account = null;
        if ($account_id === 'default' || $account_id === null) {
            $account = $mailbox_config['default'];
        } else {
            // Belirtilen hesabı bul
            foreach ($mailbox_config['accounts'] as $acc) {
                if ($acc['id'] == $account_id) {
                    $account = $acc;
                    break;
                }
            }

            // Hesap bulunamadıysa varsayılanı kullan
            if ($account === null) {
                $account = $mailbox_config['default'];
            }
        }

        // IMAP bağlantısı aç
        $mailbox = mailbox_connect($account_id);

        if (!$mailbox) {
            throw new Exception('IMAP bağlantısı açılamadı.');
        }

        // Klasör parametresine göre uygun klasöre geç
        if ($folder != 'INBOX') {
            // Mevcut bağlantıyı kapat
            @imap_close($mailbox);

            $host = parse_url($account['imap_host'], PHP_URL_HOST);
            $target_folder = null;

            if ($folder == 'SENT') {
                // Sent klasörüne yeni bağlantı aç
                $possible_folders = array('Sent', 'Sent Items', 'Gönderilmiş Öğeler');

                foreach ($possible_folders as $folder_name) {
                    $mailbox = @imap_open(
                        $host . ':993/imap/ssl/novalidate-cert}' . $folder_name,
                        $account['username'],
                        $account['password']
                    );
                    if ($mailbox) {
                        $target_folder = $folder_name;
                        break;
                    }
                }

                if (!$mailbox) {
                    throw new Exception('Giden kutusu klasörüne erişilemiyor: ' . imap_last_error());
                }
            } elseif ($folder == 'TRASH') {
                // Çöp kutusu klasörüne yeni bağlantı aç
                $possible_folders = array('Trash', 'Deleted Items', 'Çöp Kutusu', 'Deleted');

                foreach ($possible_folders as $folder_name) {
                    $mailbox = @imap_open(
                        $host . ':993/imap/ssl/novalidate-cert}' . $folder_name,
                        $account['username'],
                        $account['password']
                    );
                    if ($mailbox) {
                        $target_folder = $folder_name;
                        break;
                    }
                }

                if (!$mailbox) {
                    throw new Exception('Çöp kutusu klasörüne erişilemiyor: ' . imap_last_error());
                }
            }
        }

        // Toplam mail sayısını al
        $total_emails = imap_num_msg($mailbox);

        // Mail yoksa boş dizi döndür
        if ($total_emails <= 0) {
            @imap_close($mailbox);
            return array(
                'emails' => array(),
                'total' => 0,
                'pages' => 0
            );
        }

        // Arama sonuçlarını tutacak dizi
        $emails = array();
        $search_text = mb_strtolower($search_text, 'UTF-8');

        // Tüm mailleri kontrol et
        for ($i = $total_emails; $i >= 1; $i--) {
            $header = imap_headerinfo($mailbox, $i);

            if ($header) {
                // Konu ve gönderen bilgilerini al
                $subject = mb_strtolower(decode_mime_string($header->subject), 'UTF-8');
                $from = mb_strtolower(decode_mime_string($header->from[0]->mailbox . '@' . $header->from[0]->host), 'UTF-8');

                // Mail içeriğini al (ilk 1000 karakter)
                $body = '';
                $structure = imap_fetchstructure($mailbox, $i);

                if (!$structure->parts) {
                    // Tek parçalı mail
                    $body = imap_body($mailbox, $i, FT_PEEK);

                    // Encoding'e göre decode et
                    if ($structure->encoding == 3) { // BASE64
                        $body = base64_decode($body);
                    } elseif ($structure->encoding == 4) { // QUOTED-PRINTABLE
                        $body = quoted_printable_decode($body);
                    }

                    // HTML içeriği temizle
                    $body = strip_tags($body);
                } else {
                    // Çok parçalı mail
                    $parts = $structure->parts;
                    for ($j = 0; $j < count($parts); $j++) {
                        $part_number = $j + 1;

                        if ($parts[$j]->subtype == 'PLAIN' || $parts[$j]->subtype == 'HTML') {
                            $part_body = imap_fetchbody($mailbox, $i, $part_number, FT_PEEK);

                            // Encoding'e göre decode et
                            if ($parts[$j]->encoding == 3) { // BASE64
                                $part_body = base64_decode($part_body);
                            } elseif ($parts[$j]->encoding == 4) { // QUOTED-PRINTABLE
                                $part_body = quoted_printable_decode($part_body);
                            }

                            // HTML içeriği temizle
                            $part_body = strip_tags($part_body);
                            $body .= $part_body . ' ';
                        }
                    }
                }

                // Metni UTF-8'e çevir ve küçük harfe dönüştür
                $body = mb_strtolower(mb_substr($body, 0, 1000, 'UTF-8'), 'UTF-8');

                // Arama metnini konu, gönderen veya içerikte ara
                if (
                    strpos($subject, $search_text) !== false ||
                    strpos($from, $search_text) !== false ||
                    strpos($body, $search_text) !== false
                ) {

                    // Okunma durumunu kontrol et
                    $seen = 1; // Varsayılan olarak okunmuş

                    // Unseen bayrağını kontrol et
                    if (isset($header->Unseen) && trim($header->Unseen) == 'U') {
                        $seen = 0;
                    }

                    // Mail bayraklarını kontrol et
                    $flags = imap_fetch_overview($mailbox, $i);
                    if (!empty($flags) && isset($flags[0])) {
                        if (!$flags[0]->seen) {
                            $seen = 0;
                        }
                    }

                    $emails[] = array(
                        'id' => $i,
                        'subject' => decode_mime_string($header->subject),
                        'from' => decode_mime_string($header->from[0]->mailbox . '@' . $header->from[0]->host),
                        'date' => date('Y-m-d H:i', strtotime($header->date)),
                        'seen' => $seen
                    );
                }
            }
        }

        // Bağlantıyı kapat
        @imap_close($mailbox);

        return array(
            'emails' => $emails,
            'total' => count($emails),
            'pages' => 1 // Arama sonuçları tek sayfada gösterilecek
        );
    } catch (Exception $e) {
        error_log("Mail arama hatası: " . $e->getMessage());
        return array(
            'emails' => array(),
            'total' => 0,
            'pages' => 0,
            'error' => $e->getMessage()
        );
    }
}

/**
 * Kullanıcının tüm hazır mesajlarını getirir
 */
function get_templates()
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->get_results("SELECT * FROM mail_templates WHERE user_id = " . (int) $user_id . " ORDER BY name ASC");
}

/**
 * Etiket oluşturur
 */
function create_label($data)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa hata döndür
    if ($user_id == 0) {
        error_log('Etiket oluşturma hatası: Kullanıcı ID bulunamadı');
        return false;
    }

    // SQL sorgusunu hazırla
    $sql = "INSERT INTO mail_labels (user_id, name, color, created_at)
            VALUES (" . (int) $user_id . ",
                    '" . $db->escape($data['name']) . "',
                    '" . $db->escape($data['color']) . "',
                    NOW())";

    // Sorguyu çalıştır
    $db->query($sql);

    // Eklenen etiketin ID'sini döndür
    $result = $db->get_row("SELECT LAST_INSERT_ID() as id");
    return $result ? $result->id : 0;
}

/**
 * Etiketi günceller
 */
function update_label($id, $data)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // SQL sorgusunu hazırla
    $sql = "UPDATE mail_labels
            SET name = '" . $db->escape($data['name']) . "',
                color = '" . $db->escape($data['color']) . "'
            WHERE id = " . (int) $id . "
            AND user_id = " . (int) $user_id;

    // Sorguyu çalıştır
    return $db->query($sql);
}

/**
 * Etiketi siler
 */
function delete_label($id)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Önce etiket ilişkilerini sil
    $db->query("DELETE FROM mail_label_relations WHERE label_id = " . (int) $id . " AND user_id = " . (int) $user_id);

    // Sonra etiketi sil
    return $db->query("DELETE FROM mail_labels WHERE id = " . (int) $id . " AND user_id = " . (int) $user_id);
}

/**
 * Etiketi getirir
 */
function get_label($id)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->get_row("SELECT * FROM mail_labels WHERE id = " . (int) $id . " AND user_id = " . (int) $user_id);
}

/**
 * Kullanıcının tüm etiketlerini getirir
 */
function get_labels()
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    return $db->get_results("SELECT * FROM mail_labels WHERE user_id = " . (int) $user_id . " ORDER BY name ASC");
}

/**
 * Maile etiket ekler
 */
function add_label_to_email($label_id, $email_id, $account_id = null, $folder = 'INBOX')
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // SQL sorgusunu hazırla
    $sql = "INSERT IGNORE INTO mail_label_relations (user_id, label_id, email_id, account_id, folder, created_at)
            VALUES (" . (int) $user_id . ",
                    " . (int) $label_id . ",
                    " . (int) $email_id . ",
                    '" . $db->escape($account_id) . "',
                    '" . $db->escape($folder) . "',
                    NOW())";

    // Sorguyu çalıştır
    return $db->query($sql);
}

/**
 * Mailden etiketi kaldırır
 */
function remove_label_from_email($label_id, $email_id, $account_id = null, $folder = 'INBOX')
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // SQL sorgusunu hazırla
    $sql = "DELETE FROM mail_label_relations
            WHERE user_id = " . (int) $user_id . "
            AND label_id = " . (int) $label_id . "
            AND email_id = " . (int) $email_id . "
            AND account_id = '" . $db->escape($account_id) . "'
            AND folder = '" . $db->escape($folder) . "'";

    // Sorguyu çalıştır
    return $db->query($sql);
}

/**
 * Mailin etiketlerini getirir
 */
function get_email_labels($email_id, $account_id = null, $folder = 'INBOX')
{
    global $db, $user;


    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // Hesap yoksa boş dizi döndür
    if (!$account_id) {
        error_log('Mail etiketleri alma hatası: Hesap ID bulunamadı');
        return array();
    }


    try {
        // SQL sorgusunu hazırla
        $sql = "SELECT l.*
                FROM mail_labels l
                JOIN mail_label_relations r ON l.id = r.label_id
                WHERE r.user_id = " . (int) $user_id . "
                AND r.email_id = " . (int) $email_id . "
                AND r.account_id = '" . $db->escape($account_id) . "'";

        // Klasör filtresini ekle (isteğe bağlı)
        if ($folder) {
            $sql .= " AND r.folder = '" . $db->escape($folder) . "'";
        }

        $sql .= " ORDER BY l.name ASC";

        // Sorguyu çalıştır
        $results = $db->get_results($sql);

        // Debug için log kaydı ekle
        error_log('Mail ID: ' . $email_id . ', Hesap ID: ' . $account_id . ', Bulunan Etiket Sayısı: ' . ($results ? count($results) : 0));

        return $results ? $results : array();
    } catch (Exception $e) {
        error_log('Mail etiketleri alma hatası: ' . $e->getMessage());
        return array();
    }
}

/**
 * Belirli bir etikete sahip mailleri getirir
 */
function get_emails_by_label($label_id, $account_id = null, $folder = 'INBOX')
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // Eğer USER_LOGIN tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // Hesap yoksa boş dizi döndür
    if (!$account_id) {
        error_log('Etiketli mail listesi alma hatası: Hesap ID bulunamadı');
        return array();
    }

    try {
        // SQL sorgusunu hazırla
        $sql = "SELECT r.email_id, r.folder
                FROM mail_label_relations r
                WHERE r.user_id = " . (int) $user_id . "
                AND r.label_id = " . (int) $label_id . "
                AND r.account_id = '" . $db->escape($account_id) . "'";

        // Klasör filtresini ekle (isteğe bağlı)
        if ($folder) {
            $sql .= " AND r.folder = '" . $db->escape($folder) . "'";
        } else {
            // Eğer klasör belirtilmemişse, çöp kutusundaki mailleri hariç tut
            $sql .= " AND r.folder != 'trash'";
        }

        // Sorguyu çalıştır
        $result = $db->get_results($sql);
        $email_ids = array();

        if ($result) {
            foreach ($result as $row) {
                $email_ids[] = $row->email_id;
            }
        }

        // Debug için log kaydı ekle
        error_log('Etiket ID: ' . $label_id . ', Hesap ID: ' . $account_id . ', Bulunan Mail Sayısı: ' . (is_array($email_ids) ? count($email_ids) : 0));

        return $email_ids;
    } catch (Exception $e) {
        error_log('Etiketli mail listesi alma hatası: ' . $e->getMessage());
        return array();
    }
}

/**
 * Kontrast rengi hesaplama fonksiyonu
 * Arka plan rengine göre metin rengini (siyah veya beyaz) belirler
 */
function get_contrast_color($hexColor)
{
    // Hex rengi RGB'ye dönüştür
    $r = hexdec(substr($hexColor, 1, 2));
    $g = hexdec(substr($hexColor, 3, 2));
    $b = hexdec(substr($hexColor, 5, 2));

    // Parlaklığı hesapla (YIQ formülü)
    $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;

    // Parlaklığa göre siyah veya beyaz döndür
    return ($yiq >= 128) ? '#000000' : '#ffffff';
}

/**
 * Belirli bir etikete sahip mail sayısını getirir
 */
function get_label_email_count($label_id, $account_id = null)
{
    global $db, $user;

    // USER_LOGIN değişkenini kullan (giriş yapmış kullanıcı ID'si)
    $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

    // USER_LOGIN değişkeni tanımlı değilse ve $user->id varsa onu kullan
    if ($user_id == 0 && isset($user->id)) {
        $user_id = $user->id;
    }

    // Hala user_id yoksa 0 döndür
    if ($user_id == 0) {
        return 0;
    }

    // Hesap ID belirtilmemişse, aktif hesabı kullan
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // Hesap yoksa 0 döndür
    if (!$account_id) {
        return 0;
    }

    // mail_label_relations tablosunun var olup olmadığını kontrol et
    $check_table = $db->get_var("SHOW TABLES LIKE 'mail_label_relations'");
    if (!$check_table) {
        return 0;
    }

    // SQL sorgusunu hazırla - Tüm klasörlerdeki etiketli mailleri say
    $sql = "SELECT COUNT(DISTINCT r.email_id) as count
            FROM mail_label_relations r
            WHERE r.user_id = " . (int) $user_id . "
            AND r.label_id = " . (int) $label_id;

    if ($account_id) {
        $sql .= " AND r.account_id = '" . $db->escape($account_id) . "'";
    }

    // Çöp kutusundaki mailleri hariç tut
    $sql .= " AND r.folder = 'INBOX' GROUP BY r.email_id";
    // echo    $sql .= " AND r.folder != 'trash' ";

    // Sorguyu çalıştır ve sonuçları döndür
    $result = $db->get_row($sql);

    // Etiket sayısını döndür
    return $result ? (int)$result->count : 0;
}

/**
 * Mail hesabının sunucu bilgisini döndürür
 */
function get_mailbox_server($account_id)
{
    global $mailbox_config;

    // Hesap bilgilerini al
    $account = null;
    if ($account_id === 'default' || $account_id === null) {
        $account = $mailbox_config['default'];
    } else {
        // Belirtilen hesabı bul
        foreach ($mailbox_config['accounts'] as $acc) {
            if ($acc['id'] == $account_id) {
                $account = $acc;
                break;
            }
        }

        // Hesap bulunamadıysa varsayılanı kullan
        if ($account === null) {
            $account = $mailbox_config['default'];
        }
    }

    if (!$account) {
        return '';
    }

    // IMAP host bilgisinden sunucu bilgisini çıkar
    // Örnek: {mail.example.com:993/imap/ssl}
    $host = $account['imap_host'];

    // {} parantezlerini kaldır
    $host = str_replace('{', '', $host);
    $host = str_replace('}', '', $host);

    return $host;
}

/**
 * Belirli bir klasördeki mail sayısını getirir
 */
function get_folder_email_count($folder = 'INBOX', $account_id = null)
{
    // Taslaklar için veritabanından sayıyı al
    if ($folder == 'DRAFTS') {
        global $db, $user;
        $user_id = defined('USER_LOGIN') ? USER_LOGIN : 0;

        // USER_LOGIN değişkeni tanımlı değilse ve $user->id varsa onu kullan
        if ($user_id == 0 && isset($user->id)) {
            $user_id = $user->id;
        }

        // Hala user_id yoksa 0 döndür
        if ($user_id == 0) {
            return 0;
        }

        // mail_drafts tablosunun var olup olmadığını kontrol et
        $check_table = $db->get_var("SHOW TABLES LIKE 'mail_drafts'");
        if (!$check_table) {
            return 0; // Tablo yoksa 0 döndür
        }

        // account_id alanının var olup olmadığını kontrol et
        $check_column = $db->get_var("SHOW COLUMNS FROM mail_drafts LIKE 'account_id'");

        $sql = "SELECT COUNT(*) as count FROM mail_drafts WHERE user_id = " . (int) $user_id;

        // Eğer account_id alanı varsa ve hesap ID belirtilmişse, hesaba göre filtrele
        if ($check_column && $account_id) {
            $sql .= " AND (account_id = '" . $db->escape($account_id) . "' OR account_id IS NULL)";
        }

        $result = $db->get_row($sql);

        if ($result) {
            return (int)$result->count;
        }

        return 0;
    }

    // Diğer klasörler için IMAP kullan

    // Aktif hesap ID'sini al
    if ($account_id === null && isset($_SESSION['active_mail_account'])) {
        $account_id = $_SESSION['active_mail_account'];
    }

    // Hesap ID yoksa 0 döndür
    if (!$account_id) {
        return 0;
    }

    global $mailbox_config;

    // Hesap bilgilerini al
    $account = null;
    if ($account_id === 'default') {
        $account = $mailbox_config['default'];
    } else {
        foreach ($mailbox_config['accounts'] as $acc) {
            if ($acc['id'] == $account_id) {
                $account = $acc;
                break;
            }
        }

        if ($account === null) {
            $account = $mailbox_config['default'];
        }
    }

    if ($folder == 'INBOX') {
        // INBOX için doğrudan bağlan
        // INBOX kısmını temizle
        $host = $account['imap_host'];
        $host = preg_replace('/}INBOX$/', '}INBOX', $host);

        $mailbox = @imap_open(
            $host,
            $account['username'],
            $account['password']
        );

        if (!$mailbox) {
            return 0;
        }

        // Sadece okunmamış maillerin sayısını al
        try {
            $unread = 0;
            $total = imap_num_msg($mailbox);

            for ($i = 1; $i <= $total; $i++) {
                $header = imap_headerinfo($mailbox, $i);
                if (isset($header->Unseen) && trim($header->Unseen) == 'U') {
                    $unread++;
                } else {
                    // Mail bayraklarını kontrol et
                    $flags = imap_fetch_overview($mailbox, $i);
                    if (!empty($flags) && isset($flags[0]) && !$flags[0]->seen) {
                        $unread++;
                    }
                }
            }

            imap_close($mailbox);
            return $unread;
        } catch (Exception $e) {
            error_log("Okunmamış mail sayısı alınamadı: " . $e->getMessage());
            imap_close($mailbox);
            return 0;
        }
    } elseif ($folder == 'SENT') {

        // IMAP bağlantı dizesini oluştur
        $host = $account['imap_host'];

        // INBOX kısmını temizle
        $host = preg_replace('/}INBOX$/', '}', $host);

        // Eğer host zaten {} içeriyorsa, doğrudan kullan
        if (strpos($host, '{') === 0 && strpos($host, '}') !== false) {
            $imap_string = $host;
        } else {
            // Aksi takdirde, host adresini al ve IMAP dizesini oluştur
            $host_parts = parse_url($host);
            $server = isset($host_parts['host']) ? $host_parts['host'] : $host;
            $imap_string = '{' . $server . ':993/imap/ssl/novalidate-cert}';
        }

        // Önce INBOX'a bağlan ve klasörleri listele
        $mailbox = @imap_open(
            $imap_string,
            $account['username'],
            $account['password']
        );

        if (!$mailbox) {
            return 00;
        }


        // Klasörleri listele
        $folders = @imap_list($mailbox, $imap_string, '*');



        // Bağlantıyı kapat
        imap_close($mailbox);

        if (!is_array($folders)) {
            return 0;
        }


        // Sent klasörünü bul
        $sent_folder = null;
        foreach ($folders as $folder_path) {
            if (
                stripos($folder_path, 'Sent') !== false ||
                stripos($folder_path, 'Gönderilmiş') !== false ||
                stripos($folder_path, 'Giden') !== false ||
                stripos($folder_path, 'Outbox') !== false ||
                stripos($folder_path, 'Sent Items') !== false ||
                stripos($folder_path, 'Sent Mail') !== false
            ) {
                $sent_folder = $folder_path;
                break;
            }
        }

        if (!$sent_folder) {
            return 0;
        }



        // Sent klasörüne bağlan
        $mailbox = @imap_open(
            $sent_folder,
            $account['username'],
            $account['password']
        );

        if (!$mailbox) {
            return 0;
        }

        // Mail sayısını al
        $count = imap_num_msg($mailbox);

        // Bağlantıyı kapat
        imap_close($mailbox);

        return $count;
    } elseif ($folder == 'TRASH') {
        // IMAP bağlantı dizesini oluştur
        $host = $account['imap_host'];

        // INBOX kısmını temizle
        $host = preg_replace('/}INBOX$/', '}', $host);

        // Eğer host zaten {} içeriyorsa, doğrudan kullan
        if (strpos($host, '{') === 0 && strpos($host, '}') !== false) {
            $imap_string = $host;
        } else {
            // Aksi takdirde, host adresini al ve IMAP dizesini oluştur
            $host_parts = parse_url($host);
            $server = isset($host_parts['host']) ? $host_parts['host'] : $host;
            $imap_string = '{' . $server . ':993/imap/ssl/novalidate-cert}';
        }

        // Önce INBOX'a bağlan ve klasörleri listele
        $mailbox = @imap_open(
            $imap_string,
            $account['username'],
            $account['password']
        );

        if (!$mailbox) {
            return 0;
        }

        // Klasörleri listele
        $folders = @imap_list($mailbox, $imap_string, '*');

        // Bağlantıyı kapat
        imap_close($mailbox);

        if (!is_array($folders)) {
            return 0;
        }

        // Trash klasörünü bul
        $trash_folder = null;
        foreach ($folders as $folder_path) {
            if (
                stripos($folder_path, 'Trash') !== false ||
                stripos($folder_path, 'Çöp') !== false ||
                stripos($folder_path, 'Deleted') !== false ||
                stripos($folder_path, 'Deleted Items') !== false ||
                stripos($folder_path, 'Junk') !== false ||
                stripos($folder_path, 'Spam') !== false
            ) {
                $trash_folder = $folder_path;
                break;
            }
        }

        if (!$trash_folder) {
            return 0;
        }

        // Trash klasörüne bağlan
        $mailbox = @imap_open(
            $trash_folder,
            $account['username'],
            $account['password']
        );

        if (!$mailbox) {
            return 0;
        }

        // Mail sayısını al
        $count = imap_num_msg($mailbox);

        // Bağlantıyı kapat
        imap_close($mailbox);

        return $count;
    }

    return 0;
}
