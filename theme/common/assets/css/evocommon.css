.is-invalid {
    border-color: #F64E60 !important;
}

label.is-invalid {
    color: #F64E60;
    font-size: 0.9rem;
    font-weight: 400;
}

.btn-check {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

/* 
.ck.ck-toolbar {
    background: #fff !important;
    border: 0 !important;
    border-bottom: 1px solid #EBEDF3 !important;
}


.ck.ck-editor {
    min-height: 100%;
    min-height: 300px;
}

.ck.ck-editor__editable_inline {
    border: 0 !important;
    padding: 20px !important;
} */


/*TODO*/
.is_checked {
    color: #7E8299 !important;
    text-decoration: line-through !important;
}

.is_none_checked {
    /* font-weight: 500 !important;
    color: #3F4254 !important */
}

.card.card-custom>.card-body {
    /* padding: 0.5rem 1.25rem !important; */
}

#project_view_content_tab {
    padding: 0 !important;
}

.text-kirmizi {
    color: red !important;
}

td.text-cnmar,
.text-cnmar {
    color: #2d7ec1 !important;
}

td.text-nikeyesil2,
.text-nikeyesil2 {
    color: #23f63e !important;
}

td.text-cnmar-yesil,
.text-cnmar-yesil {
    color: #15a0315e !important;
}

.bg-cnmar {
    background-color: #2d7ec1 !important;
}

.bg-cnmar-yesil {
    background-color: #15a0315e !important;
}

.checkbox.checkbox-light-cnmar>input:checked~span {
    background-color: #2d7ec1;
}

.calendar-cnmar {
    background-color: #2d7ec1 !important;
    color: #fff !important;
}

.fc-unthemed .fc-event .fc-time,
.fc-unthemed .fc-event-dot .fc-time {
    color: #fff !important;
    display: inline !important;
}

.fc-unthemed .fc-event .fc-title,
.fc-unthemed .fc-event-dot .fc-title {
    color: #ffffff !important;
    display: inline !important;
}

#calendarModal .modal-dialog {
    max-width: 100%;
}


.svg-icon.svg-icon-primary svg g [fill] {
    -webkit-transition: fill 0.3s ease;
    transition: fill 0.3s ease;
    fill: #2d7ec1 !important;
}

.cursor-move {
    cursor: move !important
}

.tagify .tagify__tag.tagify__tag--cnmar:not(.tagify--notAllowed) {
    background-color: #2d7ec1;
}

.tagify .tagify__tag.tagify__tag--cnmar:not(.tagify--notAllowed) div .tagify__tag-text {
    color: #ffffff;
    font-weight: 500;
}

.tagify .tagify__tag.tagify__tag--cnmar2:not(.tagify--notAllowed) {
    background-color: #95dd10;
}

.tagify .tagify__tag.tagify__tag--cnmar2:not(.tagify--notAllowed) div .tagify__tag-text {
    color: #ffffff;
    font-weight: 500;
}

.btn-nikeyesil i {
    color: #999;
}

.btn-nikeyesil {
    background-color: #dfff00;
    color: #999;
}

.btn-nikeyesil.active {
    background-color: #1d274a !important;
    color: #dfff00 !important;
}

.ck-editor__editable_inline {
    min-height: 200px;
}

.text-nikeyesil {
    color: #dfff00 !important;
}

.fc .fc-scrollgrid-section-sticky>* {
    background-color: transparent !important;
}

.fc-h-event .fc-event-main {
    color: #fff !important;
}

.app-content {
    padding-top: 20px !important;
}

/* FreamWork Upgrade */
.btn-xs {
    --bs-btn-padding-y: 0.55rem;
    --bs-btn-padding-x: 1.25rem;
    --bs-btn-font-size: 0.95rem;
    --bs-btn-border-radius: 0.425rem;
    padding: calc(0.55rem + 1px) calc(1.25rem + 1px) !important;
}

#custom_data_table td,
table.dataTable td {
    font-weight: 600 !important;
    color: #7e8299 !important;
}

.card .card-header {
    min-height: 50px !important;
}

.text-right {
    text-align: right !important;
}

.dropzone {
    min-height: auto;
    padding: 1.5rem 1.75rem;
    text-align: center;
    cursor: pointer;
    border: 1px dashed #009ef7;
    background-color: #f1faff;
    border-radius: 0.475rem !important;
}

.form-group {
    margin-bottom: 0.75rem !important;
}

.timeline.timeline-3 .timeline-items {
    margin: 0;
    padding: 0;
}

.timeline.timeline-3 .timeline-items .timeline-item {
    margin-left: 25px;
    border-left: 2px solid #EBEDF3;
    padding: 0 0 20px 50px;
    position: relative;
}

.timeline.timeline-3 .timeline-items .timeline-item .timeline-media {
    position: absolute;
    top: 0;
    left: -26px;
    border: 2px solid #EBEDF3;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #ffffff;
    line-height: 0;
}

.timeline.timeline-3 .timeline-items .timeline-item .timeline-media i {
    font-size: 1.4rem;
}

.timeline.timeline-3 .timeline-items .timeline-item .timeline-media .svg-icon svg {
    height: 24px;
    width: 24px;
}

.timeline.timeline-3 .timeline-items .timeline-item .timeline-media img {
    max-width: 48px;
    max-height: 48px;
    border-radius: 100%;
}

.timeline.timeline-3 .timeline-items .timeline-item .timeline-content {
    border-radius: 0.85rem;
    padding: 0.75rem .1rem;
}
.text-demetturuncu{
    color: #FF6600 !important;
}
/* .timeline.timeline-3 .timeline-items .timeline-item .timeline-content:before {
    position: absolute;
    content: "";
    width: 0;
    height: 0;
    top: 16px;
    left: 23px;
    border-right: solid 10px #f9f9f9;
    border-bottom: solid 17px transparent;
    border-left: solid 17px transparent;
    border-top: solid 17px transparent;
}

[data-bs-theme="dark"] .timeline.timeline-3 .timeline-items .timeline-item .timeline-content:before {
    border-right: solid 10px #2b2b40;
} */

.timeline.timeline-3 .timeline-items .timeline-item:last-child {
    border-left-color: transparent;
    padding-bottom: 0;
}

.gutter-b {
    margin-bottom: .25rem !important;
}

.image-input-placeholder {
    background-image: url('../../../../theme/admin/evo/assets/svg/avatars/blank.svg');
}

[data-bs-theme="dark"] .image-input-placeholder {
    background-image: url('../../../../theme/admin/evo/assets/svg/avatars/blank-dark.svg');
}

.fc .fc-timegrid-event .fc-event-main {
    padding: 0 !important;
}

.tag-bg-cnmar {
    background-color: #156eb7 !important;
    color: #ffffff !important;
}

.tag-bg-cnmar2 {
    background-color: #6cb5f9 !important;
    color: #ffffff !important;
}

.tag-bg-primary {
    background-color: #9dec00 !important;
    color: #ffffff !important;
}

.tag-bg-warning {
    background-color: #ffc700 !important;
    color: #ffffff !important;
}

.tag-bg-danger {
    background-color: red !important;
    color: #ffffff !important;
}

.tag-bg-success {
    background-color: #50cd89 !important;
    color: #ffffff !important;
}

.tag-bg-dark {
    background-color: #131628 !important;
    color: #ffffff !important;
}

.tagify__tag>div::before {
    box-shadow: none !important;
}

[data-bs-theme="dark"] .fc {
    --fc-today-bg-color: #151521;
}

[data-bs-theme="dark"] .swal2-radio,
[data-bs-theme="dark"] .swal2-checkbox {
    background: #1e1e2d !important;
    font-weight: bold;
}

.tagify__tag__removeBtn:hover+div::before {
    box-shadow: none !important;
    transition: none !important;
}

.fc-v-event .fc-event-main-frame {
    flex-direction: row;
}

.fc-timegrid-event .fc-event-time {
    margin-right: 3px;
}

/* .tagify__tag>div {
    padding: 5px;
} */
[data-bs-theme="light"] [data-kt-app-layout=dark-sidebar] .app-sidebar {
    background-color: #ffffff ;
}
[data-bs-theme="light"] [data-kt-app-layout=dark-sidebar] .app-sidebar .app-sidebar-logo{
    border-bottom: 1px solid #ffffff ;
}
table#custom_data_table.dataTable tr.text-danger td {
    color:#f44336 !important;
}
table#custom_data_table.dataTable tr.text-success td {
    color:#50cd89 !important;
}
table.dataTable {
    margin-top: 0 !important;
}

#hide_chart_departman{
    position: absolute;
    left: 5px;
    bottom: 5px;
    width: 100px;
    height: 30px;
    
    z-index: 99999999999;
}
[data-bs-theme=dark] #hide_chart_departman{
    background-color: #1e1e2d;
}
[data-bs-theme=light] #hide_chart_departman{
    background-color: #ffffff;
}

#canvas-container-div {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    z-index: 999;
}

#canvas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: transparent;
}

#restart-button-div {
    position: fixed;
    bottom: 25px;
    right: 25px;
    width: 100px;
    height: 60px;
    opacity: 0.7;
    cursor: pointer;
    border-radius: 100%;
    z-index: 9999;
}

#restart-icon {
    width: 35px;
    height: 35px;
}
.swal2-popup{
    width: 600px !important;
}
.fc .fc-scroller-harness{
    overflow: visible !important;
}
.fc-timegrid-event-harness-inset .fc-timegrid-event, .fc-timegrid-event.fc-event-mirror, .fc-timegrid-more-link{
    min-height: 25px;
}
[data-kt-app-sidebar-minimize=on] .app-sidebar #frm_tobbar, [data-kt-app-sidebar-minimize=on] .app-sidebar #kt_app_sidebar_footer, [data-kt-app-sidebar-minimize=on] .app-sidebar .text-center.mx-5{
display: none !important;
}
[data-kt-app-sidebar-minimize=on] .app-sidebar div.separator{
    /* width: 20px !important; */
    display: none !important;
}
span.select2-selection.form-select.form-select-sm.bg-primary{
    background-color: #9dec00 !important;
    color: #fff !important;
}
span.select2-selection.form-select.form-select-sm.bg-warning{
    background-color: #9dec00 !important;
    color: #fff !important;
}
div.ck-editor__editable_inline{
    color: #010106 !important;
}
button.fc-custom1-button.fc-button.fc-button-primary{
margin-left: 5px !important;
}
[data-bs-theme=dark] .ck.ck-editor__main>.ck-editor__editable {
    background:#15171c !important;
    color:#ffffff !important;
}
/* .fc-timegrid-slots table > tbody > tr:last-child td {padding-bottom: 20px;} */
/* .fc-timegrid-slots table > tbody  {padding-bottom: 20px;} */