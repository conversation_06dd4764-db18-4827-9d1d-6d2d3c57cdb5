
/*! jQuery Validation Plugin - v1.19.1 - 6/15/2019
 * https://jqueryvalidation.org/
 * Copyright (c) 2019 <PERSON><PERSON><PERSON>; Licensed MIT */
!function (a) { "function" == typeof define && define.amd ? define(["jquery", "../jquery.validate.min"], a) : "object" == typeof module && module.exports ? module.exports = a(require("jquery")) : a(jQuery) }(function (a) { return a.extend(a.validator.messages, { required: "Bu alanın doldurulması zorunludur.", remote: "Lütfen bu alanı düzeltin.", email: "Lütfen geçerli bir e-posta adresi giriniz.", url: "Lütfen geçerli bir web adresi (URL) giriniz.", date: "Lütfen geçerli bir tarih giriniz.", dateISO: "Lütfen geçerli bir tarih giriniz(ISO formatında)", number: "Lütfen geçerli bir sayı giriniz.", digits: "Lütfen sadece sayısal karakterler giriniz.", creditcard: "Lütfen geçerli bir kredi kartı giriniz.", equalTo: "Lütfen aynı değeri tekrar giriniz.", extension: "Lütfen geçerli uzantıya sahip bir değer giriniz.", maxlength: a.validator.format("Lütfen en fazla {0} karakter uzunluğunda bir değer giriniz."), minlength: a.validator.format("Lütfen en az {0} karakter uzunluğunda bir değer giriniz."), rangelength: a.validator.format("Lütfen en az {0} ve en fazla {1} uzunluğunda bir değer giriniz."), range: a.validator.format("Lütfen {0} ile {1} arasında bir değer giriniz."), max: a.validator.format("Lütfen {0} değerine eşit ya da daha küçük bir değer giriniz."), min: a.validator.format("Lütfen {0} değerine eşit ya da daha büyük bir değer giriniz."), require_from_group: a.validator.format("Lütfen bu alanların en az {0} tanesini doldurunuz.") }), a });



var EVOlayoutAddTodo = function () {
  // Private properties
  var _element;
  var _offcanvasObject;

  // Private functions
  var _init = function () {
    var header = KTUtil.find(_element, '.offcanvas-header');
    var content = KTUtil.find(_element, '.offcanvas-content');

    _offcanvasObject = new KTOffcanvas(_element, {
      overlay: true,
      baseClass: 'offcanvas',
      placement: 'right',
      closeBy: 'evo_add_task_close',
      toggleBy: {
        0: "evo_add_task_action",
        1: 'evo_add_task_action2'
      }
    });

    KTUtil.scrollInit(content, {
      disableForMobile: true,
      resetHeightOnDestroy: true,
      handleWindowResize: true,
      height: function () {
        var height = parseInt(KTUtil.getViewPort().height);

        if (header) {
          height = height - parseInt(KTUtil.actualHeight(header));
          height = height - parseInt(KTUtil.css(header, 'marginTop'));
          height = height - parseInt(KTUtil.css(header, 'marginBottom'));
        }

        if (content) {
          height = height - parseInt(KTUtil.css(content, 'marginTop'));
          height = height - parseInt(KTUtil.css(content, 'marginBottom'));
        }

        height = height - parseInt(KTUtil.css(_element, 'paddingTop'));
        height = height - parseInt(KTUtil.css(_element, 'paddingBottom'));

        height = height - 2;

        return height;
      }
    });
  }

  // Public methods
  return {
    init: function (id) {
      _element = document.querySelector(id);

      if (!_element) {
        return;
      }

      // Initialize
      _init();
    },

    getElement: function () {
      return _element;
    }
  };
}();
var EVOlayoutAddEvent = function () {
  // Private properties
  var _element;
  var _offcanvasObject;

  // Private functions
  var _init = function () {
    var header = KTUtil.find(_element, '.offcanvas-header');
    var content = KTUtil.find(_element, '.offcanvas-content');

    _offcanvasObject = new KTOffcanvas(_element, {
      overlay: true,
      baseClass: 'offcanvas',
      placement: 'right',
      closeBy: 'evo_add_event_close',
      toggleBy: {
        0: "evo_add_event_action",
        1: 'evo_add_event_action2'
      }
    });

    KTUtil.scrollInit(content, {
      disableForMobile: true,
      resetHeightOnDestroy: true,
      handleWindowResize: true,
      height: function () {
        var height = parseInt(KTUtil.getViewPort().height);

        if (header) {
          height = height - parseInt(KTUtil.actualHeight(header));
          height = height - parseInt(KTUtil.css(header, 'marginTop'));
          height = height - parseInt(KTUtil.css(header, 'marginBottom'));
        }

        if (content) {
          height = height - parseInt(KTUtil.css(content, 'marginTop'));
          height = height - parseInt(KTUtil.css(content, 'marginBottom'));
        }

        height = height - parseInt(KTUtil.css(_element, 'paddingTop'));
        height = height - parseInt(KTUtil.css(_element, 'paddingBottom'));

        height = height - 2;

        return height;
      }
    });
  }

  // Public methods
  return {
    init: function (id) {
      _element = document.querySelector(id);

      if (!_element) {
        return;
      }

      // Initialize
      _init();
    },

    getElement: function () {
      return _element;
    }
  };
}();
jQuery(document).ready(function ($) {

  // $('#evo_performance_set_score').on('shown.bs.modal', function () {
  //   $('div.datetimepicker-date-time-evo').each(function (a, b) {
  //     console.info($(this).attr('id'))
  //     init_datepicker_evo_with_time($(this).attr('id'));
  //   });
  // })

  $(document).on('click', '#kt_app_sidebar_toggle', function (event) {
    let = value = '1';
    if ($("body").attr("data-kt-app-sidebar-minimize")){
      value = '2';
    } else{
      value = '1';
    }
    
    $.ajax({
      type: 'POST',
      url: WEB_DIR + '/index.php?do=sidebar_minimize/set',
      data: { ajax: 1, value: value },
      success: function (r) {
        if (r.error) {
          swal.fire(lang_sample.bi_problem, r.error, "error");
        } else {
          
        }
      }
    });
  });
  $(document).on('change','#sistemi_kapat',function(){
    if ($(this).is(':checked')){
      var value = '2';
    } else{
      var value = '0';
    }
    $.ajax({
      type: 'POST',
      url: WEB_DIR + '/index.php?do=system_show_down/set',
      data: { ajax: 1, value: value },
      success: function (r) {
        if (r.error) {
          swal.fire(lang_sample.bi_problem, r.error, "error");
        } else {
          toastr.success(r.msg);
        }
      }
    });
  });
  if (MODE != 'vouchers')
  system_show_down();
  
  $(document).on('click', '.s_confirm', function (event) {
    event.preventDefault();
    var text = $(this).data('text');
    var link = $(this).attr('href');
    Swal.fire({
      title: text,
      icon: "info",
      showCancelButton: true,
      confirmButtonText: lang_sample.yes_sure,
      cancelButtonText: lang_sample.cancel,
    }).then((result) => {
      if (result.isConfirmed) {
        window.location.href = link;
      }
    });
  });

  $('#frm_tobbar select').change(function () {
    $('#frm_tobbar').submit();
  });
  $(document).on('click', 'button.btn_log_view', function () {
    var data_id = $(this).data('id');
    var data_target = $(this).data('target');
    $.ajax({
      type: 'POST',
      url: WEB_DIR + '/index.php?do=log/get_log',
      data: { ajax: 1, relation_id: data_id, relation: data_target },
      success: function (r) {
        if (r.error) {
          swal.fire(lang_sample.bi_problem, r.error, "error");
        } else {
          $('#logModal').find('.modal-body').html(r.html);
          $('span#log_id').html(r.log_id_html);
          $('#logModal').modal('toggle');
        }
      }
    });
  });
  // alert('UYARI, YAKUP ABARTMA :)');
  initFinanceOzet();
  // Repeat After Me
  // $('#kt_profil_repeater').repeater({
  //   initEmpty: false,
  //   isFirstItemUndeletable: false,
  //   defaultValues: {
  //     'text-input': 'foo'
  //   },

  //   show: function (event) {
  //     $(this).slideDown();
  //     setTimeout(function () {
  //       // bankalar_bilgi_guncelle();
  //     }, 500);
  //   },

  //   hide: function (deleteElement) {
  //     $(this).slideUp(deleteElement);
  //     setTimeout(function () {
  //       // bankalar_bilgi_guncelle();
  //     }, 500);

  //   }
  // });

  // Color Palet
  if ($('.colorpalet').length > 0) {

    $('.colorpalet').colorpicker();
  }
  // KTProfile.init();

  // $('.allowance_view').click(function () {
  //   var dataId = $(this).data('id');

  //   $.ajax({
  //     type: 'POST',
  //     url: WEB_DIR + '/index.php?do=allowance/view',
  //     data: { id: dataId, ajax: 1 },
  //     success: function (r) {
  //       if (r.error) {
  //         swal.fire(lang_sample.bi_problem, r.error, "error");
  //       } else {
  //         $('#allowance_view .modal-body').html(r);
  //         $('#allowance_view').modal('show');
  //         fn_bootstrap_file_input_init();
  //       }
  //     }
  //   });
  // });
  $(document).on('click', '.salert', function () {
    Swal.fire($(this).data('text'));
  });
  $('.copy').click(function (event) {
    event.preventDefault();
    var kopyala = $(this).data('copy');
    $(this).html('<i class="ki-duotone ki-check text-success fs-3"></i>');
    copy(kopyala);
    var self = $(this);
    setTimeout(() => {
      self.html('<i class="ki-duotone ki-copy text-muted fs-3"></i>');
    }, 2000);
  });

  //
  /// CALENDAR
  //
  // ================================================================================================================

  EVOlayoutAddEvent.init('evo_add_event');

  $('#select_calendar_label').change(function (event) {
    if ($(this).val() === 'Project') {
      $('#calendar_project_row').show();
    } else {
      $('#calendar_project_row').hide();
    }
  });

  // $('input#tum_gun').change(function (event) {
  //   $("#input_takvim_bitis_tarihi").prop('disabled', function (_, val) { return !val; });
  //   $("#input_takvim_bitis_tarihi").prop('required', function (_, val) { return !val; });
  // });

  $("form#frm_calendar_add").submit(function (e) {
    e.preventDefault();
    e.stopPropagation();
    var is_valid = $("#frm_calendar_add").valid();
    var _form = $(this);
    if (is_valid) {
      $.ajax({
        type: 'POST',
        url: WEB_DIR + '/index.php',
        data: _form.serialize(),
        success: function (r) {
          if (r.error) {
            swal.fire(lang_sample.bi_problem, r.error, "error");
          } else {
            toastr.success(r.mesaj);
            _form[0].reset();
            _form.trigger("reset");
            _form.find('select').trigger("change");
            $('#tum_gun').prop("checked", false);
            $("#input_takvim_bitis_tarihi").prop('disabled', false);
            $("#input_takvim_bitis_tarihi").prop('required', true);
            $('#evo_add_event_close').trigger('click');

            $('#evo_add_event').removeClass('offcanvas-on');
            $(document.body).removeAttr('data-offcanvas-offcanvas');
            $('div.offcanvas-overlay').remove();

            if (CALENDAR)
              CALENDAR.refetchEvents();

          }
        }
      });
    }
  });
  // ================================================================================================================

  // Check Notification
  // fn_check_notification();

  $('a.notifi_read').click(function (event) {
    let dataId = $(this).data('id');
    $.ajax({
      type: 'POST',
      url: WEB_DIR + '/index.php?do=todos/notification_read',
      data: { ajax: 1, id: dataId },
      success: function (r) {
        if (r.error) {
          swal.fire(lang_sample.bi_problem, r.error, "error");
        } else {
          toastr.success(r.msg);
          $('#notifirow-' + dataId).removeClass('bg-gray-200').addClass('bg-gray-100');
          // Okunmamislari duzenle
          $('#kt_quick_notifications_toggle').find('span.label').first().html(r.count);
        }
      }
    });
  });
  $('a.notifi_delete').click(function (event) {
    let dataId = $(this).data('id');
    $.ajax({
      type: 'POST',
      url: WEB_DIR + '/index.php?do=todos/notification_delete',
      data: { ajax: 1, id: dataId },
      success: function (r) {
        if (r.error) {
          swal.fire(lang_sample.bi_problem, r.error, "error");
        } else {
          toastr.success(r.msg);
          $('#notifirow-' + dataId).remove();
          // Okunmamislari duzenle
          $('#kt_quick_notifications_toggle').find('span.label').first().html(r.count);
        }
      }
    });
  });

  //
  /// TODO LIST SIDEBAR
  //
  // ================================================================================================================
  EVOlayoutAddTodo.init('evo_add_task');
  $('#todo_label').change(function (event) {
    if ($(this).val() === 'Project') {
      $('#todo_project_row').show();
    } else {
      $('#todo_project_row').hide();
    }
  });

  // Check TODO
  // fn_check_todo();
  if ($('table#todo_server_table').length > 0) {
    var lang_url = '//cdn.datatables.net/plug-ins/1.10.20/i18n/Turkish.json';
    // begin first table
    var TABLE_TODO = $('table#todo_server_table').DataTable({
      "order": [[0, "desc"]],
      // "language": {
      //   "url": lang_url
      // },
      "columnDefs": [
        { className: "text-right", "targets": [3] }
      ],
      "createdRow": function (row, data, rowIndex) {
        $(row).attr('id', "todoRow-" + data.DT_RowId);
      },
      processing: true,
      serverSide: false,
      "ajax": WEB_DIR + '/index.php?do=todos/ajax_table&ajax=1&proje_id=' + PROJE_ID
    });

  }

  $('#gecmis_kayitlari_goster').change(function (event) {

    var is_checked = $(this).is(':checked');
    if (is_checked) {
      $('#last_record').val('1');
    } else {
      $('#last_record').val('0');
    }
    fn_todo_loading_start();
  });
  $("form#frm_todo_add_sildim").submit(function (e) {

    e.preventDefault();
    e.stopPropagation();
    var is_valid = $("#frm_todo_add").valid();
    var _form = $(this);
    if (is_valid) {
      $.ajax({
        type: 'POST',
        url: WEB_DIR + '/index.php',
        data: _form.serialize(),
        success: function (r) {
          if (r.error) {
            swal.fire(lang_sample.bi_problem, r.error, "error");
          } else {
            toastr.success(r.mesaj);
            _form[0].reset();
            _form.trigger("reset");
            _form.find('select').trigger("change");
            $('#harita_secimi_row').hide();

            if (TABLE_TODO) {
              TABLE_TODO.ajax.reload();
            }
          }
        }
      });

    }
  });
  $('#check_haritaya_ekle').change(function (event) {
    $('#harita_secimi_row').toggle('fast');
  });
  $(document).on('click', 'a.delete-todo-row', function (event) {
    event.preventDefault();
    var dataId = $(this).data('id');
    var konu = $(this).data('title');
    swal.fire({
      title: lang_sample.are_you_sure,
      text: konu + lang_sample.todo_silmek_istiyorsun + lang_sample.noback,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: lang_sample.yes_sure,
      cancelButtonText: lang_sample.cancel
    }).then(function (result) {
      if (result.value) {
        $.ajax({
          type: 'POST',
          url: WEB_DIR + '/index.php?do=todos/delete',
          data: { dataId: dataId, ajax: 1 },
          success: function (r) {
            if (r.error) {
              swal.fire(lang_sample.bi_problem, r.error, "error");
            } else {
              toastr.success(lang_sample.silindi);
              $('#todoRow-' + dataId).remove();
            }
          }
        });
      }
    });
  });

  //
  /// Kendi icin olusturulan gorevler haric, Check olanlari disable et
  // Onayladigini tekrar uncheck yapamaz.
  // console.log('input.todo_check[data-actionowner!="' + USER_LOGIN + '"]');
  $('input.todo_check[data-actionowner!="' + USER_LOGIN + '"]').each(function () {
    // console.log($(this).data('id'));
    if ($(this).is(':checked')) {
      $(this).prop('disabled', 1);
    }
  });
  //
  // UPDATE
  $(document).on('change', 'input.todo_check', function (event) {
    // event.preventDefault();
    var _id = $(this).data('id');
    var self = $(this);
    // var _actionowner = $(this).data('actionowner');
    var is_checked = $(this).is(':checked');
    var todoCheckRowA = $('.todo-konu-' + _id);
    var input_todo_check = $('.input_todo_check-' + _id);
    todoCheckRowA.removeClass('is_checked is_none_checked');
    // Gorevi olusturen ile gorevli kisi ayni ise
    if (!is_checked) {
      $.ajax({
        type: 'POST',
        url: WEB_DIR + '/index.php?do=todos/update',
        data: { dataId: _id, ajax: 1, is_check: is_checked, yetkiliye_bildirim: 1 },
        success: function (r) {

          if (r.error) {
            swal.fire(lang_sample.bi_problem, r.error, "error");
          } else {

            if (is_checked) {
              todoCheckRowA.addClass('is_checked');
              input_todo_check.prop("checked", 1);

              toastr.success(lang_sample.task_completed);
            } else {
              input_todo_check.prop("checked", 0);
              todoCheckRowA.addClass('is_none_checked');
            }
            if (r.msg) {
              toastr.warning(r.msg);
            }
          }
        }
      });
    } else {
      swal.fire({
        title: lang_sample.are_you_sure,
        text: lang_sample.todo_onay + lang_sample.noback,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: lang_sample.yes_sure,
        cancelButtonText: lang_sample.cancel
      }).then(function (result) {
        if (result.value) {
          $.ajax({
            type: 'POST',
            url: WEB_DIR + '/index.php?do=todos/update',
            data: { dataId: _id, ajax: 1, is_check: is_checked, yetkiliye_bildirim: 1 },
            success: function (r) {

              if (r.error) {
                swal.fire(lang_sample.bi_problem, r.error, "error");
              } else {

                if (is_checked) {
                  todoCheckRowA.addClass('is_checked');
                  input_todo_check.prop("checked", 1);

                  toastr.success(lang_sample.task_completed);
                } else {
                  input_todo_check.prop("checked", 0);
                  todoCheckRowA.addClass('is_none_checked');
                }

                if (r.disable) {
                  self.prop('disabled', 1);
                }
                if (r.msg) {
                  toastr.warning(r.msg);
                }
              }
            }
          });
        } else {
          self.prop('checked', 0);
        }
      });
    }
    // 
    ///
    //



  });


  // ================================================================================================================
  //
  /// END  TODO LIST SIDEBAR
  //

  $('select.selectpicker').select2();
  $('select.select2').select2();

  // datetimepicker
  // $('div.datetimepicker-evo').datetimepicker({
  //   format: 'DD/MM/YYYY',
  //   locale: moment.locale('tr', {
  //     week: { dow: 1 }
  //   })
  // });

  if ($('div.datetimepicker-evo').length > 0) {
    $('div.datetimepicker-evo').each(function (a, b) {
      init_datetimepicker_evo($(this).attr('id'));
    });
  }
  if ($('div.datetimepicker-date-time-evo').length > 0) {
    $('div.datetimepicker-date-time-evo').each(function (a, b) {
      init_datepicker_evo_with_time($(this).attr('id'));
    });
  }



  // $('div.datetimepicker-date-time-evo').datetimepicker({
  //   autoclose: true,
  //   locale: moment.locale('tr', {
  //     week: { dow: 1 }
  //   }),
  // });

  // $('div.datetimepicker-time-evo').datetimepicker({
  //   format: 'LT',
  //   autoclose: true,
  //   locale: moment.locale('tr', {
  //     week: { dow: 1 }
  //   }),
  // });

  // Inputmask({
  //   "mask": "99/99/9999"
  // }).mask("input.data-mask");

  // Inputmask({
  //   "mask": "99:99"
  // }).mask("input.date-mask-time");

  // Currency
  // Inputmask("999.999.999,99", {
  //   "numericInput": true
  // }).mask("input.money");

  // $('input.money').inputmask('#.##0,00', { reverse: true });
  // $('input.money').inputmask({ mask: "99-999-99" });
  $('input.date-mask-time').mask('00/00/0000 00:00', {
    placeholder: "dd/mm/yyyy h:i",
    clearIfNotMatch: true
  });
  $('input.data-mask-time').mask('00:00', {
    placeholder: "hh:mm",
    clearIfNotMatch: true
  });
  $('input.data-mask').mask('00/00/0000', {
    placeholder: "dd/mm/yyyy",
    clearIfNotMatch: true
  });

  $('input.money').mask('#.##0,00', { reverse: true });

  // Multiple
  if ($('#tagify_1').length > 0) {
    var input = document.getElementById('tagify_1'),
      // init Tagify script on the above inputs
      tagify = new Tagify(input, {
        whitelist: [],
        blacklist: [], // <-- passed as an attribute in this demo
      })

    // "remove all tags" button event listener
    document.getElementById('tagify_1_remove').addEventListener('click', tagify.removeAllTags.bind(tagify));
  }


  // QUICK UPDATE
  $(document).on('click', 'a[data-qucikupdate]', function (event) {
    quick_update_field($(this).data('qucikupdate'), $(this).data('quick-id'));
  });

  $(document).on('click', 'input[data-qucikupdate]', function (event) {
    if ($(this).attr('type') == 'radio') {
      // Selam 22 Aralik 2020 14/37
      // blabla 
      let me = $(this).data('td');
      if (me) {
        $('#' + me).removeClass('bg-danger text-white');
      }
      quick_update_field($(this).data('qucikupdate'), $(this).data('quick-id'));
    }
  });

  // 27 Mart 12:39
  // Narven
  // Buruk.
  $(document).on('change', 'select[data-qucikupdate]', function (event) {
    let deger = $(this).val();
    // 12 Subat 2024 @CNMAROFFICE
    // Buruk ama bilmiyorum.
    // console.info($(this).data('qucikupdate'));
    // console.info($(this).data('quick-id'));
    // console.info(deger);
    quick_update_field($(this).data('qucikupdate'), $(this).data('quick-id'), deger);
  });

  $('input:text[data-qucikupdate]').keyup(delay(function (e) {
    var self = $(this);
    quick_update_field(self.data('qucikupdate'), self.data('quick-id'), self.val());

  }, 500));
  $(document).on('change', 'input:text[data-qucikupdate]', function () {
    var self = $(this);
    quick_update_field(self.data('qucikupdate'), self.data('quick-id'), self.val());
  });
  $(document).on('change', 'input:checkbox[data-qucikupdate]', function () {
    var self = $(this);
    var val = '0';
    if (self.prop('checked')){
      val = self.val();
    }
    quick_update_field(self.data('qucikupdate'), self.data('quick-id'), val);
  });


  $('textarea[data-qucikupdate]').keyup(delay(function (e) {
    var self = $(this);
    quick_update_field(self.data('qucikupdate'), self.data('quick-id'), self.val());

  }, 500));
  // END QUICK UPDATE

  // Table Row Delete
  $(document).on('click', '.delete-table-row', function (event) {
    event.preventDefault();

    var url = $(this).attr('href');
    var dataId = $(this).data('id');
    swal.fire({
      title: lang_sample.are_you_sure,
      text: '#' + dataId + lang_sample.silmek_istiyorsun + lang_sample.noback,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: lang_sample.yes_sure,
      cancelButtonText: lang_sample.cancel
    }).then(function (result) {
      if (result.value) {
        $.ajax({
          type: 'POST',
          url: url,
          data: { dataId: dataId, ajax: 1 },
          success: function (r) {
            if (r.error) {
              swal.fire(lang_sample.bi_problem, r.error, "error");
            } else {
              toastr.success(lang_sample.silindi);
              $('#row-' + dataId).remove();
            }
          }
        });
      }
    });

  });

  if ($('table.datatable_table').length > 0) {
    // begin first table
    var orderby = $('table.datatable_table').first().data('asc');
    if (!orderby) { orderby = 'desc'; }

    var table = $('table.datatable_table').DataTable({
      "order": [[0, orderby]],
      dom: 'Qfrtip',
      search: {
        return: true
      },
      "pageLength": 50,
      processing: true,
      serverSide: false
    });
  }
  if ($('table.datatable_table_home').length > 0) {
    // begin first table
    var table = $('table.datatable_table_home').DataTable({
      "order": [[0, "asc"]],
      "pageLength": 50,
      processing: true,
      serverSide: false
    });
  }

  // var avatar1 = new KTImageInput('kt_image');

  // Back Button
  $('.btn-back-button').click(function (event) {
    event.preventDefault();
    // window.history.back();
    var _href = $(this).attr('href');
    if (_href === '#' || _href == null) {
      window.location.href = WEB_DIR + "/index.php?do=" + TARGET + '/manage';
    } else {
      window.location.href = _href;
    }
  });

  $('.btn-back-force-button').click(function (event) {
    event.preventDefault();
    window.history.back();
    // window.location.href = WEB_DIR + "/index.php?do=" + TARGET + '/manage';
  });

  // Button Loading
  $('.btn-evo-loading').click(function (event) {

    $(this).html($(this).html() + '<span class="spinner-border spinner-border-sm align-middle ms-2"></span>');
  });

  // Validate Form
  $('form.valid_form').each(function () {
    evo_validate_init($(this));
  });

  cart_footer_bar();

  $(window).on('resize', function () {
    cart_footer_bar();
  });
  // Alert Options
  toastr.options = {
    "closeButton": true,
    "debug": false,
    "newestOnTop": true,
    "progressBar": false,
    "positionClass": "toast-top-right",
    "preventDuplicates": false,
    "onclick": null,
    "showDuration": "300",
    "hideDuration": "1000",
    "timeOut": "5000",
    "extendedTimeOut": "1000",
    "showEasing": "swing",
    "hideEasing": "linear",
    "showMethod": "fadeIn",
    "hideMethod": "fadeOut"
  };

  fn_bootstrap_file_input_init();

  $('a.btn-delete-file').click(function (event) {
    event.preventDefault();

    let field = $(this).data('field');
    let id = $(this).data('id');
    let _target = $(this).data('target');
    let _value = $(this).data('value');

    $('#image-' + field).attr('src', default_image);

    $(event.target).remove();

    $.ajax({
      type: 'POST',
      url: quick_update_dir,
      data: { actionmode: 'delete_file', _target: _target, _value: _value, field: field, id: id },
      success: function (r) {
        if (r.error) {
          swal.fire(lang_sample.bi_problem, r.error, "error");
        } else {
          toastr.success(lang_sample.silindi);
        }
      }
    });
    // toastr.warning('Bilgilerin kayıt edilmesi için Güncelle butonuna basınız');
  });

});

function cart_footer_bar() {

  // Window resize
  var windowHeight = $(window).height();
  var frmHeight = 0;
  if ($('form.form').length > 0) {
    frmHeight = $('form.form').height() + 160;
  }
  var windowHeight = $(window).height();

  // console.log('windowHeight: '+ windowHeight);
  // console.log('frmHeight: '+ frmHeight);
  // FORM CARD FOOTER HIDE OR SHOW
  if (frmHeight > windowHeight) {
    $('#card-footer').show();
  } else {
    $('#card-footer').hide();
  }
}
function log(msg) {
  console.log(msg);
  toastr.success(msg);
}
function quick_update_field(string, id, reel_value) {
  $.ajax({
    type: 'POST',
    url: quick_update_dir,
    data: { actionmode: 'quick_update', string: string, id: id, reel_value: reel_value },
    success: function (r) {
      if (r.error) {
        swal.fire('Bi Hata Var', r.error, "error");
      }
      if (r.msg){
        toastr.success(r.msg);
      }
    }
  });
}
function delay(callback, ms) {
  var timer = 0;
  return function () {
    var context = this, args = arguments;
    clearTimeout(timer);
    timer = setTimeout(function () {
      callback.apply(context, args);
    }, ms || 0);
  };
}

function fn_check_todo() {

  $.ajax({
    type: 'POST',
    url: WEB_DIR + '/index.php?do=todos/checktodo',
    data: { ajax: 1 },
    success: function (r) {
      if (r.error) {
        swal.fire(lang_sample.bi_problem, r.error, "error");
      } else {
        $('#_sideBar_Todo').html(r.html);
        // Run tooltip
        $('[data-toggle="tooltip"]').each(function () { KTApp.initTooltip($(this)); });
      }

      setTimeout(fn_check_todo, 8000);
    }
  });
}
function fn_check_notification() {
  $.ajax({
    type: 'POST',
    url: WEB_DIR + '/index.php?do=todos/checknotification',
    data: { ajax: 1, last: LAST_PAGE_LOAD },
    success: function (r) {
      if (r.error) {
        swal.fire(lang_sample.bi_problem, r.error, "error");
      } else {
        if (r.notifications) {

          LAST_PAGE_LOAD = r.now;

          // var notifiCounter = $('#kt_quick_notifications_toggle').find('span.label').first();
          // var varOlanToplamNotifi = parseInt(notifiCounter.html()) + r.notifications_count;
          // notifiCounter.html( varOlanToplamNotifi );

          $.each(r.notifications, function (key, value) {
            toastr.warning(value.mesaj);
          });
        }
      }

      setTimeout(fn_check_notification, 10000);
    }
  });
}
function evo_validate_init(eLement) {

  eLement.validate({
    ignore: ":hidden:not(.validate)",
    errorClass: 'is-invalid',
    validClass: 'is-valid',
    errorPlacement: function (error, element) {
      var group = element.closest('.input-group');
      if (group.length) {
        group.after(error.addClass('invalid-feedback'));
      } else {

        if (element.attr("type") == 'radio') {
          element.parent().parent().append(error.addClass('invalid-feedback'));
        } else {
          element.after(error.addClass('invalid-feedback'));
        }
      }
    },

    //display error alert on form submit
    invalidHandler: function (event, validator) {
      var currenFrom = $(validator.currentForm);
      var btn_loading = currenFrom.find('.btn-evo-loading') ;
      if (btn_loading.length) {
        var tekrar = btn_loading.data('defaul-text');
        if (!tekrar) tekrar = 'Save';
        btn_loading.html(tekrar).prop('disabled', false);;
        btn_loading.removeClass('spinner spinner-white spinner-left');
      }
    },

    submitHandler: function (form) {

      var validSubmit = $(form).attr('data-validSubmit');
      if (validSubmit != 'true')
        form.submit();
      // form[0].submit(); // submit the form
    }
  });
}
function copy(text) {

  var $temp = $("<input>");
  $("body").append($temp);
  $temp.val(text).select();
  document.execCommand("copy");
  $temp.remove();

  try {
    var successful = document.execCommand('copy')
    var msg = successful ? 'successfully' : 'unsuccessfully'
    toastr.success('Copied ');
  } catch (err) {
    toastr.error(lang_sample.kopyalanamadi);
  }
}
function fn_bootstrap_file_input_init() {
  // Bootstrap File Input - Label
  var btnInput = $('label.btn-file-input');
  if (btnInput.length > 0) {
    btnInput.find('input').first().change(function (event) {

      var fileName = $(this).val().split('\\').pop();
      btnInput.find('span').first().addClass('font-weight-bolder').html('Dosya: ' + fileName);
    });
  }
}
var initFinanceOzet = function () {
  var element = document.getElementById("kt_mixed_widget_13_chart");
  var height = parseInt(KTUtil.css(element, 'height'));

  if (!element) {
    return;
  }

  var options = {
    series: [{
      name: 'Günlük: ',
      data: OZET_AVANS_ADET
    }],
    chart: {
      type: 'area',
      height: height,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      },
      sparkline: {
        enabled: true
      }
    },
    plotOptions: {},
    legend: {
      show: false
    },
    dataLabels: {
      enabled: false
    },
    fill: {
      type: 'solid',
      opacity: 1
    },
    stroke: {
      curve: 'smooth',
      show: true,
      width: 3,
      colors: [KTApp.getSettings()['colors']['theme']['base']['info']]
    },
    xaxis: {
      categories: OZET_GUNLER,
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false
      },
      labels: {
        show: false,
        style: {
          colors: KTApp.getSettings()['colors']['gray']['gray-500'],
          fontSize: '12px',
          fontFamily: KTApp.getSettings()['font-family']
        }
      },
      crosshairs: {
        show: false,
        position: 'front',
        stroke: {
          color: KTApp.getSettings()['colors']['gray']['gray-300'],
          width: 1,
          dashArray: 3
        }
      },
      tooltip: {
        enabled: true,
        formatter: undefined,
        offsetY: 0,
        style: {
          fontSize: '12px',
          fontFamily: KTApp.getSettings()['font-family']
        }
      }
    },
    yaxis: {
      min: 0,
      max: 60,
      labels: {
        show: false,
        style: {
          colors: KTApp.getSettings()['colors']['gray']['gray-500'],
          fontSize: '12px',
          fontFamily: KTApp.getSettings()['font-family']
        }
      }
    },
    states: {
      normal: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      hover: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      active: {
        allowMultipleDataPointsSelection: false,
        filter: {
          type: 'none',
          value: 0
        }
      }
    },
    tooltip: {
      style: {
        fontSize: '12px',
        fontFamily: KTApp.getSettings()['font-family']
      },
      y: {
        formatter: function (val) {
          return val + " adet"
        }
      }
    },
    colors: [KTApp.getSettings()['colors']['theme']['light']['info']],
    markers: {
      colors: [KTApp.getSettings()['colors']['theme']['light']['info']],
      strokeColor: [KTApp.getSettings()['colors']['theme']['base']['info']],
      strokeWidth: 3
    }
  };

  var chart = new ApexCharts(element, options);
  chart.render();
}
function init_datetimepicker_evo(selector) {
  new tempusDominus.TempusDominus(document.getElementById(selector), {
    display: {
      viewMode: "calendar",
      components: {
        decades: true,
        year: true,
        month: true,
        date: true,
        hours: false,
        minutes: false,
        seconds: false
      }
    },
    localization: {
      locale: "en",
      startOfTheWeek: 1,
      format: "dd/MM/yyyy"
    }
  });
}
function init_datepicker_evo_with_time(selector) {
  new tempusDominus.TempusDominus(document.getElementById(selector), {
    display: {
      viewMode: "calendar",
      components: {
        decades: false,
        year: true,
        month: true,
        date: true,
        hours: true,
        minutes: true,
        seconds: false
      }
    },
    localization: {
      locale: "en-GB",
      startOfTheWeek: 1,
      format: "dd/MM/yyyy HH:mm"
    }
  });
}
// DEmet Hanim Istek
function system_show_down(){
  if (USER_LOGIN == 0) {
    return false;
  }
  $.ajax({
    type: 'POST',
    url: WEB_DIR + '/index.php?do=system_show_down/get',
    data: { ajax: 1, user_id: USER_LOGIN },
    success: function (r) {
      if (r.redirect == '1') {
        location.reload();
      } 

      setTimeout(system_show_down, 10000);
    }
  });
}