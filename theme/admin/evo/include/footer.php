<div id="kt_app_footer" class="app-footer w-100">
	<!--begin::Footer container-->
	<div class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
		<!--begin::Copyright-->
		<div class="text-dark order-2 order-md-1">
			<span class="text-muted fw-semibold me-1">2021 - <?php echo date('Y'); ?>&copy;</span>
			<a href="https://cnmarship.de/" target="_blank" class="text-gray-800 text-hover-primary">CNMAR PORTAL</a>
		</div>
		<!--end::Copyright-->
		<!--begin::Menu-->
		<ul class="menu menu-gray-600 menu-hover-primary fw-semibold order-1">

			<li class="menu-item">
				<a href="https://evrimbakir.com" target="_blank" class="menu-link px-2 text-gray-800">Design & Coding: Ev<PERSON> BAKIR</a>
			</li>
		</ul>
		<!--end::Menu-->
	</div>
	<!--end::Footer container-->
</div>
<!--begin::Modal - Set Score-->
<div class="modal fade" id="evo_performance_set_score" tabindex="-1" aria-hidden="true">
	<!--begin::Modal dialog-->
	<div class="modal-dialog modal-dialog-centered mw-1000px">
		<!--begin::Modal content-->
		<div class="modal-content rounded">
			<!--begin::Modal header-->
			<div class="modal-header pb-0 border-0 justify-content-end">
				<!--begin::Close-->
				<div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
					<i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
				</div>
				<!--end::Close-->
			</div>
			<!--begin::Modal header-->

			<!--begin::Modal body-->
			<div class="modal-body  px-10 px-lg-15 pt-0 pb-15">
				<!--begin:Form-->
				<form class="form valid_form" action="" method="post" enctype="multipart/form-data">
					<input type="hidden" name="do" value="performance/add_score">
					<input type="hidden" name="ajax" value="1">
					<input type="hidden" name="score_id" value="0" id="score_id">
					<!--begin::Heading-->
					<div class="mb-13 text-center">
						<!--begin::Title-->
						<h1 class="mb-0">Performans Puanlaması</h1>
					</div>
					<!--end::Heading-->

					<!--begin::Input group-->
					<div class="d-flex flex-column mb-8 fv-row">
						<!--begin::Label-->
						<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
							<span class="required">Personel Seçiniz</span>
						</label>
						<!--end::Label-->
						<?php
						$db->query("SET NAMES 'utf8'");
						$employes = get_results('users', "user_role<>'Director' AND is_delete=0 order by name asc", 'name,lastname,user_id,avatar,roles');
						?>
						<select class="form-select form-select-solid" data-allow-clear="true" data-dropdown-parent="#evo_performance_set_score" id="performance_user_id" data-control="select2" data-placeholder="<?php echo _('Personel Listesi'); ?>" name="employe[]" multiple required>
							<option></option>
							<option value="all_employee">Tüm Personel</option>
							<?php foreach ($employes as $k => $v) :
								if ($v->user_id == USER_LOGIN) continue;
								$roles = $role_names = array();
								if ($v->roles)
									$roles = explode(',', str_replace('#', '', $v->roles));
								foreach ($roles as $ab => $c) {
									if ($c)
										$role_names[] = get_var('category', 'name', "id=$c");
								}
							?>
								<option value="<?php echo $v->user_id; ?>" data-kt-rich-content-subcontent="<?php echo implode(',', $role_names); ?>" data-kt-rich-content-icon="<?php echo  $img_src = T . $employee_web_images . $v->avatar . '&h=300';; ?>"><?php echo $v->name; ?> <?php echo $v->lastname; ?></option>
							<?php endforeach; ?>
						</select>
					</div>
					<div class="d-flex flex-column mb-8 fv-row">
						<!--begin::Label-->
						<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
							<span class="required">Departman Seçiniz</span>
							<span class="ms-1" data-bs-toggle="tooltip" title="Oylayabileceğiniz tüm departmanların listesi">
								<i class="ki-duotone ki-information-5 text-gray-500 fs-6"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i></span>
						</label>
						<!--end::Label-->
						<?php
						$category = get_results('category', "object_type='performance_departman' AND is_delete=0 AND autoload='yes'");
						?>
						<select class="form-select form-select-solid" data-allow-clear="true" data-dropdown-parent="#evo_performance_set_score" id="performance_category" data-control="select2" data-placeholder="<?php echo _('Departman listesi'); ?>" name="kirilim_id" required>
							<option></option>
							<?php foreach ($category as $k => $v) :
								$kirilim = get_results('performance_kirilim', "is_delete=0 AND category_id=$v->id");
							?>
								<optgroup label="<?php echo $v->name; ?>">
									<?php foreach ($kirilim as $kir => $kiri) : ?>
										<option value="<?php echo $kiri->id; ?>"><?php echo $kiri->name; ?></option>
									<?php endforeach; ?>
								</optgroup>
							<?php endforeach; ?>
						</select>
					</div>
					<div class="d-flex flex-column mb-8 fv-row">
						<!--begin::Label-->
						<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
							<span class="">Proje Seçiniz</span>
						</label>
						<!--end::Label-->
						<?php
						$projeler =  $db->get_results("SELECT id FROM projects WHERE  is_delete=0 order by id desc");
						?>
						<select name="performance[project_id]" id="performance_project_id" data-dropdown-parent="#evo_performance_set_score" class="form-select" data-control="select2" data-placeholder="<?php echo _('Proje'); ?> <?php echo _('Seçiniz'); ?>">
							<option></option> <?php
												foreach ($projeler as $k => $v) :
													$proje_adi = get_project_name($v->id);
												?>
								<option <?php echo ($v->id == $cari_payment->project_id) ? 'selected="selected"' : ''; ?> value="<?php echo $v->id; ?>"><?php echo get_project_name($v->id); ?></option>
							<?php endforeach; ?>
						</select>
					</div>
					<!--end::Input group-->
					<div class="row">
						<div class="col-md-4">
							<div class="mb-8 fv-row">
								<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
									<span class="required"><?php echo _('Gerçekleşme Tarihi'); ?>:</span>
								</label>
								<?php fn_datepicker_time_input('puan_degerlendirme_tarihi', true); ?>
								<div class="fs-7 fw-semibold text-muted">Olayın yaşandığı tarih</div>
							</div>
						</div>
						<div class="col-md-8">
							<div class="fv-row mb-8">
								<!--begin::Heading-->
								<div class="mb-2">
									<!--begin::Label-->
									<label class="d-flex align-items-center fs-5 fw-semibold">
										<span class="required">Değerlendirme</span>
									</label>
								</div>
								<!--end::Heading-->

								<!--begin::Radio group-->
								<div class="btn-group w-100" id="performance_score_button" data-kt-buttons="true" data-kt-buttons-target="[data-kt-button]">
									<!--begin::Radio-->
									<label class="btn btn-outline btn-color-muted btn-active-danger" data-kt-button="true" data-bs-toggle="tooltip" data-bs-custom-class="tooltip-inverse" data-bs-placement="top" title="Berbat">
										<!--begin::Input-->
										<input class="btn-check" type="radio" name="score" value="1" required />
										<!--end::Input-->
										<span class="position-relative">
											<i class="ki-duotone ki-dislike fs-2"><span class="path1"></span><span class="path2"></span></i>
											<i class="ki-duotone ki-dislike fs-2" style="position: absolute;left: -9px;top: 1px;"><span class="path1"></span><span class="path2"></span></i>
										</span>
									</label>
									<label class="btn btn-outline btn-color-muted btn-active-warning" data-kt-button="true" data-bs-toggle="tooltip" data-bs-custom-class="tooltip-inverse" data-bs-placement="top" title="Kötü">
										<!--begin::Input-->
										<input class="btn-check" type="radio" name="score" value="2" required />
										<!--end::Input-->
										<i class="ki-duotone ki-dislike fs-2"><span class="path1"></span><span class="path2"></span></i>
									</label>
									<!--end::Radio-->
									<!--begin::Radio-->
									<label class="btn btn-outline btn-color-muted btn-active-success" data-kt-button="true" data-bs-toggle="tooltip" data-bs-custom-class="tooltip-inverse" data-bs-placement="top" title="Beğendim">
										<!--begin::Input-->
										<input class="btn-check" type="radio" name="score" value="3" required />
										<!--end::Input-->
										<i class="ki-duotone ki-like fs-2"><span class="path1"></span><span class="path2"></span></i>
									</label>
									<!--end::Radio-->
									<!--begin::Radio-->
									<label class="btn btn-outline btn-color-muted btn-active-primary" required data-kt-button="true" data-bs-toggle="tooltip" data-bs-custom-class="tooltip-inverse" data-bs-placement="top" title="Bayıldım">
										<!--begin::Input-->
										<input class="btn-check" type="radio" name="score" value="4" />
										<!--end::Input-->
										<span class="position-relative">
											<i class="ki-duotone ki-like fs-2"><span class="path1"></span><span class="path2"></span></i>
											<i class="ki-duotone ki-like fs-2 " style="position: absolute;left: 11px;top: -6px;"><span class="path1"></span><span class="path2"></span></i>
										</span>
									</label>
									<!--end::Radio-->
								</div>
								<!--end::Radio group-->
							</div>
						</div>
					</div>

					<!--begin::Input group-->
					<div class="d-flex flex-column mb-8">
						<label class="fs-6 fw-semibold mb-2">Açıklama</label>
						<textarea class="form-control form-control-solid" rows="2" name="performance[description]" required placeholder=""></textarea>
					</div>
					<div class="d-flex flex-column mb-8">
						<label class="fs-6 fw-semibold mb-2" for="formFile">Medya <span id="medya_files"></span></label>
						<input class="form-control" name="media" type="file" id="formFile">
					</div>
					<!--end::Input group-->
					<!--begin::Actions-->
					<div class="text-end">
						<button type="submit" class="btn btn-primary s_confirm_performance" data-text="Are you sure?">Kaydet</button>
					</div>
					<!--end::Actions-->
				</form>
				<!--end:Form-->
			</div>
			<!--end::Modal body-->
		</div>
		<!--end::Modal content-->
	</div>
	<!--end::Modal dialog-->
</div>
<!--end::Modal - Set Score-->