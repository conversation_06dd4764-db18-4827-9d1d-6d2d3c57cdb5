<?php if ($target == 'performance' && $confetti == '1') : ?>
	<div id="canvas-container-div">
		<canvas id="canvas"></canvas>
	</div>
	<div id="restart-button-div" class="d-flex flex-center">
		<div class="bg-white p-2"><img id="restart-icon" src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/409445/restart.svg"></div>
		<button class="btn btn-icon btn-light ms-3" id="close_confetti"><i class="fa-solid fa-circle-xmark fs-4 "></i></button>
	</div>
<?php endif; ?>
<div class="modal fade" id="logModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-xl" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title"><?php echo _('Log: Son 10 Kayıt'); ?> <span id="log_id"><small class="text-muted">(<?php echo ucfirst($relation); ?>: #<?php echo $relation_id; ?>)</small></span>
				</h5>
				<!--begin::Close-->
				<div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
					<i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
				</div>
				<!--end::Close-->
			</div>
			<div class="modal-body">
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-light-warning font-weight-bold" data-bs-dismiss="modal"><?php echo _('Kapat'); ?></button>
			</div>
		</div>
	</div>
</div>
<!--begin::Scrolltop-->
<div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
	<i class="ki-duotone ki-arrow-up">
		<span class="path1"></span>
		<span class="path2"></span>
	</i>
</div>
<script>
	var hostUrl = "<?php echo $s_w_tema; ?>/assets/";
</script>

<!--begin::Global Javascript Bundle(mandatory for all pages)-->
<script src="<?php echo $s_w_tema ?>/assets/plugins/global/plugins.bundle.js"></script>
<script src="<?php echo $s_w_tema ?>/assets/js/scripts.bundle.js"></script>
<script src="<?php echo $s_w_tema ?>/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js"></script>
<script src="<?php echo $s_w_tema ?>/assets/plugins/custom/formrepeater/formrepeater.bundle.js"></script>
<script type="text/javascript" src="<?php echo WEB_LIB_DIR ?>/editor/ckeditor/ckeditor.js"></script>
<script>
	CKEDITOR.disableAutoInline = true;
	CKEDITOR.env.isCompatible = true;
</script>
<script>
	var quick_update_dir = '<?php echo WEB_DIR ?>/quick-update.php';
	var default_image = '<?php echo COMMON_W_DIR . '/assets/img/blank-img.jpg' ?>';
	var WEB_DIR = '<?php echo WEB_DIR ?>';
	var APP_INIT = '<?php echo APP_INIT ?>';
	var INDEX_SCRIPT = '<?php echo INDEX_SCRIPT ?>';
	var AJAX_DIR = '<?php echo WEB_DIR ?>/ajax.php';
	var TARGET = '<?php echo $target ?>';
	var MODE = '<?php echo $mode ?>';
	var LAST_PAGE_LOAD = '<?php echo time() ?>'; /*En son sayfa yükleme zamanı*/
	var USER_LOGIN = '<?php echo USER_LOGIN; ?>';
	var TIMEZONE = '<?php echo get_selected_timezone(); ?>';
	<?php
	if ($target == 'projects' &&  $_GET['id']) {
		$_id = $_GET['id'];
	}
	if (isset($_GET['proje_id'])) {
		$_id = $_GET['proje_id'];
	}
	?>
	var PROJE_ID = '<?php echo $_id; ?>';
	var INVOICE_ID = '<?php echo ($invoice_id) ? $invoice_id : 0; ?>';
	var OFFER_ID = '<?php echo ($offer_id) ? $offer_id : 0; ?>';
	var DRAFT_OPERATION_ACTION = 17;
	var GLOBAL_GET_ID = '<?php echo $_GET['id'] ?>';
	var PERMISSION_TARGET = <?php echo json_encode($PERMISSION_TARGET); ?>;
	var lang_sample = {
		task_completed: '<?php echo _('Görev tamamlandı'); ?> 🥳',
		are_you_sure: '<?php echo _('Are you sure?'); ?>',
		yes_sure: '<?php echo _('Yes, sure!'); ?>',
		cancel: '<?php echo _('Cancel'); ?>',
		noback: '<?php echo _('This action cannot be undone!'); ?>',
		bi_problem: '<?php echo _('Bi hata ile karşılaşıldı'); ?>',
		silindi: '<?php echo _('Başarıyla silindi.'); ?>',
		silmek_istiyorsun: ' <?php echo _('numaralı satırı silmek istiyorsunuz.'); ?>',
		todo_silmek_istiyorsun: ' <?php echo _('konulu görevi silmek istiyorsunuz.'); ?>',
		todo_onay: '<?php echo _('Bu görevi yaptığını onaylıyorsun. Görev sahibine bilgi gönderilecektir.'); ?>',
		kopyalandi: '<?php echo _('Panoya kopyalandi.'); ?>',
		kopyalanamadi: '<?php echo _('Panoya kopyalanamadi!'); ?>',
		operasyon_mail_title: '<?php echo _('The client will be informed about the selected services.'); ?>',
		onayla: '<?php echo _('Onayla'); ?>',
		aciklama: '<?php echo _('Açıklama'); ?>',
		guncelle: '<?php echo _('Güncelle'); ?>',
		calendar_silmek: ' <?php echo _('numaralı etkinliği silmek istiyorsunuz.'); ?>',
		loading: '<?php echo _('Lütfen bekleyiniz...'); ?>'
	}
</script>

<?php if ($target == 'index' or $target == 'opr_dashboard' or $target == 'finance_dashboard') : ?>
	<script src="<?php echo COMMON_W_DIR; ?>/assets/script/dashboard.js?v=<?php echo VERSION ?>"></script>
<?php endif; ?>
<?php if ($target == 'calendar' or $target == 'index'   or $target == 'opr_dashboard' or $target == 'finance_dashboard' or $target == 'calendar_list') : ?>
	<script src="<?php echo W_ADDONS_DIR; ?>/calendar/js/calendar.js?v=<?php echo VERSION ?>"></script>
<?php endif; ?>
<?php if ($target == 'mailbox'): ?>
	<script src="<?php echo $s_w_tema; ?>/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>
<?php endif; ?>
<script src="<?php echo $s_w_tema ?>/assets/plugins/custom/datatables/datatables.bundle.js"></script>
<?php if ($target == 'profile') : ?>
	<script src="<?php echo W_ADDONS_DIR; ?>/employee/js/employee.js?v=<?php echo VERSION ?>"></script>
<?php endif; ?>
<?php if ($target == 'customers' or $target == 'invoices' or $target == 'offer') : ?>
	<script src="<?php echo COMMON_W_DIR; ?>/assets/script/Sortable.min.js"></script>
	<script src="<?php echo COMMON_W_DIR ?>/assets/script/jquery.validate.min.js"></script>
<?php endif; ?>
<script script src="<?php echo COMMON_W_DIR ?>/assets/script/jquery.validate.min.js"></script>
<?php if (file_exists(ADDONS_DIR . "/" . $target . "/js/$target.js") && $target != 'calendar') : ?>
	<script src="<?php echo W_ADDONS_DIR; ?>/<?php echo $target ?>/js/<?php echo $target ?>.js?v=<?php echo VERSION ?>">
	</script>
<?php endif; ?>
<?php if ($target == 'projects' && $mode == 'view') : ?>
	<script src="<?php echo COMMON_W_DIR; ?>/assets/script/Sortable.min.js"></script>
	<script src="<?php echo W_ADDONS_DIR; ?>/<?php echo $target ?>/js/project_view.js?v=<?php echo VERSION ?>"></script>
	<script src="https://unpkg.com/bootstrap-datepicker@1.9.0/dist/js/bootstrap-datepicker.min.js"></script>
	<?php if (!APP_INIT): ?>
		<script src="<?php echo W_ADDONS_DIR; ?>/mail/js/mail.js" type="text/javascript"></script>
		<script src="<?php echo W_ADDONS_DIR; ?>/projects/js/attendance_galeri.js?v=<?php echo VERSION ?>" type="text/javascript"></script>
		<script src="<?php echo W_ADDONS_DIR; ?>/calendar/js/calendar.js?v=<?php echo VERSION ?>"></script>
		<script src="<?php echo W_ADDONS_DIR; ?>/projects/js/murettebat_galeri.js?v=<?php echo VERSION ?>" type="text/javascript"></script>
		<script src="<?php echo W_ADDONS_DIR; ?>/projects/js/projects_galeri.js?v=<?php echo VERSION ?>" type="text/javascript"></script>
	<?php endif; ?>
<?php endif; ?>
<?php if ($target == 'calendar' && ($mode == 'add' or $mode == 'add2' or $mode == 'update')) : ?>
	<script src="<?php echo $s_w_tema ?>/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js?v=7.2.9"></script>
<?php endif; ?>
<?php if ($target == 'mail') : ?>
	<script src="<?php echo $s_w_tema ?>/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>
	<?php if ($mode == 'draft' && $id) : ?>
		<!-- <script src="<?php echo $s_w_tema ?>/assets/plugins/custom/uppy/uppy.bundle.js"></script> -->
		<script>
			var image_upload_dir = "<?php echo WEB_DIR; ?>/index.php?do=mail/draft&action=upload_doc";
		</script>
		<script src="<?php echo W_ADDONS_DIR; ?>/mail/js/mailsablon_upload.js" type="text/javascript"></script>
	<?php endif; ?>
	<?php if ($mode == 'send') : ?>
		<script>
			var image_upload_dir = "<?php echo WEB_DIR; ?>/index.php?do=mail/send&action=upload_doc";
		</script>
	<?php endif; ?>
<?php endif; ?>
<?php if ($target == 'faturalar' or $target == 'giderler' && $id) : ?>
	<!-- <script src="<?php echo $s_w_tema ?>/assets/plugins/custom/uppy/uppy.bundle.js"></script> -->
<?php endif; ?>
<?php if ($mode == 'avans_kapama' && $id) : ?>
	<!-- <script src="<?php echo $s_w_tema ?>/assets/plugins/custom/uppy/uppy.bundle.js"></script> -->
	<script>
		var image_upload_dir = "<?php echo WEB_DIR; ?>/index.php?do=allowance/avans_kapama&action=upload_doc";
	</script>
	<script src="<?php echo W_ADDONS_DIR; ?>/allowance/js/file_upload.js" type="text/javascript"></script>
<?php endif; ?>
<?php if ($target == 'avans_talebi' && $mode == 'view') : ?>
	<!-- <script src="<?php echo $s_w_tema ?>/assets/plugins/custom/uppy/uppy.bundle.js"></script> -->
	<script src="<?php echo W_ADDONS_DIR; ?>/avans_talebi/js/file_upload.js" type="text/javascript"></script>
<?php endif; ?>
<?php if (file_exists(COMMON_DIR . "/assets/script/evocommon.js")) : ?>
	<script src="<?php echo (COMMON_W_DIR . "/assets/script/evocommon.js?v=" . VERSION) ?>"></script>
<?php endif; ?>
<script src="<?php echo (COMMON_W_DIR . "/assets/script/datatableinit.js?v=" . VERSION) ?>"></script>
<?php if ($target == 'performance' && ($mode == 'view' or  $mode == 'employee')) : ?>
	<script src="https://cdn.amcharts.com/lib/5/index.js"></script>
	<script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
	<script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
	<script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
	<script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
	<script src="<?php echo $s_w_tema ?>/assets/plugins/custom/fslightbox/fslightbox.bundle.js"></script>
	<script src="<?php echo W_ADDONS_DIR; ?>/<?php echo $target ?>/js/performance_chart.js?v=<?php echo VERSION ?>"></script>
	<script src="<?php echo W_ADDONS_DIR; ?>/calendar/js/calendar.js?v=<?php echo VERSION ?>"></script>
<?php endif; ?>
<?php if ($target == 'performance' && ($mode == 'projects')) : ?>
	<script src="<?php echo W_ADDONS_DIR; ?>/<?php echo $target ?>/js/projects_chart.js?v=<?php echo VERSION ?>"></script>
	<?php if ($confetti == 1) : ?>
		<script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/409445/jquery-3.4.1.min.js"></script>
		<script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/409445/verletExpress.js"></script>
		<script src="<?php echo W_ADDONS_DIR; ?>/<?php echo $target ?>/js/confetti.js?v=<?php echo VERSION ?>"></script>
	<?php endif; ?>
<?php endif; ?>
<script src="<?php echo (COMMON_W_DIR . "/assets/script/jquery.mask.js") ?>"></script>
<?php if ($target == 'allowance' && $mode == 'view') : ?>
	<?php if ($_SESSION['admin']['user_type'] == 'Admin') : ?>
		<script src="<?php echo W_ADDONS_DIR; ?>/admin_allowance/js/admin_allowance.js?v=<?php echo VERSION ?>"></script>
	<?php endif; ?>
	<?php if ($_SESSION['admin']['user_type'] == 'Finance') : ?>
		<script src="<?php echo W_ADDONS_DIR; ?>/finance_allowance/js/finance_allowance.js?v=<?php echo VERSION ?>"></script>
	<?php endif; ?>
<?php endif; ?>
<script script type="text/javascript">
	<?php
	if (isset($_SESSION['message']['S']))
		foreach ($_SESSION['message']['S'] as $k => $mesaj) :  ?>
		toastr.success('<?php echo $mesaj; ?>');
		<?php unset($_SESSION['message']['S'][$k]);  ?>
	<?php endforeach ?>
	<?php
	if (isset($_SESSION['message']['E']))
		foreach ($_SESSION['message']['E'] as $k => $mesaj) :  ?>
		toastr.error('<?php echo $mesaj; ?>');
		<?php unset($_SESSION['message']['E'][$k]);  ?>
	<?php endforeach ?>
	<?php
	if (isset($_SESSION['message']['W']))
		foreach ($_SESSION['message']['W'] as $k => $mesaj) :  ?>
		toastr.warning('<?php echo $mesaj; ?>');
		<?php unset($_SESSION['message']['W'][$k]);  ?>
	<?php endforeach ?>
</script>
<?php if ($target == 'petition' && $form && file_exists(ADDONS_DIR . "/petition/admin/c_forms/$form/script.js")) : ?>
	<script src="<?php echo W_ADDONS_DIR; ?>/petition/admin/c_forms/<?php echo $form ?>/script.js?v=<?php echo VERSION ?>"></script>
<?php endif; ?>
<script>
	<?php if ($do == 'index' or $target == 'opr_dashboard' or $target == 'finance_dashboard') : ?>
		setTimeout(function() {
			window.location.reload(1);
		}, 36000 * 5);
		var firstHour = new Date().getHours() - 8;
		var lastHour = new Date().getHours() + 8 + ':30:00';

	<?php else : ?>
		var firstHour = '00';
		var lastHour = 23 + ':59:59';
	<?php endif; ?>
</script>