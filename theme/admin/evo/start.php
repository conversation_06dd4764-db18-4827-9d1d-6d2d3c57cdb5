<?php
// Kullanici var olan sifresini degistirmisse, logout yapsin.
$_user = get_row('users', "user_id=" . USER_LOGIN, 'pass,sidebar_minimize');
if ($_SESSION['admin']['pass'] != $_user->pass) {
	unset($_SESSION['admin']['user_id']);
	unset($_SESSION['admin']['first_last_name']);
	unset($_SESSION['admin']);
	unset($_SESSION['user_login']);
	fn_redirect(WEB_DIR . '/index.php?cikis=1');
}
include 'include/head.php';

?>

<body id="kt_app_body" data-kt-app-layout="light-sidebar" data-kt-app-header-fixed="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" data-kt-app-toolbar-fixed="<?php echo ((($target == 'performance' or $target == 'projects') && ($mode == 'view' or $mode == 'employee' or $mode == 'projects') or  $target == 'mailbox')) ? 'true' : 'false'; ?>" class="app-default" <?php echo ($_user->sidebar_minimize == '1') ? 'data-kt-app-sidebar-minimize="on"' : ''; ?>>
	<!--begin::Theme mode setup on page load data-kt-app-sidebar-minimize="on"-->
	<script>
		var defaultThemeMode = "light";
		var themeMode;
		if (document.documentElement) {
			if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
				themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
			} else {
				if (localStorage.getItem("data-bs-theme") !== null) {
					themeMode = localStorage.getItem("data-bs-theme");
				} else {
					themeMode = defaultThemeMode;
				}
			}
			if (themeMode === "system") {
				themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
			}
			document.documentElement.setAttribute("data-bs-theme", themeMode);
		}
	</script>
	<!--end::Theme mode setup on page load-->
	<!--begin::App-->
	<div class="d-flex flex-column flex-root app-root" id="kt_app_root">
		<!--begin::Page-->
		<div class="app-page flex-column flex-column-fluid" id="kt_app_page">
			<!--begin::Header-->
			<?php include 'include/header.php'; ?>
			<!--end::Header-->
			<!--begin::Wrapper-->
			<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
				<!--begin::Sidebar-->
				<?php
				// if (USER_LOGIN == '2')
				include 'include/sidebar_evo.php';
				?>
				<!--end::Sidebar-->
				<!--begin::Main-->
				<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
					<!--begin::Content wrapper-->
					<div class="d-flex flex-column flex-column-fluid">
						<!--begin::Toolbar-->
						<?php include 'include/toolbar.php'; ?>
						<!--end::Toolbar-->
						<!--begin::Content-->
						<div id="kt_app_content" class="app-content flex-column-fluid">
							<!--begin::Content container-->
							<div id="kt_app_content_container" class="px-6 acontainer-fluid">
								<div class="mt-0" id="content_<?php echo $target; ?>">
									<?php
									$inc_target = fn_target_inc_set();
									include($inc_target);
									?>
								</div>
							</div>
							<!--end::Content container-->
						</div>
						<!--end::Content-->
					</div>
					<!--end::Content wrapper-->
					<!--begin::Footer-->
					<?php include 'include/footer.php'; ?>
					<!--end::Footer-->
				</div>
				<!--end:::Main-->
			</div>
			<!--end::Wrapper-->
		</div>
		<!--end::Page-->
	</div>
	<?php include 'include/footer_script.php'; ?>
</body>
<!--end::Body-->

</html>